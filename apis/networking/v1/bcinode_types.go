/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// NOTE: json tags are required.  Any new fields you add must have json tags for the fields to be serialized.

// BciNodeSpec defines the desired state of BciNode
type BciNodeSpec struct {
	// InstanceID is the unique instance id of this node
	InstanceID string `json:"instanceId,omitempty"`

	// InstanceType is the type of node, bcc/bbc etc.
	InstanceType string `json:"instanceType,omitempty"`

	// EniMultiIP is eni with multi ip mode
	//
	// +kubebuilder:validation:Optional
	EniMultiIP EniMultiIPSpec `json:"eniMultiIP,omitempty"`
}

type EniMultiIPSpec struct {
	// Pool is the list of IPs available to the node for allocation. When
	// an IP is used, the IP will remain on this list but will be added to
	// Status.EniMultiIP.Used
	//
	// +optional
	Pool AllocationMap `json:"pool,omitempty"`

	// MinAllocate is the minimum number of IPs that must be allocated when
	// the node is first bootstrapped. It defines the minimum base socket
	// of addresses that must be available. After reaching this watermark,
	// the PreAllocate and MaxAboveWatermark logic takes over to continue
	// allocating IPs.
	// +kubebuilder:validation:Minimum=0
	MinAllocate int `json:"minAllocate,omitempty"`

	// MaxAllocate is the maximum number of IPs that can be allocated to the
	// node. When the current amount of allocated IPs will approach this value,
	// the considered value for PreAllocate will decrease down to 0 in order to
	// not attempt to allocate more addresses than defined.
	//
	// +kubebuilder:validation:Minimum=0
	// +kubebuilder:validation:Required
	MaxAllocate int `json:"maxAllocate,omitempty"`

	// PreAllocate defines the number of IP addresses that must be
	// available for allocation in the IPAMspec. It defines the buffer of
	// addresses available immediately without requiring cilium-operator to
	// get involved.
	//
	// +kubebuilder:validation:Minimum=0
	PreAllocate int `json:"preAllocate,omitempty"`

	// MaxAboveWatermark is the maximum number of addresses to allocate
	// beyond the addresses needed to reach the PreAllocate watermark.
	// Going above the watermark can help reduce the number of API calls to
	// allocate IPs, e.g. when a new ENI is allocated, as many secondary
	// IPs as possible are allocated. Limiting the amount can help reduce
	// waste of IPs.
	//
	// +kubebuilder:validation:Minimum=0
	MaxAboveWatermark int `json:"maxAboveWatermark,omitempty"`
}

// AllocationMap is a map of user enis indexed by userID
type AllocationMap map[string]UserAllocationEnis

// UserAllocationEnis is a map of allocated enis indexed by eniID
type UserAllocationEnis map[string]*AllocationEni

// AllocationIP is an eni which is available for allocation, or already
// has been allocated
type AllocationEni struct {
	// UserID is the id of tenant user
	UserID string `json:"userID"`

	// EniID is the id of eni
	EniID string `json:"eniID"`

	// MacAddress is the hw address of eni
	MacAddress string `json:"macAddress"`

	// SubnetID is the subnet id where eni creates
	SubnetID string `json:"subnetID,omitempty"`

	// SecurityGroupIDs is the security groups bound to eni
	SecurityGroupIDs []string `json:"securityGroupIDs,omitempty"`

	// VpcID is the vpc id where eni creates
	VpcID string `json:"vpcID,omitempty"`

	// VpcCIDR is the cidr of VpcID
	VpcCIDR string `json:"vpcCidr,omitempty"`

	// PrimaryIPAddress the primary IPv4 address
	PrimaryIPAddress string `json:"primaryIPAddress"`

	// PrivateIPAddresses is a map of AllocationIP indexed by IP
	PrivateIPAddresses map[string]AllocationIP `json:"privateIPAddresses,omitempty"`

	// PrivateIPv6Addresses is a map of AllocationIP indexed by IPv6 address
	PrivateIPv6Addresses map[string]AllocationIP `json:"privateIPv6Addresses,omitempty"`
}

// AllocationIP is a IP which is available for allocation, or already
// has been allocated
type AllocationIP struct {
	// UserID is the id of tenant user which this IP belongs
	UserID string `json:"userID,omitempty"`

	// EniID is the id of eni which this IP belongs
	EniID string `json:"eniID,omitempty"`

	// EIP is the eip of this private IP, left blank if no eip
	//
	// +optional
	EIP *string `json:"eip,omitempty"`

	// Owner is the owner of the IP. This field is set if the IP has been
	// allocated. It will be set to format of pod namespace/pod name
	Owner string `json:"owner,omitempty"`

	// ContainerID is the infra container id of this IP
	ContainerID string `json:"containerID,omitempty"`
}

// EniMultiIPStatus defines the status of eni multi ip mode
type EniMultiIPStatus struct {
	// Used lists all IPs out of Spec.EniMultiIP.Pool which have been allocated
	// and are in use.
	//
	// +optional
	Used AllocationMap `json:"used,omitempty"`
	// NotReady lists all eniID which init failed
	//
	// +optional
	NotReady []string `json:"notReady,omitempty"`
}

type IPReleaseStatus string

// IPReleaseStatus value can be one of the following string :
// * marked-for-release : Set by bci controller as possible candidate for IP
// * ready-for-release  : Acknowledged as safe to release by agent
// * do-not-release     : IP already in use / not owned by the node. Set by agent
// * released           : IP successfully released. Set by bci controller
const (
	IPAMMarkForRelease  = "marked-for-release"
	IPAMReadyForRelease = "ready-for-release"
	IPAMDoNotRelease    = "do-not-release"
	IPAMReleased        = "released"
)

// BciNodeStatus defines the observed state of BciNode
type BciNodeStatus struct {
	// EniMultiIP is eni multi ip mode specific status of the node.
	EniMultiIP EniMultiIPStatus `json:"eniMultiIP,omitempty"`

	// WaitReleaseIP tracks the state for every IP considered for release.
	// This map in map structure has two level of indices. The first index is eni id while the
	// second index is IP address.
	//
	// +optional
	WaitReleaseIP map[string]map[string]IPReleaseStatus `json:"waitReleaseIP,omitempty"`
}

// +genclient
// +genclient:nonNamespaced
// +kubebuilder:object:root=true
// +kubebuilder:subresource:status
// +kubebuilder:resource:scope=Cluster

// BciNode is the Schema for the bcinodes API
type BciNode struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   BciNodeSpec   `json:"spec,omitempty"`
	Status BciNodeStatus `json:"status,omitempty"`
}

// +genclient:nonNamespaced
// +kubebuilder:object:root=true

// BciNodeList contains a list of BciNode
type BciNodeList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []BciNode `json:"items"`
}

func init() {
	SchemeBuilder.Register(&BciNode{}, &BciNodeList{})
}
