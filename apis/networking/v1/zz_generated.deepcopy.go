//go:build !ignore_autogenerated

/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by controller-gen. DO NOT EDIT.

package v1

import (
	runtime "k8s.io/apimachinery/pkg/runtime"
)

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AllocationEni) DeepCopyInto(out *AllocationEni) {
	*out = *in
	if in.SecurityGroupIDs != nil {
		in, out := &in.SecurityGroupIDs, &out.SecurityGroupIDs
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.PrivateIPAddresses != nil {
		in, out := &in.PrivateIPAddresses, &out.PrivateIPAddresses
		*out = make(map[string]AllocationIP, len(*in))
		for key, val := range *in {
			(*out)[key] = *val.DeepCopy()
		}
	}
	if in.PrivateIPv6Addresses != nil {
		in, out := &in.PrivateIPv6Addresses, &out.PrivateIPv6Addresses
		*out = make(map[string]AllocationIP, len(*in))
		for key, val := range *in {
			(*out)[key] = *val.DeepCopy()
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AllocationEni.
func (in *AllocationEni) DeepCopy() *AllocationEni {
	if in == nil {
		return nil
	}
	out := new(AllocationEni)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AllocationIP) DeepCopyInto(out *AllocationIP) {
	*out = *in
	if in.EIP != nil {
		in, out := &in.EIP, &out.EIP
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AllocationIP.
func (in *AllocationIP) DeepCopy() *AllocationIP {
	if in == nil {
		return nil
	}
	out := new(AllocationIP)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in AllocationMap) DeepCopyInto(out *AllocationMap) {
	{
		in := &in
		*out = make(AllocationMap, len(*in))
		for key, val := range *in {
			var outVal map[string]*AllocationEni
			if val == nil {
				(*out)[key] = nil
			} else {
				inVal := (*in)[key]
				in, out := &inVal, &outVal
				*out = make(UserAllocationEnis, len(*in))
				for key, val := range *in {
					var outVal *AllocationEni
					if val == nil {
						(*out)[key] = nil
					} else {
						inVal := (*in)[key]
						in, out := &inVal, &outVal
						*out = new(AllocationEni)
						(*in).DeepCopyInto(*out)
					}
					(*out)[key] = outVal
				}
			}
			(*out)[key] = outVal
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AllocationMap.
func (in AllocationMap) DeepCopy() AllocationMap {
	if in == nil {
		return nil
	}
	out := new(AllocationMap)
	in.DeepCopyInto(out)
	return *out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BciNode) DeepCopyInto(out *BciNode) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BciNode.
func (in *BciNode) DeepCopy() *BciNode {
	if in == nil {
		return nil
	}
	out := new(BciNode)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *BciNode) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BciNodeList) DeepCopyInto(out *BciNodeList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]BciNode, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BciNodeList.
func (in *BciNodeList) DeepCopy() *BciNodeList {
	if in == nil {
		return nil
	}
	out := new(BciNodeList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *BciNodeList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BciNodeSpec) DeepCopyInto(out *BciNodeSpec) {
	*out = *in
	in.EniMultiIP.DeepCopyInto(&out.EniMultiIP)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BciNodeSpec.
func (in *BciNodeSpec) DeepCopy() *BciNodeSpec {
	if in == nil {
		return nil
	}
	out := new(BciNodeSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BciNodeStatus) DeepCopyInto(out *BciNodeStatus) {
	*out = *in
	in.EniMultiIP.DeepCopyInto(&out.EniMultiIP)
	if in.WaitReleaseIP != nil {
		in, out := &in.WaitReleaseIP, &out.WaitReleaseIP
		*out = make(map[string]map[string]IPReleaseStatus, len(*in))
		for key, val := range *in {
			var outVal map[string]IPReleaseStatus
			if val == nil {
				(*out)[key] = nil
			} else {
				inVal := (*in)[key]
				in, out := &inVal, &outVal
				*out = make(map[string]IPReleaseStatus, len(*in))
				for key, val := range *in {
					(*out)[key] = val
				}
			}
			(*out)[key] = outVal
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BciNodeStatus.
func (in *BciNodeStatus) DeepCopy() *BciNodeStatus {
	if in == nil {
		return nil
	}
	out := new(BciNodeStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EniMultiIPSpec) DeepCopyInto(out *EniMultiIPSpec) {
	*out = *in
	if in.Pool != nil {
		in, out := &in.Pool, &out.Pool
		*out = make(AllocationMap, len(*in))
		for key, val := range *in {
			var outVal map[string]*AllocationEni
			if val == nil {
				(*out)[key] = nil
			} else {
				inVal := (*in)[key]
				in, out := &inVal, &outVal
				*out = make(UserAllocationEnis, len(*in))
				for key, val := range *in {
					var outVal *AllocationEni
					if val == nil {
						(*out)[key] = nil
					} else {
						inVal := (*in)[key]
						in, out := &inVal, &outVal
						*out = new(AllocationEni)
						(*in).DeepCopyInto(*out)
					}
					(*out)[key] = outVal
				}
			}
			(*out)[key] = outVal
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EniMultiIPSpec.
func (in *EniMultiIPSpec) DeepCopy() *EniMultiIPSpec {
	if in == nil {
		return nil
	}
	out := new(EniMultiIPSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EniMultiIPStatus) DeepCopyInto(out *EniMultiIPStatus) {
	*out = *in
	if in.Used != nil {
		in, out := &in.Used, &out.Used
		*out = make(AllocationMap, len(*in))
		for key, val := range *in {
			var outVal map[string]*AllocationEni
			if val == nil {
				(*out)[key] = nil
			} else {
				inVal := (*in)[key]
				in, out := &inVal, &outVal
				*out = make(UserAllocationEnis, len(*in))
				for key, val := range *in {
					var outVal *AllocationEni
					if val == nil {
						(*out)[key] = nil
					} else {
						inVal := (*in)[key]
						in, out := &inVal, &outVal
						*out = new(AllocationEni)
						(*in).DeepCopyInto(*out)
					}
					(*out)[key] = outVal
				}
			}
			(*out)[key] = outVal
		}
	}
	if in.NotReady != nil {
		in, out := &in.NotReady, &out.NotReady
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EniMultiIPStatus.
func (in *EniMultiIPStatus) DeepCopy() *EniMultiIPStatus {
	if in == nil {
		return nil
	}
	out := new(EniMultiIPStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in UserAllocationEnis) DeepCopyInto(out *UserAllocationEnis) {
	{
		in := &in
		*out = make(UserAllocationEnis, len(*in))
		for key, val := range *in {
			var outVal *AllocationEni
			if val == nil {
				(*out)[key] = nil
			} else {
				inVal := (*in)[key]
				in, out := &inVal, &outVal
				*out = new(AllocationEni)
				(*in).DeepCopyInto(*out)
			}
			(*out)[key] = outVal
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new UserAllocationEnis.
func (in UserAllocationEnis) DeepCopy() UserAllocationEnis {
	if in == nil {
		return nil
	}
	out := new(UserAllocationEnis)
	in.DeepCopyInto(out)
	return *out
}
