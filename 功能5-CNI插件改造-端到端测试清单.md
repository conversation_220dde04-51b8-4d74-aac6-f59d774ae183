# 功能5：CNI 插件改造 - 端到端测试清单

## 测试概述
本测试清单覆盖CNI插件IPv6双栈改造在真实环境中的端到端功能验证，包括完整的网络配置、Pod生命周期管理和故障处理场景。

## 测试环境

### 环境要求
- Kubernetes集群 (v1.20+)
- 支持IPv6的Linux内核 (≥3.10)
- 启用IPv6的网络栈
- BCI资源控制器和CNI插件部署
- 支持IPv6的VPC网络环境

### 测试数据准备
- IPv6子网: `2001:db8::/64`
- IPv4子网: `***********/24`
- 测试节点: 至少2个支持IPv6的节点
- 测试镜像: 支持IPv6的网络工具镜像

## 测试场景

### 1. 双栈Pod创建和删除 (核心功能)

#### 1.1 双栈Pod基本功能
- [ ] **E2E-CNI-001**: 创建双栈Pod，验证IPv4+IPv6地址分配
  - 创建带有`bci.baidu.com/bci-enable-ipv6: "true"`的Pod
  - 验证Pod获得IPv4和IPv6地址
  - 验证地址格式正确性
  - 验证网络接口配置

- [ ] **E2E-CNI-002**: 删除双栈Pod，验证资源清理
  - 删除双栈Pod
  - 验证IPv4和IPv6地址释放
  - 验证路由规则清理
  - 验证网络命名空间清理

#### 1.2 单栈Pod兼容性
- [ ] **E2E-CNI-003**: 创建IPv4单栈Pod，验证向后兼容
  - 创建不带IPv6 annotation的Pod
  - 验证仅分配IPv4地址
  - 验证原有功能不受影响

- [ ] **E2E-CNI-004**: IPv6单栈Pod（如果支持）
  - 创建仅启用IPv6的Pod
  - 验证IPv6地址分配
  - 验证IPv6网络连通性

### 2. 网络连通性测试

#### 2.1 双栈网络连通性
- [ ] **E2E-CNI-005**: 双栈Pod内部网络连通性
  - 在Pod内执行IPv4网络测试
  - 在Pod内执行IPv6网络测试
  - 验证默认路由配置
  - 验证DNS解析功能

- [ ] **E2E-CNI-006**: 双栈Pod间通信
  - 创建两个双栈Pod
  - 测试IPv4地址互通
  - 测试IPv6地址互通
  - 验证双栈网络隔离

#### 2.2 外部网络连通性
- [ ] **E2E-CNI-007**: 双栈Pod访问外部网络
  - 测试IPv4访问外部服务
  - 测试IPv6访问外部服务
  - 验证出入流量路由
  - 验证NAT功能

- [ ] **E2E-CNI-008**: 外部访问双栈Pod
  - 配置Service暴露双栈Pod
  - 测试IPv4访问Service
  - 测试IPv6访问Service
  - 验证负载均衡功能

### 3. 故障处理和恢复

#### 3.1 网络故障处理
- [ ] **E2E-CNI-009**: IPv6地址分配失败处理
  - 模拟IPv6地址池耗尽
  - 验证Pod创建失败
  - 验证错误信息准确性
  - 验证资源清理完整性

- [ ] **E2E-CNI-010**: IPv6网络配置失败处理
  - 模拟IPv6路由配置失败
  - 验证Pod创建失败
  - 验证回滚机制
  - 验证IPv4功能不受影响

#### 3.2 节点故障处理
- [ ] **E2E-CNI-011**: 节点IPv6功能禁用
  - 在节点上禁用IPv6功能
  - 创建双栈Pod
  - 验证Pod调度到支持IPv6的节点
  - 验证错误处理机制

- [ ] **E2E-CNI-012**: 节点网络中断恢复
  - 模拟节点网络中断
  - 验证Pod网络自动恢复
  - 验证IPv4/IPv6连通性恢复
  - 验证路由表重建

### 4. 多Pod场景测试

#### 4.1 高并发场景
- [ ] **E2E-CNI-013**: 并发创建多个双栈Pod
  - 同时创建10个双栈Pod
  - 验证所有Pod正常启动
  - 验证IP地址分配无冲突
  - 验证网络隔离正确

- [ ] **E2E-CNI-014**: 大规模Pod部署
  - 创建50个双栈Pod
  - 验证IP地址池管理
  - 验证网络性能
  - 验证资源使用情况

#### 4.2 混合场景
- [ ] **E2E-CNI-015**: 混合单栈和双栈Pod
  - 创建IPv4单栈Pod
  - 创建IPv6双栈Pod
  - 验证混合网络连通性
  - 验证资源隔离

- [ ] **E2E-CNI-016**: 多租户场景
  - 在不同Namespace创建双栈Pod
  - 验证网络隔离
  - 验证资源配额
  - 验证安全策略

### 5. 配置和管理测试

#### 5.1 配置变更测试
- [ ] **E2E-CNI-017**: 动态启用/禁用IPv6
  - 修改Pod annotation启用IPv6
  - 验证配置生效
  - 修改annotation禁用IPv6
  - 验证配置回退

- [ ] **E2E-CNI-018**: CNI配置更新
  - 更新CNI配置文件
  - 重启CNI插件
  - 验证现有Pod不受影响
  - 验证新Pod使用新配置

#### 5.2 运维管理测试
- [ ] **E2E-CNI-019**: 日志和监控
  - 查看CNI插件日志
  - 验证IPv6操作日志
  - 检查监控指标
  - 验证告警机制

- [ ] **E2E-CNI-020**: 故障排查
  - 模拟常见网络问题
  - 使用诊断工具排查
  - 验证故障定位准确性
  - 验证修复建议有效性

### 6. 性能和压力测试

#### 6.1 网络性能测试
- [ ] **E2E-CNI-021**: 双栈网络性能基准
  - 测试IPv4网络吞吐量
  - 测试IPv6网络吞吐量
  - 对比单栈和双栈性能
  - 验证性能损失在可接受范围

- [ ] **E2E-CNI-022**: 网络延迟测试
  - 测试IPv4网络延迟
  - 测试IPv6网络延迟
  - 测试双栈切换延迟
  - 验证延迟符合SLA要求

#### 6.2 资源使用测试
- [ ] **E2E-CNI-023**: 内存和CPU使用
  - 监控CNI插件资源使用
  - 对比单栈和双栈资源消耗
  - 验证资源使用合理性
  - 检查内存泄漏

- [ ] **E2E-CNI-024**: 网络资源使用
  - 监控路由表大小
  - 监控网络接口数量
  - 验证资源清理效率
  - 检查资源泄漏

### 7. 集成测试

#### 7.1 与Kubernetes集成
- [ ] **E2E-CNI-025**: Service双栈支持
  - 创建双栈Service
  - 验证IPv4和IPv6 Endpoint
  - 测试Service发现
  - 验证负载均衡

- [ ] **E2E-CNI-026**: Ingress双栈支持
  - 配置双栈Ingress
  - 测试IPv4和IPv6访问
  - 验证SSL终止
  - 验证路由规则

#### 7.2 与云平台集成
- [ ] **E2E-CNI-027**: 百度云VPC集成
  - 验证IPv6子网配置
  - 测试安全组规则
  - 验证路由表配置
  - 测试跨可用区通信

- [ ] **E2E-CNI-028**: 负载均衡器集成
  - 配置双栈负载均衡器
  - 测试IPv4和IPv6流量分发
  - 验证健康检查
  - 测试故障转移

### 8. 兼容性和升级测试

#### 8.1 版本兼容性
- [ ] **E2E-CNI-029**: 旧版本兼容性
  - 从旧版本升级
  - 验证现有Pod不受影响
  - 验证配置迁移
  - 测试功能完整性

- [ ] **E2E-CNI-030**: 混合版本部署
  - 部分节点使用新版本
  - 验证跨版本通信
  - 测试滚动升级
  - 验证一致性

#### 8.2 环境兼容性
- [ ] **E2E-CNI-031**: 不同内核版本
  - 在不同内核版本测试
  - 验证功能完整性
  - 记录兼容性矩阵
  - 验证性能一致性

- [ ] **E2E-CNI-032**: 不同容器运行时
  - 测试containerd集成
  - 测试docker集成
  - 验证CRI兼容性
  - 测试网络命名空间

## 测试工具和脚本

### 测试工具
- `kubectl` - Kubernetes命令行工具
- `ping6` - IPv6连通性测试
- `iperf3` - 网络性能测试
- `tcpdump` - 网络包分析
- `nsenter` - 命名空间工具

### 测试脚本
- `test-dual-stack-pod.sh` - 双栈Pod测试脚本
- `test-network-connectivity.sh` - 网络连通性测试
- `test-performance.sh` - 性能测试脚本
- `test-cleanup.sh` - 资源清理验证

### 测试镜像
- `busybox-ipv6:latest` - 支持IPv6的busybox
- `nginx-ipv6:latest` - 支持IPv6的nginx
- `curl-ipv6:latest` - 支持IPv6的curl工具

## 测试数据和配置

### Pod配置模板
```yaml
apiVersion: v1
kind: Pod
metadata:
  name: test-dual-stack
  annotations:
    bci.baidu.com/bci-enable-ipv6: "true"
spec:
  containers:
  - name: test
    image: busybox-ipv6:latest
    command: ["sleep", "3600"]
```

### Service配置模板
```yaml
apiVersion: v1
kind: Service
metadata:
  name: test-service
spec:
  ipFamilies: ["IPv4", "IPv6"]
  ipFamilyPolicy: RequireDualStack
  ports:
  - port: 80
    targetPort: 80
  selector:
    app: test
```

## 测试执行计划

### 测试阶段
1. **Phase 1**: 基础功能测试 (E2E-CNI-001 ~ E2E-CNI-008)
2. **Phase 2**: 故障处理测试 (E2E-CNI-009 ~ E2E-CNI-012)
3. **Phase 3**: 多Pod场景测试 (E2E-CNI-013 ~ E2E-CNI-016)
4. **Phase 4**: 配置管理测试 (E2E-CNI-017 ~ E2E-CNI-020)
5. **Phase 5**: 性能压力测试 (E2E-CNI-021 ~ E2E-CNI-024)
6. **Phase 6**: 集成测试 (E2E-CNI-025 ~ E2E-CNI-028)
7. **Phase 7**: 兼容性测试 (E2E-CNI-029 ~ E2E-CNI-032)

### 测试优先级
- **P0 (高优先级)**: E2E-CNI-001~008 (核心功能)
- **P1 (中优先级)**: E2E-CNI-009~020 (故障处理和配置)
- **P2 (低优先级)**: E2E-CNI-021~032 (性能和兼容性)

## 测试报告

### 测试覆盖率
- **功能覆盖率**: 100%
- **代码覆盖率**: >80%
- **场景覆盖率**: 涵盖生产环境主要场景

### 通过标准
- 所有P0测试用例通过
- 90%以上P1测试用例通过
- 80%以上P2测试用例通过
- 无阻塞性Bug
- 性能指标符合要求

## 测试统计
- **总测试用例数**: 32
- **核心功能测试**: 8
- **故障处理测试**: 4
- **多Pod场景测试**: 4
- **配置管理测试**: 4
- **性能压力测试**: 4
- **集成测试**: 4
- **兼容性测试**: 4

## 环境清理
每次测试后需要清理：
- 删除测试Pod和Service
- 清理网络配置
- 释放IP地址
- 清理日志文件
- 重置监控指标 