# 功能4-Node agent改造-单元测试清单

## 测试概述
本文档列出了功能4（Node agent改造）的单元测试清单，用于验证IPAM双栈支持的正确性。

## 测试环境
- Go版本：1.19+
- 测试框架：testing包
- Mock工具：golang/mock

## 测试用例分类

### 1. IPAM结构体测试
#### 1.1 新增字段验证
- [ ] 测试IPv6UserIdlePool字段的初始化
- [ ] 测试IPv6Lookup字段的初始化
- [ ] 测试新增字段的类型正确性

#### 1.2 字段并发安全性
- [ ] 测试IPv6UserIdlePool的并发读写安全性
- [ ] 测试IPv6Lookup的并发读写安全性
- [ ] 测试字段间的并发操作一致性

### 2. 地址池管理测试
#### 2.1 UpdateUserIdlePool函数测试
- [ ] 测试IPv4地址池更新
- [ ] 测试IPv6地址池更新
- [ ] 测试IPv4+IPv6混合地址池更新
- [ ] 测试重复IP地址的处理
- [ ] 测试空IP地址列表的处理
- [ ] 测试无效IP地址格式的处理

#### 2.2 RebuildUserIdlePool函数测试
- [ ] 测试IPv4地址池重建
- [ ] 测试IPv6地址池重建
- [ ] 测试IPv4+IPv6混合地址池重建
- [ ] 测试已分配IP地址的过滤
- [ ] 测试空数据的重建处理

#### 2.3 DeleteUserIdlePool函数测试
- [ ] 测试IPv4地址池删除
- [ ] 测试IPv6地址池删除
- [ ] 测试IPv4+IPv6混合地址池删除
- [ ] 测试部分删除的处理
- [ ] 测试删除不存在的地址池

### 3. IP地址分配测试
#### 3.1 AllocateIP函数测试
- [ ] 测试仅分配IPv4地址
- [ ] 测试仅分配IPv6地址
- [ ] 测试同时分配IPv4和IPv6地址（双栈）
- [ ] 测试IPv6分配失败时的IPv4回滚
- [ ] 测试不支持IPv6的节点处理
- [ ] 测试Pod annotation的IPv6配置
- [ ] 测试节点label的IPv6配置

#### 3.2 popIPs函数测试
- [ ] 测试IPv4地址的弹出
- [ ] 测试IPv4地址池耗尽的处理
- [ ] 测试IPv4地址队列的过滤器

#### 3.3 popIPv6s函数测试
- [ ] 测试IPv6地址的弹出
- [ ] 测试IPv6地址池耗尽的处理
- [ ] 测试IPv6地址队列的过滤器
- [ ] 测试IPv6地址的请求触发

### 4. IP地址使用状态管理测试
#### 4.1 markIPUsed函数测试
- [ ] 测试IPv4地址标记为已使用
- [ ] 测试IPv6地址标记为已使用
- [ ] 测试IPv4+IPv6混合地址标记
- [ ] 测试重复标记的处理
- [ ] 测试无效IP地址的标记

#### 4.2 markIPUnused函数测试
- [ ] 测试IPv4地址标记为未使用
- [ ] 测试IPv6地址标记为未使用
- [ ] 测试IPv4+IPv6混合地址释放
- [ ] 测试不存在IP地址的释放
- [ ] 测试释放计数的准确性

### 5. IP地址状态查询测试
#### 5.1 ipIsUsed函数测试
- [ ] 测试IPv4地址使用状态查询
- [ ] 测试不存在用户的处理
- [ ] 测试不存在ENI的处理
- [ ] 测试空IP地址列表的处理

#### 5.2 ipv6IsUsed函数测试
- [ ] 测试IPv6地址使用状态查询
- [ ] 测试不存在用户的处理
- [ ] 测试不存在ENI的处理
- [ ] 测试空IPv6地址列表的处理

#### 5.3 isIPAllocated函数测试
- [ ] 测试IPv4地址分配状态查询
- [ ] 测试边界条件处理

#### 5.4 isIPv6Allocated函数测试
- [ ] 测试IPv6地址分配状态查询
- [ ] 测试边界条件处理

### 6. 地址类型识别测试
#### 6.1 isIPv6Address函数测试
- [ ] 测试标准IPv6地址识别
- [ ] 测试IPv4地址识别
- [ ] 测试无效地址格式识别
- [ ] 测试边界条件处理

### 7. 过滤器测试
#### 7.1 usableIPFilter函数测试
- [ ] 测试IPv4地址可用性过滤
- [ ] 测试WaitReleaseIP状态过滤
- [ ] 测试空payload处理

#### 7.2 usableIPv6Filter函数测试
- [ ] 测试IPv6地址可用性过滤
- [ ] 测试WaitReleaseIP状态过滤
- [ ] 测试空payload处理

### 8. 辅助函数测试
#### 8.1 shouldRequestIPv6函数测试
- [ ] 测试节点IPv6标签检查
- [ ] 测试Pod IPv6 annotation检查
- [ ] 测试节点获取失败处理
- [ ] 测试Pod获取失败处理
- [ ] 测试IPv6不支持的场景

#### 8.2 getIPv6AndMarkUsed函数测试
- [ ] 测试IPv6地址分配和标记
- [ ] 测试IPv6地址分配失败处理
- [ ] 测试IPv6地址分配重试机制
- [ ] 测试已分配IPv6地址的查找

#### 8.3 checkPodIPv6sHasAllocated函数测试
- [ ] 测试Pod IPv6地址分配状态查询
- [ ] 测试多IPv6地址的查找
- [ ] 测试不存在的Pod处理

### 9. 请求处理测试
#### 9.1 RequestPrivateIPv6函数测试
- [ ] 测试IPv6地址请求节点标记
- [ ] 测试节点获取失败处理
- [ ] 测试节点annotation patch失败处理

### 10. 辅助函数扩展测试
#### 10.1 isPodReleased函数测试
- [ ] 测试Pod IPv4地址释放状态查询
- [ ] 测试Pod IPv6地址释放状态查询
- [ ] 测试Pod IPv4+IPv6混合地址释放状态查询

#### 10.2 getEniIDByPodNameSpaceName函数测试
- [ ] 测试通过Pod名称查找ENI ID（IPv4）
- [ ] 测试通过Pod名称查找ENI ID（IPv6）
- [ ] 测试通过Pod名称查找ENI ID（IPv4+IPv6）

### 11. RPC结构体测试
#### 11.1 ENIMultiIPReply测试
- [ ] 测试IPv6字段的设置和获取
- [ ] 测试IPv6Gateway字段的设置和获取
- [ ] 测试双栈地址的序列化和反序列化

## 测试数据准备
### 测试用例数据
- IPv4地址池：********-**********
- IPv6地址池：2001:db8::1-2001:db8::100
- 测试节点：node-1, node-2
- 测试Pod：test-pod-1, test-pod-2
- 测试用户：user-1, user-2
- 测试ENI：eni-1, eni-2

### Mock对象
- Kubernetes Client Mock
- BciNode CRD Mock
- Pod资源Mock
- Node资源Mock

## 测试执行
```bash
# 运行所有单元测试
go test -v ./pkg/nodeagent/ipam/... -cover

# 运行特定测试
go test -v ./pkg/nodeagent/ipam/... -run TestIPAM_AllocateIP

# 生成测试覆盖率报告
go test -v ./pkg/nodeagent/ipam/... -coverprofile=coverage.out
go tool cover -html=coverage.out -o coverage.html
```

## 测试覆盖率要求
- 代码覆盖率：≥85%
- 分支覆盖率：≥80%
- 函数覆盖率：≥90%

## 测试注意事项
1. 所有测试用例应该是独立的，不依赖于其他测试用例
2. 测试用例应该覆盖正常情况和异常情况
3. 测试用例应该包含边界条件测试
4. 测试用例应该验证并发安全性
5. 测试用例应该使用mock对象隔离依赖

## 测试验证标准
- [ ] 所有测试用例通过
- [ ] 测试覆盖率达到要求
- [ ] 没有数据竞争（data race）
- [ ] 没有内存泄漏
- [ ] 性能测试通过

## 总结
本测试清单包含11个测试分类，共计82个测试用例，全面覆盖了Node agent IPv6改造的各个方面。通过执行这些测试用例，可以确保IPv6双栈功能的正确性、稳定性和性能。 