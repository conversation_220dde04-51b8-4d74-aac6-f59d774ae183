# 功能4-Node agent改造-端到端测试清单

## 测试概述
本文档列出了功能4（Node agent改造）的端到端测试清单，用于验证IPAM双栈支持在真实环境中的正确性。

## 测试环境
- Kubernetes集群：1.20+
- 百度智能云VPC环境
- 支持IPv6的子网和ENI
- Node agent部署版本：最新

## 测试前置条件
### 环境准备
1. 准备双栈VPC环境
2. 创建支持IPv6的子网
3. 配置安全组规则
4. 部署更新后的Node agent
5. 创建测试节点标签

### 节点配置
```yaml
apiVersion: v1
kind: Node
metadata:
  name: test-node
  labels:
    bci-enable-ipv6: "true"
```

### 测试Pod模板
```yaml
apiVersion: v1
kind: Pod
metadata:
  name: test-pod
  annotations:
    bci.baidu.com/bci-enable-ipv6: "true"
    cross-vpc-eni.cce.io/subnetID: "subnet-xxxxx"
    cross-vpc-eni.cce.io/securityGroupIDs: "sg-xxxxx"
spec:
  containers:
  - name: test-container
    image: nginx:latest
```

## 测试用例分类

### 1. 单栈IPv4测试
#### 1.1 传统IPv4功能验证
- [ ] 在IPv6节点上创建仅IPv4的Pod
- [ ] 验证IPv4地址正常分配
- [ ] 验证IPv4网络连通性
- [ ] 验证IPv4地址池管理
- [ ] 验证Pod删除后IPv4地址释放

#### 1.2 IPv4地址池管理
- [ ] 验证IPv4地址池的动态扩展
- [ ] 验证IPv4地址池的回收机制
- [ ] 验证IPv4地址池的并发访问
- [ ] 验证IPv4地址池的持久化

### 2. 单栈IPv6测试
#### 2.1 IPv6地址分配
- [ ] 在IPv6节点上创建IPv6的Pod
- [ ] 验证IPv6地址正常分配
- [ ] 验证IPv6地址格式正确性
- [ ] 验证IPv6地址唯一性
- [ ] 验证IPv6地址与ENI的绑定

#### 2.2 IPv6网络连通性
- [ ] 验证Pod内部IPv6地址配置
- [ ] 验证Pod与外部IPv6网络连通性
- [ ] 验证Pod间IPv6网络连通性
- [ ] 验证IPv6路由规则正确性

#### 2.3 IPv6地址池管理
- [ ] 验证IPv6地址池的初始化
- [ ] 验证IPv6地址池的动态扩展
- [ ] 验证IPv6地址池的回收机制
- [ ] 验证IPv6地址池的并发访问

### 3. 双栈测试
#### 3.1 双栈地址分配
- [ ] 在IPv6节点上创建双栈Pod
- [ ] 验证同时分配IPv4和IPv6地址
- [ ] 验证双栈地址的唯一性
- [ ] 验证双栈地址的正确绑定
- [ ] 验证双栈地址的RPC响应

#### 3.2 双栈网络连通性
- [ ] 验证Pod内部双栈地址配置
- [ ] 验证Pod IPv4网络连通性
- [ ] 验证Pod IPv6网络连通性
- [ ] 验证双栈路由规则正确性
- [ ] 验证双栈DNS解析

#### 3.3 双栈地址释放
- [ ] 验证Pod删除后双栈地址同时释放
- [ ] 验证IPv4地址回收到正确的地址池
- [ ] 验证IPv6地址回收到正确的地址池
- [ ] 验证双栈地址释放的原子性

### 4. 错误处理测试
#### 4.1 IPv6分配失败处理
- [ ] 验证IPv6地址池耗尽时的处理
- [ ] 验证IPv6分配失败时的IPv4回滚
- [ ] 验证IPv6网络不可用时的处理
- [ ] 验证IPv6 ENI创建失败时的处理

#### 4.2 节点异常处理
- [ ] 验证节点不支持IPv6时的处理
- [ ] 验证节点IPv6标签错误时的处理
- [ ] 验证节点网络配置错误时的处理
- [ ] 验证节点重启后的状态恢复

#### 4.3 Pod异常处理
- [ ] 验证Pod annotation错误时的处理
- [ ] 验证Pod网络配置错误时的处理
- [ ] 验证Pod强制删除时的地址清理
- [ ] 验证Pod重建时的地址重分配

### 5. 兼容性测试
#### 5.1 向后兼容性
- [ ] 验证旧版本Pod在新Node agent上的运行
- [ ] 验证IPv4-only配置的兼容性
- [ ] 验证现有IPv4地址池的兼容性
- [ ] 验证现有CRD状态的兼容性

#### 5.2 混合环境测试
- [ ] 验证IPv4节点和IPv6节点的混合部署
- [ ] 验证IPv4 Pod和IPv6 Pod的混合调度
- [ ] 验证不同版本Node agent的混合部署
- [ ] 验证不同子网配置的混合使用

### 6. 性能测试
#### 6.1 地址分配性能
- [ ] 测试IPv4地址分配的性能
- [ ] 测试IPv6地址分配的性能
- [ ] 测试双栈地址分配的性能
- [ ] 测试大规模Pod创建的性能

#### 6.2 地址释放性能
- [ ] 测试IPv4地址释放的性能
- [ ] 测试IPv6地址释放的性能
- [ ] 测试双栈地址释放的性能
- [ ] 测试大规模Pod删除的性能

#### 6.3 并发性能
- [ ] 测试并发IPv4地址分配
- [ ] 测试并发IPv6地址分配
- [ ] 测试并发双栈地址分配
- [ ] 测试并发地址释放

### 7. 可靠性测试
#### 7.1 故障恢复测试
- [ ] 测试Node agent重启后的状态恢复
- [ ] 测试网络中断后的自动恢复
- [ ] 测试VPC API故障后的重试机制
- [ ] 测试Kubernetes API故障后的重试机制

#### 7.2 资源泄漏测试
- [ ] 测试IPv4地址池的资源泄漏
- [ ] 测试IPv6地址池的资源泄漏
- [ ] 测试内存泄漏检测
- [ ] 测试goroutine泄漏检测

### 8. 监控和日志测试
#### 8.1 监控指标验证
- [ ] 验证IPv4地址池使用率指标
- [ ] 验证IPv6地址池使用率指标
- [ ] 验证双栈地址分配成功率指标
- [ ] 验证地址分配延迟指标

#### 8.2 日志记录验证
- [ ] 验证IPv4地址分配日志
- [ ] 验证IPv6地址分配日志
- [ ] 验证双栈地址分配日志
- [ ] 验证错误处理日志

## 测试执行步骤

### 环境准备
1. 部署测试环境
2. 配置节点标签
3. 部署Node agent
4. 验证基础功能

### 测试执行
1. 执行单栈IPv4测试
2. 执行单栈IPv6测试
3. 执行双栈测试
4. 执行错误处理测试
5. 执行性能测试
6. 执行可靠性测试

### 结果验证
1. 检查所有测试用例结果
2. 验证监控指标
3. 分析性能数据
4. 检查日志记录

## 测试数据收集
### 性能指标
- 地址分配延迟：P50, P90, P99
- 地址释放延迟：P50, P90, P99
- 并发处理能力：QPS
- 资源使用率：CPU, Memory

### 功能指标
- 地址分配成功率：>99.9%
- 地址释放成功率：>99.9%
- 错误恢复时间：<30s
- 状态一致性：100%

## 测试工具
### 自动化测试脚本
```bash
#!/bin/bash
# 双栈Pod创建测试
kubectl apply -f test-pod-dual-stack.yaml
kubectl wait --for=condition=Ready pod/test-pod --timeout=60s

# 验证地址分配
kubectl get pod test-pod -o yaml | grep -E "(bci_internal_PodIP|bci_internal_multi_podips)"

# 测试网络连通性
kubectl exec test-pod -- ping -c 3 <external-ipv4>
kubectl exec test-pod -- ping6 -c 3 <external-ipv6>
```

### 测试用例模板
```yaml
apiVersion: v1
kind: Pod
metadata:
  name: test-dual-stack-pod
  annotations:
    bci.baidu.com/bci-enable-ipv6: "true"
    cross-vpc-eni.cce.io/subnetID: "subnet-xxxxx"
    cross-vpc-eni.cce.io/securityGroupIDs: "sg-xxxxx"
spec:
  containers:
  - name: test-container
    image: nginx:latest
    ports:
    - containerPort: 80
```

## 测试验证标准
- [ ] 所有测试用例通过
- [ ] 性能指标达到要求
- [ ] 无资源泄漏
- [ ] 错误处理正确
- [ ] 监控指标正常
- [ ] 日志记录完整

## 回归测试
### 每日回归测试
- [ ] 基础功能测试
- [ ] 性能基线测试
- [ ] 兼容性测试

### 发布前回归测试
- [ ] 全量功能测试
- [ ] 性能压力测试
- [ ] 可靠性测试

## 总结
本测试清单包含8个测试分类，共计62个测试用例，全面覆盖了Node agent IPv6改造的端到端场景。通过执行这些测试用例，可以确保IPv6双栈功能在生产环境中的正确性、稳定性和性能。 