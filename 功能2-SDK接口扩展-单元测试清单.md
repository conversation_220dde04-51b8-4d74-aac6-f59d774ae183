# 功能2-SDK接口扩展-单元测试清单

## 功能概述
SDK接口扩展功能在现有的百度云ENI SDK基础上增加了IPv6支持，通过在关键数据结构中添加IPv6相关字段来实现双栈IP地址管理。

## 测试范围
- **修改文件**: 
  - `bci-cni-driver/pkg/bcesdk/eni/batch_add_private_ip.go`
  - `bci-cni-driver/pkg/bcesdk/eni/create.go`
  - `bci-cni-driver/pkg/bcesdk/eni/stat.go`
  - `bci-cni-driver/pkg/bcesdk/eni/types.go`
- **核心变更**: 
  - BatchPrivateIPArgs添加IsIpv6字段
  - CreateENIArgs添加IPv6PrivateIPSet字段
  - StatENIResponse添加IPv6PrivateIPSet字段
  - ENI结构添加IPv6PrivateIPSet字段
- **测试目标**: 验证SDK接口对IPv6的支持和向后兼容性

## 单元测试清单

### 1. BatchPrivateIPArgs结构体测试
**测试类别**: 结构体字段验证
**测试目标**: 验证批量IP操作参数结构的IPv6支持

#### 1.1 IsIpv6字段验证测试
- **测试用例**: TestBatchPrivateIPArgsIsIpv6Field
- **验证内容**: 
  - 验证IsIpv6字段存在且类型为bool
  - 验证JSON标签正确（isIpv6）
  - 验证字段默认值为false
  - 验证字段可以正确设置为true
- **测试方法**: 创建BatchPrivateIPArgs实例，验证字段属性
- **期望结果**: 字段定义符合设计方案

#### 1.2 IPv6参数序列化测试
- **测试用例**: TestBatchPrivateIPArgsIPv6Serialization
- **验证内容**:
  - 验证IsIpv6=true时的JSON序列化
  - 验证IsIpv6=false时的JSON序列化
  - 验证IPv6地址列表的序列化
  - 验证混合IPv4/IPv6地址的处理
- **测试数据**: 
  - IPv4地址参数 (IsIpv6=false)
  - IPv6地址参数 (IsIpv6=true)
  - 混合地址参数
- **期望结果**: 序列化格式正确，字段值准确

#### 1.3 向后兼容性测试
- **测试用例**: TestBatchPrivateIPArgsBackwardCompatibility
- **验证内容**:
  - 验证不设置IsIpv6字段时的默认行为
  - 验证现有IPv4代码的兼容性
  - 验证新字段不影响现有功能
- **测试方法**: 使用旧版本参数格式，验证兼容性
- **期望结果**: 向后兼容性完全保持

### 2. CreateENIArgs结构体测试
**测试类别**: ENI创建参数验证
**测试目标**: 验证ENI创建参数的IPv6支持

#### 2.1 IPv6PrivateIPSet字段验证测试
- **测试用例**: TestCreateENIArgsIPv6PrivateIPSet
- **验证内容**:
  - 验证IPv6PrivateIPSet字段存在
  - 验证字段类型为[]*PrivateIP
  - 验证JSON标签正确（ipv6PrivateIpSet）
  - 验证字段可以为空
- **测试方法**: 创建CreateENIArgs实例，验证字段属性
- **期望结果**: 字段定义符合设计方案

#### 2.2 双栈ENI创建参数测试
- **测试用例**: TestCreateENIArgsDualStack
- **验证内容**:
  - 验证同时包含IPv4和IPv6地址的参数
  - 验证IPv4和IPv6地址的独立性
  - 验证地址数量限制
- **测试数据**:
  - 同时包含IPv4和IPv6地址的参数
  - 仅包含IPv4地址的参数
  - 仅包含IPv6地址的参数
- **期望结果**: 双栈参数正确处理

#### 2.3 参数验证逻辑测试
- **测试用例**: TestCreateENIArgsValidation
- **验证内容**:
  - 验证IPv6地址格式验证
  - 验证IPv6主IP验证逻辑
  - 验证IPv6地址数量限制
- **测试数据**:
  - 有效的IPv6地址
  - 无效的IPv6地址格式
  - 超出限制的IPv6地址数量
- **期望结果**: 参数验证正确，错误信息准确

### 3. StatENIResponse结构体测试
**测试类别**: ENI状态响应验证
**测试目标**: 验证ENI状态查询响应的IPv6支持

#### 3.1 IPv6PrivateIPSet字段验证测试
- **测试用例**: TestStatENIResponseIPv6PrivateIPSet
- **验证内容**:
  - 验证IPv6PrivateIPSet字段存在
  - 验证字段类型为[]*PrivateIP
  - 验证JSON标签正确（ipv6PrivateIpSet）
  - 验证字段反序列化正确
- **测试方法**: 创建StatENIResponse实例，验证字段属性
- **期望结果**: 字段定义符合设计方案

#### 3.2 双栈ENI状态响应测试
- **测试用例**: TestStatENIResponseDualStack
- **验证内容**:
  - 验证同时包含IPv4和IPv6地址的响应
  - 验证IPv4和IPv6地址的独立显示
  - 验证地址状态的正确反映
- **测试数据**:
  - 包含双栈地址的响应JSON
  - 仅包含IPv4地址的响应JSON
  - 仅包含IPv6地址的响应JSON
- **期望结果**: 双栈响应正确解析

#### 3.3 响应反序列化测试
- **测试用例**: TestStatENIResponseDeserialization
- **验证内容**:
  - 验证包含IPv6地址的JSON响应反序列化
  - 验证IPv6地址状态的正确解析
  - 验证与IPv4地址的区分
- **测试数据**:
  - 真实的百度云API响应JSON
  - 模拟的双栈响应JSON
- **期望结果**: 反序列化成功，数据完整

### 4. ENI结构体测试
**测试类别**: ENI数据结构验证
**测试目标**: 验证ENI基础数据结构的IPv6支持

#### 4.1 IPv6PrivateIPSet字段验证测试
- **测试用例**: TestENIStructureIPv6PrivateIPSet
- **验证内容**:
  - 验证IPv6PrivateIPSet字段存在
  - 验证字段类型为[]*PrivateIP
  - 验证JSON标签正确
  - 验证字段初始化行为
- **测试方法**: 创建ENI实例，验证字段属性
- **期望结果**: 字段定义符合设计方案

#### 4.2 ENI数据结构完整性测试
- **测试用例**: TestENIStructureIntegrity
- **验证内容**:
  - 验证IPv4和IPv6地址的独立存储
  - 验证数据结构的一致性
  - 验证字段的可选性
- **测试数据**:
  - 包含双栈地址的ENI数据
  - 仅包含IPv4地址的ENI数据
  - 空地址池的ENI数据
- **期望结果**: 数据结构完整，一致性良好

### 5. 接口集成测试
**测试类别**: API接口集成验证
**测试目标**: 验证扩展后的接口与百度云API的集成

#### 5.1 批量添加IPv6地址接口测试
- **测试用例**: TestBatchAddIPv6PrivateIP
- **验证内容**:
  - 验证IsIpv6=true时的API调用
  - 验证IPv6地址的批量添加
  - 验证API响应的正确处理
- **测试数据**:
  - 有效的IPv6地址列表
  - 自动分配IPv6地址的请求
- **期望结果**: API调用成功，IPv6地址正确添加

#### 5.2 创建双栈ENI接口测试
- **测试用例**: TestCreateDualStackENI
- **验证内容**:
  - 验证同时创建IPv4和IPv6地址的ENI
  - 验证IPv6PrivateIPSet参数的传递
  - 验证API响应的正确处理
- **测试数据**:
  - 包含IPv4和IPv6地址的创建参数
  - 仅包含IPv6地址的创建参数
- **期望结果**: 双栈ENI创建成功

#### 5.3 查询双栈ENI接口测试
- **测试用例**: TestStatDualStackENI
- **验证内容**:
  - 验证双栈ENI状态查询
  - 验证IPv6地址信息的正确返回
  - 验证响应解析的正确性
- **测试数据**:
  - 真实的双栈ENI ID
  - 模拟的API响应
- **期望结果**: 双栈ENI状态正确查询

### 6. 错误处理和边界条件测试
**测试类别**: 异常情况验证
**测试目标**: 验证IPv6相关的错误处理

#### 6.1 IPv6地址格式验证测试
- **测试用例**: TestIPv6AddressValidation
- **验证内容**:
  - 验证无效IPv6地址格式的处理
  - 验证IPv6地址长度限制
  - 验证特殊IPv6地址格式的处理
- **测试数据**:
  - 格式错误的IPv6地址
  - 超长的IPv6地址
  - 特殊格式的IPv6地址（::1, ::ffff:0:0等）
- **期望结果**: 错误处理正确，信息准确

#### 6.2 API错误响应处理测试
- **测试用例**: TestIPv6APIErrorHandling
- **验证内容**:
  - 验证IPv6相关API错误的处理
  - 验证错误信息的正确解析
  - 验证重试机制的正确性
- **测试数据**:
  - 模拟的API错误响应
  - 网络超时场景
  - 权限不足场景
- **期望结果**: 错误处理健壮，用户友好

#### 6.3 边界条件测试
- **测试用例**: TestIPv6BoundaryConditions
- **验证内容**:
  - 验证IPv6地址数量边界
  - 验证空IPv6地址列表的处理
  - 验证并发访问的安全性
- **测试场景**:
  - 最大IPv6地址数量
  - 空IPv6地址列表
  - 并发IPv6地址操作
- **期望结果**: 边界条件正确处理

## 测试环境要求
- **Go版本**: 1.18+
- **测试框架**: 使用Go标准测试框架
- **依赖包**: 
  - `encoding/json`
  - `context`
  - `bytes`
  - `icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce`
  - `icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/logger`

## 测试覆盖率要求
- **代码覆盖率**: ≥90%
- **分支覆盖率**: ≥85%
- **重点覆盖**: 新增的IPv6相关字段和逻辑

## 测试执行策略
1. **优先级**: 高优先级测试（结构体验证、接口集成）
2. **执行顺序**: 按测试类别顺序执行
3. **并发执行**: 支持并发测试执行
4. **模拟测试**: 使用模拟的百度云API响应进行测试
5. **失败处理**: 详细的错误信息和堆栈跟踪 