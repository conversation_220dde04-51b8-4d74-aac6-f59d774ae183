# 功能6.3-CNI双栈删除-端到端测试清单

## 概述
本文档列出了功能6.3-CNI双栈删除的端到端测试清单，主要验证CNI插件在实际Kubernetes环境中处理双栈Pod删除的完整流程。

## 测试环境要求
- Kubernetes集群（支持IPv6）
- 双栈网络配置的节点
- BCI资源控制器和CNI插件
- 双栈ENI资源

## 测试范围
- 双栈Pod完整生命周期（创建和删除）
- IPv4和IPv6网络配置清理
- 路由规则清理验证
- 资源泄漏检查

## 端到端测试清单

### 1. 双栈Pod删除流程测试

#### 1.1 标准双栈Pod删除测试
- **测试名称**: E2E_DualStack_Pod_Delete_Standard
- **测试目标**: 验证标准双栈Pod删除的完整流程
- **前置条件**:
  - 集群支持双栈网络
  - 存在可用的双栈ENI资源
  - Pod已成功创建并分配双栈地址
- **测试步骤**:
  1. 创建双栈Pod并验证网络配置正确
  2. 记录Pod的IPv4和IPv6地址
  3. 记录相关的路由规则和网络接口
  4. 删除Pod
  5. 验证网络资源清理完成
- **验证点**:
  - Pod的IPv4和IPv6地址被正确回收
  - 相关路由规则被完全删除
  - 网络接口被正确清理
  - 无网络资源泄漏
- **预期结果**: Pod删除成功，所有网络资源被正确清理

#### 1.2 多IP双栈Pod删除测试
- **测试名称**: E2E_DualStack_MultiIP_Pod_Delete
- **测试目标**: 验证多IP双栈Pod删除的完整流程
- **前置条件**:
  - 集群支持多IP分配
  - Pod已成功创建并分配多个IPv4和IPv6地址
- **测试步骤**:
  1. 创建需要多个IP的双栈Pod
  2. 验证所有IP地址分配成功
  3. 记录所有分配的IP地址和路由规则
  4. 删除Pod
  5. 验证所有网络资源清理完成
- **验证点**:
  - 所有IPv4和IPv6地址被回收
  - 所有相关路由表和规则被删除
  - 无IP地址泄漏
- **预期结果**: 多IP双栈Pod删除成功，所有IP和路由资源被清理

### 2. 路由规则清理验证测试

#### 2.1 自定义路由表清理测试
- **测试名称**: E2E_DualStack_RouteTable_Cleanup
- **测试目标**: 验证双栈Pod删除时自定义路由表的清理
- **前置条件**:
  - Pod使用需要独立路由表的配置
  - 已创建双栈Pod并分配独立路由表
- **测试步骤**:
  1. 创建需要独立路由表的双栈Pod
  2. 验证IPv4和IPv6路由表创建正确
  3. 记录路由表ID和规则
  4. 删除Pod
  5. 验证路由表和规则被完全清理
- **验证点**:
  - IPv4自定义路由表被删除
  - IPv6自定义路由表被删除
  - 系统路由表（main、local、default）保持不变
  - 无残留路由规则
- **预期结果**: 自定义路由表被完全清理，系统路由表未受影响

#### 2.2 容器路由规则清理测试
- **测试名称**: E2E_DualStack_ContainerRules_Cleanup
- **测试目标**: 验证容器相关路由规则的清理
- **前置条件**:
  - Pod已创建并配置容器路由规则
  - 存在to-container和from-container规则
- **测试步骤**:
  1. 创建双栈Pod并验证容器规则配置
  2. 检查IPv4和IPv6的to-container规则
  3. 检查IPv4和IPv6的from-container规则
  4. 删除Pod
  5. 验证所有容器规则被删除
- **验证点**:
  - IPv4 to-container规则被删除
  - IPv4 from-container规则被删除
  - IPv6 to-container规则被删除
  - IPv6 from-container规则被删除
- **预期结果**: 所有容器相关路由规则被正确清理

### 3. 网络接口清理测试

#### 3.1 veth接口清理测试
- **测试名称**: E2E_DualStack_VethInterface_Cleanup
- **测试目标**: 验证veth网络接口的清理
- **前置条件**:
  - Pod已创建并配置veth接口
  - veth接口配置了双栈地址
- **测试步骤**:
  1. 创建双栈Pod并验证veth接口创建
  2. 检查容器内和主机侧的veth接口
  3. 验证接口配置的IPv4和IPv6地址
  4. 删除Pod
  5. 验证veth接口被完全删除
- **验证点**:
  - 容器内veth接口被删除
  - 主机侧veth接口被删除
  - 接口相关的地址配置被清理
- **预期结果**: veth接口被完全清理，无残留配置

### 4. 异常场景处理测试

#### 4.1 网络命名空间提前消失测试
- **测试名称**: E2E_DualStack_Netns_Gone_Graceful
- **测试目标**: 验证网络命名空间提前消失时的处理
- **前置条件**:
  - Pod已创建并正常运行
  - 能够模拟网络命名空间消失
- **测试步骤**:
  1. 创建双栈Pod
  2. 手动删除Pod的网络命名空间
  3. 触发Pod删除流程
  4. 验证删除流程正常完成
- **验证点**:
  - CNI删除操作不报错
  - IP地址被正确回收
  - 主机侧网络配置被清理
- **预期结果**: 即使网络命名空间提前消失，删除流程也能正常完成

#### 4.2 部分路由规则删除失败测试
- **测试名称**: E2E_DualStack_PartialRuleCleanup_Recovery
- **测试目标**: 验证部分路由规则删除失败时的处理
- **前置条件**:
  - Pod已创建并配置路由规则
  - 能够模拟路由规则删除失败
- **测试步骤**:
  1. 创建双栈Pod
  2. 模拟部分路由规则删除失败
  3. 触发Pod删除流程
  4. 检查错误处理和日志记录
- **验证点**:
  - 错误被正确报告和日志记录
  - 能够成功删除的资源被清理
  - 系统状态保持一致
- **预期结果**: 错误被妥善处理，系统状态保持稳定

### 5. 性能和资源测试

#### 5.1 批量Pod删除性能测试
- **测试名称**: E2E_DualStack_Batch_Delete_Performance
- **测试目标**: 验证批量双栈Pod删除的性能
- **前置条件**:
  - 集群有足够资源支持批量Pod
  - 监控系统可用
- **测试步骤**:
  1. 批量创建双栈Pod（如50个）
  2. 验证所有Pod创建成功
  3. 批量删除所有Pod
  4. 监控删除过程的性能指标
- **验证点**:
  - 删除操作的平均耗时
  - 系统资源使用情况
  - 网络资源清理的完整性
- **预期结果**: 批量删除性能满足要求，无资源泄漏

#### 5.2 并发删除稳定性测试
- **测试名称**: E2E_DualStack_Concurrent_Delete_Stability
- **测试目标**: 验证并发删除双栈Pod的稳定性
- **前置条件**:
  - 集群支持并发操作
  - 有足够的双栈Pod可供测试
- **测试步骤**:
  1. 创建多个双栈Pod
  2. 并发触发多个Pod的删除操作
  3. 监控删除过程的稳定性
  4. 验证所有删除操作的结果
- **验证点**:
  - 并发删除操作不互相干扰
  - 所有Pod都被正确删除
  - 网络资源清理完整
  - 无竞态条件或死锁
- **预期结果**: 并发删除操作稳定可靠

### 6. 日志和监控验证

#### 6.1 删除过程日志完整性测试
- **测试名称**: E2E_DualStack_Delete_Logging_Complete
- **测试目标**: 验证删除过程日志记录的完整性
- **前置条件**:
  - 日志系统正常工作
  - 日志级别配置适当
- **测试步骤**:
  1. 创建双栈Pod
  2. 删除Pod并收集所有相关日志
  3. 分析日志内容的完整性
- **验证点**:
  - 包含IPv4和IPv6地址发现日志
  - 包含路由规则删除日志
  - 包含网络接口清理日志
  - 包含操作成功/失败的明确状态
- **预期结果**: 日志记录完整，便于问题排查

#### 6.2 错误场景日志测试
- **测试名称**: E2E_DualStack_Error_Logging_Clear
- **测试目标**: 验证错误场景的日志记录清晰度
- **前置条件**:
  - 能够触发各种错误场景
  - 日志系统正常工作
- **测试步骤**:
  1. 触发各种删除错误场景
  2. 收集和分析错误日志
  3. 验证日志的可读性和有用性
- **验证点**:
  - 错误信息清晰明确
  - 包含足够的上下文信息
  - 区分IPv4和IPv6相关错误
- **预期结果**: 错误日志清晰有用，便于问题诊断

## 测试执行环境
- Kubernetes版本: 1.20+
- 网络插件: BCI CNI
- 操作系统: Linux（支持IPv6）
- 测试框架: Ginkgo/Gomega

## 执行方式
```bash
# 执行双栈删除相关端到端测试
cd bci-cni-driver
./e2e-test.sh --focus="DualStack.*Delete"

# 执行特定测试用例
./e2e-test.sh --focus="E2E_DualStack_Pod_Delete_Standard"
```

## 验证工具
```bash
# 检查IP地址分配情况
ip addr show

# 检查路由规则
ip rule show
ip -6 rule show

# 检查路由表
ip route show table all
ip -6 route show table all

# 检查网络接口
ip link show

# 检查网络命名空间
ip netns list
```

## 注意事项
1. 测试环境必须支持IPv6，包括内核和网络配置
2. 需要监控系统资源，确保无泄漏
3. 测试用例需要覆盖各种Pod配置和网络场景
4. 错误场景测试需要谨慎，避免影响集群稳定性
5. 性能测试需要在代表性环境中执行
6. 日志分析需要考虑并发情况下的日志交错问题 