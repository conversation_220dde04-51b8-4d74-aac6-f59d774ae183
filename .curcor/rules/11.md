# 创建双栈Pod遇到问题
1. pod创建时分配的IPv4和IPv6分别为 *************  240c:4085:4:3101::5
2. 在节点上查看弹性网卡路由配置结果如下：
```
// 获取netns列表
ip netns list
enins-eni-q95s0rxv8rvn (id: 0)
cni-f7f0b348-d46b-cde4-d981-ef74b800a32a (id: 1)

// 查看弹性网卡ipv4路由规则
ip netns exec enins-eni-q95s0rxv8rvn ip route
default via *********** dev eth1 
***********/18 dev eth1 proto kernel scope link src ************* 
************* dev veth9f32028d scope host 

// 查看弹性网卡ipv6路由规则
ip netns exec enins-eni-q95s0rxv8rvn ip -6 route
240c:4085:4:3101::5 dev veth08d00c02 metric 1024 pref medium
240c:4085:4:3101::/64 dev eth1 proto kernel metric 256 pref medium
fe80::/64 dev eth1 proto kernel metric 256 pref medium
fe80::/64 dev veth08d00c02 proto kernel metric 256 pref medium
fe80::/64 dev veth9f32028d proto kernel metric 256 pref medium

// 查看容器网卡ipv4路由规则
ip netns exec cni-f7f0b348-d46b-cde4-d981-ef74b800a32a ip route
default via *************** dev eth0 src ************* metric 1000 
*************** dev eth0 scope link src ************* metric 1000 

// 查看容器网卡ipv6路由规则
ip netns exec cni-f7f0b348-d46b-cde4-d981-ef74b800a32a ip -6 route
240c:4085:4:3101::5 dev eth1 proto kernel metric 256 pref medium
fe80::/64 dev eth0 proto kernel metric 256 pref medium
fe80::/64 dev eth1 proto kernel metric 256 pref medium
febf:ffff:ffff:ffff:ffff:ffff:ffff:fffe dev eth1 src 240c:4085:4:3101::5 metric 1000 pref medium
default via febf:ffff:ffff:ffff:ffff:ffff:ffff:fffe dev eth1 src 240c:4085:4:3101::5 metric 1000 pref medium
```

请先分析上述结果是否正确，若不正确，请结合@bci-cni-driver 为Pod配置路由规则的实现逻辑，分析定位问题并修复