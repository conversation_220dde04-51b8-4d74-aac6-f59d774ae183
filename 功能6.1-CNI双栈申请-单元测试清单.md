# 功能6.1-CNI双栈申请-单元测试清单

## 功能概述
CNI插件双栈申请功能，支持同时处理IPv4和IPv6地址分配，确保双栈Pod能够获取到完整的网络配置。

## 测试范围
本测试清单覆盖CNI插件双栈申请功能的所有核心组件和关键路径。

## 单元测试清单

### 1. IP地址解析和验证测试 (cmd/plugins/bci-cni/main_test.go)

#### 1.1 IPv4地址解析测试
**测试文件**: `TestCmdAdd_IPv4AddressParsing`
- **测试目标**: 验证IPv4地址正确解析
- **测试场景**:
  - 单个IPv4地址解析
  - 多个IPv4地址解析
  - 无效IPv4地址格式处理
- **断言要点**:
  - IPv4地址正确解析到allocIPs列表
  - 无效格式返回适当错误
  - 多IP场景所有地址都被正确解析

#### 1.2 IPv6地址解析测试
**测试文件**: `TestCmdAdd_IPv6AddressParsing`
- **测试目标**: 验证IPv6地址正确解析和验证
- **测试场景**:
  - 有效IPv6地址解析
  - 无效IPv6地址格式
  - IPv4地址误传给IPv6字段
- **断言要点**:
  - IPv6地址正确解析
  - IPv4地址传给IPv6字段时返回错误
  - 无效IPv6格式返回适当错误

#### 1.3 双栈地址组合测试
**测试文件**: `TestCmdAdd_DualStackAddressParsing`
- **测试目标**: 验证IPv4和IPv6地址同时存在时的处理
- **测试场景**:
  - 单IPv4 + 单IPv6组合
  - 多IPv4 + 单IPv6组合
  - 仅IPv4地址（无IPv6）
- **断言要点**:
  - 双栈地址都能正确解析
  - 仅IPv4场景正常工作
  - 地址分类正确（IPv4/IPv6分别处理）

### 2. IPConfig构建测试

#### 2.1 IPv4 IPConfig构建测试
**测试文件**: `TestCmdAdd_IPv4IPConfigCreation`
- **测试目标**: 验证IPv4地址的IPConfig正确构建
- **测试场景**:
  - 单IPv4地址IPConfig
  - 多IPv4地址IPConfig
  - 网关配置
- **断言要点**:
  - IPv4地址使用/32掩码
  - 网关正确设置
  - 多IP时第一个IP加入retIPConfig

#### 2.2 IPv6 IPConfig构建测试
**测试文件**: `TestCmdAdd_IPv6IPConfigCreation`
- **测试目标**: 验证IPv6地址的IPConfig正确构建
- **测试场景**:
  - IPv6地址IPConfig创建
  - IPv6网关配置
  - 掩码验证
- **断言要点**:
  - IPv6地址使用/128掩码
  - IPv6Config正确添加到result.IPs
  - 网关配置正确

#### 2.3 双栈IPConfig聚合测试
**测试文件**: `TestCmdAdd_DualStackIPConfigAggregation`
- **测试目标**: 验证IPv4和IPv6 IPConfig正确聚合
- **测试场景**:
  - IPv4 + IPv6 IPConfig组合
  - result.IPs包含所有地址
  - retIPConfig仅包含主IPv4地址
- **断言要点**:
  - result.IPs包含所有IPv4和IPv6配置
  - retIPConfig仅包含第一个IPv4地址
  - 地址顺序正确

### 3. 路由规则配置测试

#### 3.1 IPv4路由规则测试
**测试文件**: `TestCmdAdd_IPv4RoutingRules`
- **测试目标**: 验证IPv4路由规则正确配置
- **测试场景**:
  - 需要独立路由表场景
  - addToContainerRule调用
  - addFromContainerRule调用
- **断言要点**:
  - 旧规则正确清理
  - to container规则正确添加
  - from container规则正确添加

#### 3.2 IPv6路由规则测试
**测试文件**: `TestCmdAdd_IPv6RoutingRules`
- **测试目标**: 验证IPv6路由规则正确配置
- **测试场景**:
  - IPv6地址存在时的路由配置
  - IPv6规则清理和添加
  - 路由表配置
- **断言要点**:
  - IPv6路由规则正确配置
  - 使用相同路由表号
  - 错误处理正确

#### 3.3 双栈路由规则测试
**测试文件**: `TestCmdAdd_DualStackRoutingRules`
- **测试目标**: 验证IPv4和IPv6路由规则同时配置
- **测试场景**:
  - IPv4和IPv6规则都被配置
  - 配置顺序正确
  - 失败时错误传播
- **断言要点**:
  - IPv4和IPv6规则都正确配置
  - 任何一个失败都能正确处理
  - 错误信息明确区分IPv4/IPv6

### 4. 错误处理测试

#### 4.1 IPAM响应错误测试
**测试文件**: `TestCmdAdd_IPAMResponseErrors`
- **测试目标**: 验证IPAM响应异常时的处理
- **测试场景**:
  - IPAM返回失败
  - 空响应处理
  - IPv6地址格式错误
- **断言要点**:
  - 错误正确传播
  - 清理函数被调用
  - 错误信息准确

#### 4.2 网络配置错误测试
**测试文件**: `TestCmdAdd_NetworkConfigurationErrors`
- **测试目标**: 验证网络配置失败时的处理
- **测试场景**:
  - setupContainerVeth失败
  - setupEniNsVeth失败
  - 路由规则配置失败
- **断言要点**:
  - 错误正确处理
  - 资源清理执行
  - 错误信息详细

#### 4.3 IPv6特定错误测试
**测试文件**: `TestCmdAdd_IPv6SpecificErrors`
- **测试目标**: 验证IPv6特定错误场景
- **测试场景**:
  - IPv6地址验证失败
  - IPv6路由配置失败
  - IPv6网络接口配置失败
- **断言要点**:
  - IPv6错误明确标识
  - 不影响IPv4功能
  - 错误恢复机制

### 5. 边界条件测试

#### 5.1 地址为空测试
**测试文件**: `TestCmdAdd_EmptyAddresses`
- **测试目标**: 验证地址为空时的处理
- **测试场景**:
  - IPv4地址为空
  - IPv6地址为空
  - 所有地址都为空
- **断言要点**:
  - 适当的错误处理
  - 不会导致panic
  - 错误信息准确

#### 5.2 特殊网络场景测试
**测试文件**: `TestCmdAdd_SpecialNetworkScenarios`
- **测试目标**: 验证特殊网络配置场景
- **测试场景**:
  - 不需要独立路由表
  - MTU自动检测
  - 网络命名空间异常
- **断言要点**:
  - 所有场景正确处理
  - 默认行为符合预期
  - 优雅降级

### 6. 性能测试

#### 6.1 地址处理性能测试
**测试文件**: `BenchmarkCmdAdd_AddressProcessing`
- **测试目标**: 验证地址处理性能
- **测试场景**:
  - 大量IPv4地址处理
  - IPv4+IPv6组合处理
  - 内存分配优化
- **断言要点**:
  - 处理时间在合理范围
  - 内存使用优化
  - 无内存泄露

## 覆盖率目标
- **目标覆盖率**: 85%+
- **关键路径覆盖率**: 95%+
- **错误处理覆盖率**: 90%+

## 测试数据准备
- Mock IPAM响应（IPv4单地址、多地址、IPv6地址、双栈）
- Mock 网络接口
- Mock netlink操作
- 测试用的CNI配置

## 测试环境要求
- Go 1.19+
- Mock框架支持
- 网络命名空间权限
- netlink操作权限 