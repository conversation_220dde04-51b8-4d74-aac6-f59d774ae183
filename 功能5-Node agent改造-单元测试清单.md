# 功能5-Node agent改造-单元测试清单

## 1. 测试覆盖目标
- 验证IPAM增强支持双栈IP分配功能
- 测试IPv6地址池管理功能
- 验证IPv6地址分配和释放逻辑
- 测试Pod IPv6 annotation patch功能
- 验证双栈IP分配的完整流程

## 2. 测试文件定位
- **主要测试文件**: `pkg/nodeagent/ipam/ipam_test.go`
- **辅助测试文件**: `pkg/nodeagent/ipam/ipam_helper_test.go`

## 3. 测试用例详细清单

### 3.1 基础IPv6支持测试

#### 3.1.1 IPv6地址池管理测试
```go
func TestIPAM_IPv6UserIdlePool(t *testing.T) {
    // 测试场景1：IPv6地址池初始化
    // 测试场景2：IPv6地址池添加和删除
    // 测试场景3：IPv6地址池重建
    // 测试场景4：IPv6地址池查找功能
}
```

#### 3.1.2 IPv6地址检查测试
```go
func TestIPAM_isIPv6Allocated(t *testing.T) {
    // 测试场景1：IPv6地址已分配情况
    // 测试场景2：IPv6地址未分配情况
    // 测试场景3：IPv6地址池为空情况
    // 测试场景4：用户或ENI不存在情况
}
```

### 3.2 IPv6地址分配测试

#### 3.2.1 Pod IPv6需求检查测试
```go
func TestIPAM_podRequiresIPv6(t *testing.T) {
    // 测试场景1：Pod有IPv6 annotation且为true
    // 测试场景2：Pod有IPv6 annotation但为false
    // 测试场景3：Pod没有IPv6 annotation
    // 测试场景4：Pod annotation为空
    // 测试场景5：Pod不存在情况
}
```

#### 3.2.2 IPv6地址分配测试
```go
func TestIPAM_getIPv6AndMarkUsed(t *testing.T) {
    // 测试场景1：正常IPv6地址分配
    // 测试场景2：Pod已有IPv6地址分配
    // 测试场景3：IPv6地址池为空
    // 测试场景4：IPv6地址标记失败重试
    // 测试场景5：IPv6地址分配失败
}
```

#### 3.2.3 IPv6地址弹出测试
```go
func TestIPAM_popIPv6(t *testing.T) {
    // 测试场景1：成功弹出IPv6地址
    // 测试场景2：IPv6地址池为空
    // 测试场景3：IPv6地址池初始化
    // 测试场景4：IPv6地址池过滤功能
}
```

### 3.3 IPv6地址标记测试

#### 3.3.1 IPv6地址标记为已使用测试
```go
func TestIPAM_markIPv6Used(t *testing.T) {
    // 测试场景1：成功标记IPv6地址
    // 测试场景2：IPv6地址已被使用
    // 测试场景3：用户或ENI不存在
    // 测试场景4：BciNode为空
    // 测试场景5：批量标记IPv6地址
}
```

#### 3.3.2 IPv6地址标记为未使用测试
```go
func TestIPAM_markIPv6Unused(t *testing.T) {
    // 测试场景1：成功释放IPv6地址
    // 测试场景2：Pod没有IPv6地址
    // 测试场景3：IPv6地址池回收
    // 测试场景4：用户或ENI不存在
    // 测试场景5：BciNode为空
}
```

### 3.4 IPv6 Annotation管理测试

#### 3.4.1 IPv6 Annotation Patch测试
```go
func TestIPAM_PatchBciInternalIPv6(t *testing.T) {
    // 测试场景1：成功patch IPv6 annotation
    // 测试场景2：删除IPv6 annotation
    // 测试场景3：Pod不存在
    // 测试场景4：IPv6地址为空
    // 测试场景5：Kubernetes API调用失败
}
```

#### 3.4.2 IPv6 Annotation验证测试
```go
func TestIPAM_checkPodIPv6HasAllocated(t *testing.T) {
    // 测试场景1：Pod已分配IPv6地址
    // 测试场景2：Pod未分配IPv6地址
    // 测试场景3：IPv6地址池为空
    // 测试场景4：用户或ENI不存在
}
```

### 3.5 双栈IP分配集成测试

#### 3.5.1 AllocateIP增强测试
```go
func TestIPAM_AllocateIP_DualStack(t *testing.T) {
    // 测试场景1：仅分配IPv4地址
    // 测试场景2：分配IPv4和IPv6地址
    // 测试场景3：IPv6分配失败但IPv4成功
    // 测试场景4：IPv6 annotation patch失败
    // 测试场景5：Pod不需要IPv6地址
}
```

#### 3.5.2 ReleaseIP增强测试
```go
func TestIPAM_ReleaseIP_DualStack(t *testing.T) {
    // 测试场景1：同时释放IPv4和IPv6地址
    // 测试场景2：仅释放IPv4地址
    // 测试场景3：IPv6释放失败但IPv4成功
    // 测试场景4：IPv6 annotation清理失败
    // 测试场景5：Pod已释放情况
}
```

### 3.6 地址池管理增强测试

#### 3.6.1 地址池更新测试
```go
func TestIPAM_UpdateUserIdlePool_IPv6(t *testing.T) {
    // 测试场景1：IPv6地址池正常更新
    // 测试场景2：IPv6地址池添加和删除
    // 测试场景3：IPv6地址去重处理
    // 测试场景4：IPv6地址池日志输出
}
```

#### 3.6.2 地址池重建测试
```go
func TestIPAM_RebuildUserIdlePool_IPv6(t *testing.T) {
    // 测试场景1：IPv6地址池重建
    // 测试场景2：IPv6已分配地址过滤
    // 测试场景3：IPv6地址池初始化
    // 测试场景4：IPv6地址池日志输出
}
```

### 3.7 过滤器功能测试

#### 3.7.1 IPv6可用性过滤测试
```go
func TestIPAM_usableIPv6Filter(t *testing.T) {
    // 测试场景1：IPv6地址可用
    // 测试场景2：IPv6地址不可用
    // 测试场景3：IPv6地址在等待释放状态
    // 测试场景4：无效PayLoad处理
}
```

## 4. 边界情况测试

### 4.1 错误处理测试
```go
func TestIPAM_IPv6ErrorHandling(t *testing.T) {
    // 测试场景1：BciNode为空
    // 测试场景2：网络错误处理
    // 测试场景3：并发访问测试
    // 测试场景4：资源不足处理
}
```

### 4.2 性能测试
```go
func TestIPAM_IPv6Performance(t *testing.T) {
    // 测试场景1：大量IPv6地址分配
    // 测试场景2：大量IPv6地址释放
    // 测试场景3：地址池并发访问
    // 测试场景4：内存使用情况
}
```

## 5. 测试数据准备

### 5.1 Mock数据定义
```go
// IPv6测试Pod
func createIPv6Pod() *corev1.Pod {
    return &corev1.Pod{
        ObjectMeta: metav1.ObjectMeta{
            Name:      "test-ipv6-pod",
            Namespace: "default",
            Annotations: map[string]string{
                "bci.baidu.com/bci-enable-ipv6": "true",
            },
        },
        Spec: corev1.PodSpec{
            Containers: []corev1.Container{
                {
                    Name:  "test-container",
                    Image: "nginx:alpine",
                },
            },
        },
    }
}

// IPv6地址池数据
func createIPv6AllocationMap() networkingv1.AllocationMap {
    return networkingv1.AllocationMap{
        "test-user": networkingv1.UserAllocationEnis{
            "eni-12345": &networkingv1.AllocationEni{
                MacAddress: "02:42:ac:11:00:02",
                PrivateIPv6Addresses: map[string]networkingv1.AllocationIP{
                    "2001:db8::1": {
                        UserID: "test-user",
                        EniID:  "eni-12345",
                    },
                    "2001:db8::2": {
                        UserID: "test-user",
                        EniID:  "eni-12345",
                    },
                },
            },
        },
    }
}
```

## 6. 测试覆盖率要求

### 6.1 代码覆盖率目标
- **整体覆盖率**: ≥85%
- **核心函数覆盖率**: ≥90%
- **分支覆盖率**: ≥80%

### 6.2 功能覆盖率目标
- **IPv6地址分配**: 100%
- **IPv6地址释放**: 100%
- **IPv6 annotation管理**: 100%
- **双栈IP分配**: 100%
- **地址池管理**: 100%

## 7. 测试执行计划

### 7.1 测试阶段
1. **单元测试**: 各功能模块独立测试
2. **集成测试**: 双栈IP分配完整流程测试
3. **性能测试**: 大量并发场景测试
4. **回归测试**: 现有功能兼容性测试

### 7.2 测试环境要求
- Go 1.19+
- Kubernetes 1.20+
- Mock框架支持
- 测试数据准备工具

## 8. 成功标准

### 8.1 功能验证
- [x] IPv6地址池管理功能正常
- [x] IPv6地址分配和释放正常
- [x] IPv6 annotation patch功能正常
- [x] 双栈IP分配流程正常
- [x] 现有IPv4功能不受影响

### 8.2 质量标准
- [x] 所有测试用例通过
- [x] 代码覆盖率达标
- [x] 性能指标满足要求
- [x] 错误处理完善
- [x] 日志输出清晰

## 9. 风险评估

### 9.1 技术风险
- **并发安全**: IPv6地址池并发访问
- **内存使用**: 双栈地址池内存占用
- **性能影响**: IPv6功能对现有性能的影响

### 9.2 缓解措施
- **锁机制**: 确保地址池操作的线程安全
- **内存监控**: 监控地址池内存使用情况
- **性能测试**: 全面的性能回归测试 