---
type: "manual"
---

# 创建双栈Pod遇到问题
1. pod创建时分配的IPv4和IPv6分别为 *************  240c:4085:4:3101::2
2. 在节点上查看弹性网卡路由配置结果如下：
```
// 获取netns列表
ip netns list
enins-eni-4mafhhrt8h6c (id: 0)
cni-43724e60-39d3-cd96-79e8-6f80f580ff11 (id: 1)

// 查看弹性网卡ipv4路由规则
ip netns exec enins-eni-4mafhhrt8h6c ip route
default via *********** dev eth1 
***********/18 dev eth1 proto kernel scope link src ************* 
************* dev veth4809ccdf scope host 

// 查看弹性网卡ipv6路由规则
ip netns exec enins-eni-4mafhhrt8h6c ip -6 route
240c:4085:4:3101::2 dev vetheb525681 metric 1024 pref medium
fe80::/64 dev eth1 proto kernel metric 256 pref medium
fe80::/64 dev vetheb525681 proto kernel metric 256 pref medium
fe80::/64 dev veth4809ccdf proto kernel metric 256 pref medium

// 查看容器网卡ipv4路由规则
ip netns exec cni-43724e60-39d3-cd96-79e8-6f80f580ff11 ip route
default via *************** dev eth0 src ************* metric 1000 
*************** dev eth0 scope link src ************* metric 1000 

// 查看容器网卡ipv6路由规则
ip netns exec cni-43724e60-39d3-cd96-79e8-6f80f580ff11 ip -6 route
240c:4085:4:3101::2 dev eth1 proto kernel metric 256 pref medium
fe80::/64 dev eth0 proto kernel metric 256 pref medium
fe80::/64 dev eth1 proto kernel metric 256 pref medium
```

请先分析上述结果是否正确，若不正确，请结合@bci-cni-driver 为Pod配置路由规则的实现逻辑，分析定位问题并修复