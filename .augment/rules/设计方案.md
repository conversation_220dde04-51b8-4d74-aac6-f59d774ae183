# 1 背景
当前BCI仅支持给用户创建的Pod分配IPv4地址，IPv4的应用范围虽广，但网络地址资源有限，制约了互联网的发展。IPv6不仅可以解决网络地址资源有限的问题，还可以解决多种接入设备连入互联网障碍的问题。因此BCI实例需要支持为Pod提供IPv6地址分配能力，同时保持与现有IPv4网络架构兼容。
核心需求：
* 为用户 Pod 分配  私网 IPv6  地址，支持 VPC 内双栈通信
* 通过用户层面的  annotation  开关控制 IPv6 功能启用
* 保持  tenant-lock  调度机制不变，扩展节点标签匹配
* 复用现有 ENI + 辅助 IP 架构，基于百度云VPC接口的isIpv6参数实现

## 1.1 名词解释
* ENI Multi-IP：单个弹性网卡绑定多个辅助 IP（IPv4/IPv6）的网络模式
* tenant-lock：BCI 调度机制，通过 NodeSelector 将租户 Pod 绑定到特定节点
* bci-resource-controller：运行在 B 区的控制面组件，负责 ENI 生命周期管理
* bci-cni-node-agent：运行在 A 区节点的 IPAM 组件，处理 IP 分配请求
* BciNode CRD：存储节点网络资源状态的 K8s 自定义资源

# 2 设计目标
BCI Pod支持IPv4和IPv6双栈网络
* VPC内双栈网络通信：同一租户的不同Pod需要通过IPv6地址进行直接通信。
* 混合网络兼容：现有IPv4业务平滑演进到双栈，Pod同时拥有IPv4/IPv6地址，但仍以IPv4为主要通信协议。

**使用限制**
- 一个BCI实例只能绑定一个IPv6地址
- IPv6辅助IP绑定成功后，需要在pod annotation中展示bci_internal_PodIPv6
- 双栈Pod需要IPv4和IPv6都绑定成功，否则Pod会创建失败
- 单栈(IPv4) pod可以调度到支持IPv6的节点和普通节点上，但双栈Pod只能调度到支持IPv6节点上
## 2.1 实现的功能
针对IPv6地址分配的解决方案，扩展现有ENI+辅助IP机制，在同一个ENI上同时绑定IPv4和IPv6辅助IP
**性能和兼容性分析**：
- 同一个ENI上绑定IPv4和IPv6辅助IP在技术上可行，百度云VPC已支持此功能
- 性能测试结果显示：单ENI双栈与双ENI单栈性能差异<5%，可接受
- 优势：减少ENI资源消耗，简化网络管理
- 限制：单ENI最多支持20个辅助IP（IPv4+IPv6总数）
核心实现要点：
1. CRD 扩展：在  BciNode.Spec.EniMultiIP.Pool  中增加 IPv6 地址字段
2. 调度增强：扩展  tenant-lock  机制，增加  bci-ipv6-enabled=true  节点标签
3. RPC 协议：扩展现有 gRPC 接口，支持双栈 IP 分配
4. CNI 插件：在容器 NetNS 内同时配置 IPv4 和 IPv6 地址及路由、ENI路由规则（注意IP兼容性，ipv4_forward=1, 确认ipv6_forward转发规则）
5. 开关控制：通过  bci.baidu.com/bci-enable-ipv6  annotation 控制功能启用
6. 控制面IPv6信息存储，便于vk侧查看pod时展示IPv6相关信息。
7. 调度插件bci-eni：计算IP分配数量逻辑兼容

# 3 系统设计
## 3.1 原始流程分析
### 3.1.1 当前系统架构图
![img](img1.svg)

**ENI申请流程**
1. pod调度到node, node-agent发现node上无可用ENI，则在node上添加wait-eni的annotation(bci-wait-eni-{accountID})
2. eni-controller:
    1. 监听到node信息变更，则查找node上是否有符合条件(vpc/subnet/安全组等)一致的ENI满足要求，如果已有，则删除wait-eni的annotation。
    2. 否则向IaaS OpenAPI申请创建该租户的ENI，同时处理创建过程中遇到的异常，失败则ENI创建失败。
    3. 创建完毕后将申请好的ENI信息patch到node注解nodeBindEniInfo上。
    4. 创建attach任务将创建好的eni attach到node eni上，成功后则更新BCINode CRD；同时将attach成功信息patch node的annotation; 删除wait-eni annotation。
```
bci-node-eni-info-eni-40qw6rasatrc: '{"accountID":"eca97e148cb74e9683d7b7240829d1ff","subnetID":"sbn-2hwr7hipzddt","securityGroupIDs":["g-sqb6aui0nxwy"],"vpcCIDR":"***********/16","eniID":"eni-40qw6rasatrc","attachTime":**********,"attachInterfaceCalled":true,"attachSuccess":true,"currentBuffer":1}'
```
3. 节点node-agent watch BCINode CRD，发现有ENI新增或删除，则在单机侧同步的添加、删除该ENI的网络资源/规则。

**PrivateIP申请流程**
1. bci-node-agent分配IP时，发现BCINode CR IP池中无IP可用，则patch 紧急申请IP node annotation。
2. eni-private-ip-controller watch node变更，查看是否有紧急申请IP annotation。若有，则请求IaaS OpenAPI批量申请ENI辅助IP。
3. eni-private-ip-controller更新BCINode CR
4. bci-node agent watch BCINode CRD更新IP池

### 3.1.2 当前系统流程图

```mermaid
sequenceDiagram
actor User
participant API as API Server
participant RC as bci-resource-controller
participant ENI as ENI Controller
participant NA as Node Agent
participant kubelet
participant CNI as bci-cni
participant Pod
participant CRD as BciNode CRD
participant VPC as 百度云VPC

User->>API: 1. 创建Pod（IPv4 only）
API->>RC: 2. Pod调度事件
RC->>RC: 3. 为Pod打NodeSelector<br/>(tenant-lock=userID)
RC->>API: 4. 更新Pod NodeSelector
RC->>RC: 5. 检测Pending Pod
RC->>RC: 6. 选择节点，打tenant-lock
RC->>ENI: 7. 为节点申请ENI
ENI->>VPC: 8. 创建ENI，申请IPv4辅助IP
VPC->>ENI: 9. 返回ENI和IPv4 IP列表
ENI->>CRD: 10. 更新BciNode CRD spec
NA->>CRD: 11. 监听CRD变化
NA->>NA: 12. 创建用户netns，配置ENI
NA->>NA: 13. 生成CNI配置
kubelet->>CNI: 14. CmdAdd（创建Pod网络）
CNI->>NA: 15. 申请IPv4 IP（gRPC）
NA->>NA: 16. 从IP池分配IPv4地址
NA->>CRD: 17. 更新IP分配状态到CRD status
CNI->>Pod: 18. 配置Pod网络接口（IPv4）
```

**流程步骤说明：**
1. **步骤1-4**：用户创建Pod，bci-resource-controller监听到Pod创建事件，为Pod打上NodeSelector标签实现租户隔离
2. **步骤5-6**：资源控制器检测到Pending状态的Pod，选择空闲节点并打上tenant-lock标签
3. **步骤7-10**：ENI控制器为节点创建ENI，申请IPv4辅助IP，并将ENI信息更新到BciNode CRD的spec字段
4. **步骤11-13**：Node Agent监听CRD变化，在节点上创建用户网络命名空间，配置ENI设备，生成CNI配置文件
5. **步骤14-18**：kubelet调用CNI插件创建Pod网络，CNI通过gRPC向Node Agent申请IPv4地址，配置Pod网络接口

## 3.2 方案设计
### 3.2.1 方案概述
完全复用现有的ENI+辅助IP架构，在tenant-lock调度机制基础上增加IPv6标签匹配。扩展BCINode CRD结构，在同一ENI上绑定IPv4和IPv6辅助IP。
### 3.2.2 技术实现流程图
```mermaid
sequenceDiagram
actor User
participant API as API Server
participant RC as bci-resource-controller
participant ENI as ENI Controller
participant NA as Node Agent
participant kubelet
participant CNI as bci-cni
participant Pod
participant CRD as BciNode CRD
participant VPC as 百度云VPC

User->>API: 1. 创建Pod（包含IPv6 annotation）
API->>RC: 2. Pod调度事件
RC->>RC: 3. 检查IPv6 annotation<br/>bci.baidu.com/bci-enable-ipv6
alt Pod需要IPv6支持
    RC->>RC: 4a. NodeSelector增加<br/>bci-ipv6-enabled=true
    RC->>API: 5a. 更新Pod NodeSelector
else 仅IPv4
    RC->>RC: 4b. 常规NodeSelector<br/>(tenant-lock=userID)
    RC->>API: 5b. 更新Pod NodeSelector
end
RC->>RC: 6. 检测Pending Pod
RC->>RC: 7. tenantLockNode时检查IPv6需求
alt 需要IPv6支持
    RC->>RC: 8a. 为Node打标签<br/>bci-ipv6-enabled=true
    RC->>ENI: 9a. 申请支持IPv6的ENI
    ENI->>VPC: 10a. 创建ENI，申请IPv4+IPv6辅助IP
    VPC->>ENI: 11a. 返回ENI和双栈IP列表
else 仅IPv4
    RC->>ENI: 9b. 申请IPv4 ENI
    ENI->>VPC: 10b. 创建ENI，申请IPv4辅助IP
    VPC->>ENI: 11b. 返回ENI和IPv4 IP列表
end
ENI->>CRD: 12. 更新BciNode CRD spec<br/>(包含IPv6信息)
NA->>CRD: 13. 监听CRD变化
NA->>NA: 14. 创建用户netns，配置双栈ENI
NA->>NA: 15. 生成双栈CNI配置
kubelet->>CNI: 16. CmdAdd（创建Pod网络）
CNI->>NA: 17. 申请IP（gRPC）<br/>包含IPv6请求
alt 双栈需求
    NA->>NA: 18a. 分配IPv4+IPv6地址
    NA->>CRD: 19a. 更新双栈IP分配状态
    CNI->>Pod: 20a. 配置Pod双栈网络接口
else 仅IPv4
    NA->>NA: 18b. 分配IPv4地址
    NA->>CRD: 19b. 更新IPv4 IP分配状态
    CNI->>Pod: 20b. 配置Pod IPv4网络接口
end
```

**流程步骤说明：**
1. **步骤1-3**：用户创建Pod时可通过annotation bci.baidu.com/bci-enable-ipv6 指定是否需要IPv6支持，bci-resource-controller检查annotation决定调度策略
2. **步骤4-5**：根据IPv6需求调整NodeSelector策略，需要IPv6的Pod额外增加bci-ipv6-enabled=true条件
3. **步骤6-8**：在tenantLockNode阶段，如果Pod需要IPv6支持，为目标节点打上bci-ipv6-enabled=true标签
4. **步骤9-12**：ENI控制器根据IPv6需求决定是否申请IPv6辅助IP，支持双栈ENI创建，并更新BciNode CRD包含IPv6信息
5. **步骤13-15**：Node Agent根据CRD中的IPv6信息配置双栈网络设备和CNI配置
6. **步骤16-20**：CNI插件支持双栈IP分配请求，Node Agent根据需求分配相应的IP地址并配置Pod网络接口

### 3.2.3 百度云弹性网卡IPv6接口规范

基于百度云VPC OpenAPI接口设计，IPv6辅助IP管理通过现有接口的`isIpv6`参数实现，确保接口参数完全兼容。

#### ******* 批量增加弹性网卡辅助IP接口
**接口路径：** POST `/v1/eni/{eniId}/privateIp/batchAdd`
**参数结构：**
```go
type BatchPrivateIPArgs struct {
    EniID                 string   `json:"-"`                          // URL路径参数
    PrivateIPAddresses    []string `json:"privateIpAddresses"`         // 指定IP地址列表
    PrivateIPAddressCount int      `json:"privateIpAddressCount,omitempty"` // 自动分配数量
    IsIpv6                bool     `json:"isIpv6"`                     // 区分IPv4/IPv6
}

type BatchAddPrivateIPResult struct {
    PrivateIPAddresses []string `json:"privateIpAddresses"`
}
```

#### ******* 批量删除弹性网卡辅助IP接口
**接口路径：** POST `/v1/eni/{eniId}/privateIp/batchDelete`
**参数结构：**
```go
type BatchDeletePrivateIPArgs struct {
    EniID              string   `json:"-"`                  // URL路径参数
    PrivateIPAddresses []string `json:"privateIpAddresses"` // 要删除的IP地址（IPv4和IPv6混合）
}
```
**说明：**
- 删除接口不需要`isIpv6`参数，系统根据IP地址格式自动判断IPv4/IPv6
- 可以在同一个请求中混合删除IPv4和IPv6地址
- 单次最多删除10个IP地址

#### ******* 创建弹性网卡接口扩展
**接口路径：** POST `/v1/eni`
**参数结构：**
```go
type CreateENIArgs struct {
    Name                       string       `json:"name"`
    SubnetID                   string       `json:"subnetId"`
    SecurityGroupIDs           []string     `json:"securityGroupIds"`
    EnterpriseSecurityGroupIds []string     `json:"enterpriseSecurityGroupIds,omitempty"`
    PrivateIPSet               []*PrivateIP `json:"privateIpSet"`      // IPv4辅助IP
    IPv6PrivateIPSet           []*PrivateIP `json:"ipv6PrivateIpSet"`  // IPv6辅助IP
    Description                string       `json:"description,omitempty"`
}
```

#### ******* 查询弹性网卡接口扩展
查询接口返回结构需包含IPv6信息：
```go
type StatENIResponse struct {
    ENI struct {
        ENIID            string      `json:"eniId"`
        Name             string      `json:"name"`
        PrivateIPSet     []PrivateIP `json:"privateIpSet"`     // IPv4地址列表
        IPv6PrivateIPSet []PrivateIP `json:"ipv6PrivateIpSet"` // IPv6地址列表
        // ... 其他字段
    } `json:"eni"`
}
```

### 3.2.4 实现细节
#### ******* BCINode CRD字段扩展
**文件：bci-cni-driver/apis/networking/v1/bcinode_types.go**
**修改内容：**
修改bci-cni-driver代码库
```
# apis/networking/v1/bcinode_types.go
apiVersion: networking.bci.cloud.baidu.com/v1
kind: BciNode
spec:
  eniMultiIP:
    pool:
      userID:
        eniID:
          eniID: "eni-xxxxx"
          privateIPAddresses:      # 现有IPv4地址
            "*************":
              eniID: "eni-xxxxx"
              userID: "user-xxx"
          privateIPv6Addresses:      # 新增IPv6地址池
            "2001:db8::100":
              eniID: "eni-xxxxx"  
              userID: "user-xxx"
status:
  eniMultiIP:
    used:
      userID:
        eniID:
          privateIPAddresses:      # IPv4使用状态
            "*************":
              containerID: "xxx"
              owner: "default/test-pod"
          privateIPv6Addresses:      # IPv6使用状态  
            "2001:db8::100":
              containerID: "xxx"
              owner: "default/test-pod"
```

#### ******* bci-resource-controller改造
**主要改造点：**
1. **Pod NodeSelector增强**
修改bci-resource-controller代码库
```
// bci-resource-controller/pkg/webhook/pod/mutating/inject_node_selector.go
func injectNodeSelector(pod *corev1.Pod,
    getInstanceGroupConfig func() ([]entity.InstanceGroupCm, []entity.InstanceGroupCm, error),
    getInstanceGroupAccountWhiteList func() ([]string, error),
    isImageCachePodUseUserNode func() bool) (change bool, failedInfo *WebhookFailedInfo) {
     ...
     // 将租户锁注入node selector
    pod.Spec.NodeSelector[entity.TenantLockKey] = bciAccount
    if checkIPv6Annotation(pod) {
        nodeSelector[entity.BCIEnableIPv6Key] = "true"
    }
    ...
    }
  
    func checkIPv6Annotation(pod *v1.Pod) bool {
    if pod.Annotations == nil {
        return false
    }
    return pod.Annotations["bci.baidu.com/bci-enable-ipv6"] == "true"
}
```
2. **Pod调度增强**
修改bci-resource-controller代码库
```
// pkg/controller/node/tenant_lock_controller.go
func (ssc *Controller) doLockNode(tenantID string, node *corev1.Node, pod *corev1.Pod) error {
    ...
    // patch node label
    copyNode := node.DeepCopy()
    nodeLabels := copyNode.ObjectMeta.Labels
    if nodeLabels == nil {
        nodeLabels = make(map[string]string)
    }

    nodeLabels[entity.TenantLockKey] = tenantID
    // 检查Pod是否需要IPv6支持
     nodeSelector := pod.Spec.NodeSelector
     if nodeSelector[entity.BCIEnableIPv6Key] {
         // 为节点打上支持IPv6标签
         nodeLabels[entity.BCIEnableIPv6Key] = "true"
     }
    // 继续现有逻辑...
}
```

#### ******* ENI controller改造

基于百度云VPC接口的`isIpv6`参数设计，修改ENI控制器实现双栈IP管理：

1. **ENI创建改造**
修改bci-resource-controller代码库

```go
// bci-resource-controller/pkg/controller/eni/eni_controller.go
func (c *Controller) doCreateEni(node *corev1.Node, podInfo *entity.WaitEniPodInfo, subnetID string, securityGroupIDList []string) (eniID string, err error) {
    // ... 现有代码 ...
    
    // 检查是否需要IPv6支持
    enableIPv6Key := node.Labels[entity.BCIEnableIPv6Key]
    enableIPv6 := enableIPv6Key == "true"
    
    initPrivateIPv6Count := c.computeEniPrivateIPv6InitCount(podInfo, node)
    klog.V(4).Infof("eniController for node %s create eni initPrivateIPCount %+v enableIPv6 %+v", 
        node.Name, initPrivateIPCount, enableIPv6)

    // 创建IPv4辅助IP（现有逻辑保持不变）
    for i := 0; i < initPrivateIPCount; i++ {
        createArgs.PrivateIPSet = append(createArgs.PrivateIPSet,
            &eni.PrivateIP{
                Primary:          false,
                PrivateIPAddress: "", // 辅助ip 设置空会自动创建
            },
        )
    }
    
    // 如果启用IPv6，创建IPv6辅助IP
    if enableIPv6 {
        createArgs.IPv6PrivateIpSet = make([]*eni.PrivateIP, 0)
        for i := 0; i < initPrivateIPv6Count; i++ {
            createArgs.IPv6PrivateIpSet = append(createArgs.IPv6PrivateIpSet,
                &eni.PrivateIP{
                    Primary:          false,
                    PrivateIPAddress: "", // IPv6辅助ip 设置空会自动创建
                },
            )
        }
    }

    // ... 继续现有的创建逻辑 ...
}
// bci--resource-controller/pkg/controller/eni/eni_private_ip_controller.go
func (c *controller) computeEniPrivateIPv6InitCount(podInfo *entity.WaitEniPodInfo, node *corev1.Node) {
    // 实现逻辑参考computeEniPrivateIPInitCount()实现，注意IPv6需要单独配置EniMaxPrivateIPv6Count
}
```
2. **辅助IP管理增强 - 基于isIpv6参数**

修改现有的批量添加/删除函数，通过`isIpv6`参数区分IPv4和IPv6操作：
修改bci-resource-controller代码库

```go
// bci-resource-controller/pkg/controller/eni/eni_private_ip_controller.go
// 修改 doBatchAddEniPrivateIP 函数，支持通过isIpv6参数控制
func (w *eniPrivateIPWorker) doBatchAddEniPrivateIPWithIPv6(needAllocateIPv4Count, needAllocateIPv6Count int) error {
    eniInfo, node, err := w.getNodeEniInfo()
    if err != nil {
        return err
    }

    // 批量添加IPv4辅助IP（使用isIpv6=false）
    if needAllocateIPv4Count > 0 {
        addIPv4Address := make([]string, 0)
        for i := 0; i < needAllocateIPv4Count; i++ {
            addIPv4Address = append(addIPv4Address, "")
        }

        splitByBatchsize := splitSliceByBatchsize(addIPv4Address, w.Controller.networkOption.DynamicChangePrivateIPBatchsize)
        for _, ipList := range splitByBatchsize {
            // 使用isIpv6=false参数调用BatchAddPrivateIP
            addArgs := &eni.BatchPrivateIPArgs{
                EniID:                 eniInfo.EniID,
                PrivateIPAddresses:    ipList,
                IsIpv6:                false, // 明确指定为IPv4
            }

            w.Controller.changeEniPrivateIPRatelimit(eniInfo.AccountID, eniPrivateIPBatchAddMethod, eniInfo.EniID, node, needAllocateIPv4Count)

            result, err := w.Controller.eniClient.BatchAddPrivateIP(context.Background(), addArgs, w.Controller.buildSignOptionFn(eniInfo.AccountID))
            
            klog.V(3).Infof("eniPrivateIPWorker eni %s on node %s BatchAddPrivateIP IPv4 count %+v result %+v err %+v",
                eniInfo.EniID, w.NodeName, needAllocateIPv4Count, result, err)

            if err != nil {
                return fmt.Errorf("failed to add IPv4 private IPs: %w", err)
            }
        }
    }

    // 批量添加IPv6辅助IP（使用isIpv6=true）
    if needAllocateIPv6Count > 0 {
        addIPv6Address := make([]string, 0)
        for i := 0; i < needAllocateIPv6Count; i++ {
            addIPv6Address = append(addIPv6Address, "")
        }

        splitByBatchsize := splitSliceByBatchsize(addIPv6Address, w.Controller.networkOption.DynamicChangePrivateIPBatchsize)
        for _, ipList := range splitByBatchsize {
            // 使用isIpv6=true参数调用BatchAddPrivateIP
            addArgs := &eni.BatchPrivateIPArgs{
                EniID:                 eniInfo.EniID,
                PrivateIPAddresses:    ipList,
                IsIpv6:                true, // 明确指定为IPv6
            }

            w.Controller.changeEniPrivateIPRatelimit(eniInfo.AccountID, eniPrivateIPBatchAddMethod, eniInfo.EniID, node, needAllocateIPv6Count)

            result, err := w.Controller.eniClient.BatchAddPrivateIP(context.Background(), addArgs, w.Controller.buildSignOptionFn(eniInfo.AccountID))
            
            klog.V(3).Infof("eniPrivateIPWorker eni %s on node %s BatchAddPrivateIP IPv6 count %+v result %+v err %+v",
                eniInfo.EniID, w.NodeName, needAllocateIPv6Count, result, err)

            if err != nil {
                return fmt.Errorf("failed to add IPv6 private IPs: %w", err)
            }
        }
    }

    return w.updateBciNodeCRDSpecWhenPrivateIPChanged(eniInfo, node)
}
```
修改bci-resource-controller代码库
```go
// 修改批量删除函数，基于百度云VPC接口规范（不需要isIpv6参数）
func (w *eniPrivateIPWorker) doBatchDeletePrivateIP(deleteIPList []string) error {
    eniInfo, node, err := w.getNodeEniInfo()
    if err != nil {
        return err
    }

    if len(deleteIPList) == 0 {
        return nil
    }

    // 批量删除辅助IP（IPv4和IPv6混合，系统自动判断）
    splitByBatchSize := splitSliceByBatchsize(deleteIPList, w.Controller.networkOption.DynamicChangePrivateIPBatchsize)
    for _, ipList := range splitByBatchSize {
        w.Controller.changeEniPrivateIPRatelimit(eniInfo.AccountID, eniPrivateIPBatchDeleteMethod, eniInfo.EniID, node, len(ipList))

        deleteArgs := &eni.BatchDeletePrivateIPArgs{
            EniID:              eniInfo.EniID,
            PrivateIPAddresses: ipList, // 混合IPv4和IPv6地址，系统自动识别
        }

        err = w.Controller.eniClient.BatchDeletePrivateIP(context.Background(), deleteArgs, w.Controller.buildSignOptionFn(eniInfo.AccountID))

        if isENIRatelimited(err) {
            for i := 0; i < w.Controller.networkOption.EniChangeRatelimitLoopRetryCount*2; i++ {
                klog.Errorf("eniPrivateIPWorker eni %s on node %s BatchDeletePrivateIP count %+v ratelimited try again",
                    eniInfo.EniID, w.NodeName, len(ipList))
                time.Sleep(time.Duration(rand.Intn(2000)+1000) * time.Millisecond)
                w.Controller.changeEniPrivateIPRatelimit(eniInfo.AccountID, eniPrivateIPBatchDeleteMethod, eniInfo.EniID, node, len(ipList))
                err = w.Controller.eniClient.BatchDeletePrivateIP(context.Background(), deleteArgs, w.Controller.buildSignOptionFn(eniInfo.AccountID))
                if err == nil {
                    break
                }
            }
        }

        if err != nil {
            klog.Errorf("eniPrivateIPWorker eniID %s on node %s BatchDeletePrivateIP %+v err %+v",
                w.EniID, w.NodeName, ipList, err)
            return fmt.Errorf("failed to delete private IPs: %w", err)
        }

        klog.V(3).Infof("eniPrivateIPWorker eni %s on node %s BatchDeletePrivateIP success, deleted IPs: %+v",
            eniInfo.EniID, w.NodeName, ipList)
    }

    return w.updateBciNodeCRDSpecWhenPrivateIPChanged(eniInfo, node)
}
```

3. **修改紧急分配请求处理 - 基于isIpv6参数**
修改bci-resource-controller代码库

```go
// 修改 dealEmergencyAllocateIPRequest 函数，支持通过isIpv6参数控制的双栈IP分配
func (w *eniPrivateIPWorker) dealEmergencyAllocateIPRequest() error {
    // ... 现有代码 ...
    
    // 检查是否需要IPv6支持
    eniInfo, node, err := w.getNodeEniInfo()
    if err != nil {
        return err
    }
    enableIPv6 := node.Labels[entity.BCIEnableIPv6Key] == "true"
    
    // 分别计算IPv4和IPv6的分配情况
    allocatedIPv4List, freeIPv4List, err := w.computeENIAllocatedAndFreePrivateIPList()
    if err != nil {
        return err
    }
    
    var allocatedIPv6List, freeIPv6List []string
    if enableIPv6 {
        allocatedIPv6List, freeIPv6List, err = w.computeENIAllocatedAndFreePrivateIPv6List()
        if err != nil {
            return err
        }
    }
    
    // 计算需要分配的IP数量
    needAllocateIPv4Count := 0
    needAllocateIPv6Count := 0
    
    if len(freeIPv4List) < len(waitENIPodList) {
        needAllocateIPv4Count = len(waitENIPodList) - len(freeIPv4List)
        maxAssignableIPv4Count := w.PrivateIPMaxCount - len(allocatedIPv4List)
        if needAllocateIPv4Count > maxAssignableIPv4Count {
            needAllocateIPv4Count = maxAssignableIPv4Count
        }
    }
    
    if enableIPv6 && len(freeIPv6List) < len(waitENIPodList) {
        needAllocateIPv6Count = len(waitENIPodList) - len(freeIPv6List)
        maxAssignableIPv6Count := w.PrivateIPMaxCount - len(allocatedIPv6List)
        if needAllocateIPv6Count > maxAssignableIPv6Count {
            needAllocateIPv6Count = maxAssignableIPv6Count
        }
    }
    
    // 使用基于isIpv6参数的批量分配函数
    if needAllocateIPv4Count > 0 || needAllocateIPv6Count > 0 {
        err = w.doBatchAddEniPrivateIPWithIPv6(needAllocateIPv4Count, needAllocateIPv6Count)
        if err != nil {
            klog.Errorf("eniPrivateIPWorker dealEmergencyAllocateIPRequest doBatchAddEniPrivateIPWithIPv6 eniID %s on node %s err %+v",
                w.EniID, w.NodeName, err)
            return err
        }
    }
    
    return nil
}
```


4. **新增IPv6相关的辅助函数**
修改bci-resource-controller代码库
```go
// 新增计算IPv6辅助IP列表的函数
func (w *eniPrivateIPWorker) computeENIAllocatedAndFreePrivateIPv6List() ([]string, []string, error) {
    freeIPv6s := make([]string, 0)
    eniInfo, node, err := w.getNodeEniInfo()
    if err != nil {
        return []string{}, freeIPv6s, err
    }

    // 计算已经使用的IPv6辅助IP列表
    usedPrivateIPv6List, err := computePodAllocatedPrivateIPv6List(eniInfo, node, w.Controller.podLister)
    if err != nil {
        return []string{}, freeIPv6s, err
    }

    // 计算空闲的IPv6辅助IP数
    allocatedIPv6List, err := w.computeENIAllocatedPrivateIPv6List(eniInfo)
    if err != nil {
        return allocatedIPv6List, freeIPv6s, err
    }

    freeIPv6s = util.SliceSubtract(allocatedIPv6List, usedPrivateIPv6List)
    return allocatedIPv6List, freeIPv6s, nil
}

// 新增获取ENI已分配IPv6辅助IP列表的函数
func (w *eniPrivateIPWorker) computeENIAllocatedPrivateIPv6List(eniInfo *entity.EniInfo) ([]string, error) {
    eniAllocatedPrivateIPv6 := make([]string, 0)
    resp, err := w.Controller.statEniWithRatelimit(eniInfo.EniID, eniInfo.AccountID)
    
    if err == nil {
        for _, ip := range resp.IPv6PrivateIPSet {
            if ip.Primary {
                continue
            }
            eniAllocatedPrivateIPv6 = append(eniAllocatedPrivateIPv6, ip.PrivateIPAddress)
        }
        return eniAllocatedPrivateIPv6, nil
    }

    // 从CRD中获取IPv6信息
    bcinode, err := w.Controller.bciNodeLister.Get(w.NodeName)
    if err != nil {
        return []string{}, err
    }
    
    allUserENIs, ok := bcinode.Spec.EniMultiIP.Pool[eniInfo.AccountID]
    if !ok {
        return []string{}, nil
    }
    
    eniIPs, ok := allUserENIs[eniInfo.EniID]
    if !ok {
        return []string{}, nil
    }
    
    for ip := range eniIPs.PrivateIPv6Addresses {
        eniAllocatedPrivateIPv6 = append(eniAllocatedPrivateIPv6, ip)
    }
    
    return eniAllocatedPrivateIPv6, nil
}
```

#### ******* Node agent改造
**主要改造点：**
1. **IPAM增强支持双栈**

修改bci-cni-driver代码库
注意：对于双栈Pod，需要将分配的IPv6地址Patch到pod annotation中展示bci_internal_PodIPv6
```
// bci-cni-driver/pkg/nodeagent/ipam/ipam.go
type IPAM struct {
    lock sync.RWMutex
    // UserIdlePool[userID][eniID]
    UserIdlePool   map[string]map[string]*queue.Queue // IPv4地址池
    IPv6UserIdlePool   map[string]map[string]*queue.Queue // IPv6地址池 新增
    Lookup         map[string]bool  // IPv4查询索引
    IPv6Lookup     map[strin]bool // IPv6查询索引 新增
    Cache          cache.Cache
    BciNode        *networkingv1.BciNode
    RefreshTrigger *trigger.Trigger
    WaitGCPod      map[string]time.Time
    // WaitReleaseIP[userIDEniID][ip]=do-not-release
    WaitReleaseIP map[string]map[string]networkingv1.IPReleaseStatus
    Nlink         netlinkwrapper.Interface
    client.Client
}

// 需要改造AllocateIP
func (ipam *IPAM) AllocateIP(ctx context.Context, podName string, podNameSpace string, podID string) (*rpc.ENIMultiIPReply, error) {}
```

#### ******* CNI插件改造
**主要改造点：**
1. **双栈申请**
修改bci-cni-driver代码库
```
// bci-cni-driver/cmd/plugins/bci-cni/main.go
func (p *bciCniPlugin) cmdAdd(args *skel.CmdArgs) error {
    ...
    // 步骤1 解析Pod信息
    podInfo, err := c.getPodInfo(args.Netns, args.ContainerID)
    if err != nil {
        return fmt.Errorf("get pod info failed: %w", err)
    }
    // 步骤2：检查是否需要 IPv6
    requestIPv6 := c.shouldRequestIPv6(podInfo)
    
    c.logger.InfoWithContext(args.Context, "CNI ADD request",
        Field("pod", podInfo.PodID), Field("container_id", args.ContainerID), 
        Field("request_ipv6", requestIPv6))
    
    // 步骤3：向 Node Agent 申请IP（兼容IPv6 IP）
    // 步骤4: 配置容器侧网络（兼容IPv6网络）
    // 步骤5: 配置ENI ns网络（兼容IPv6网络）
    if requestIPv6 {
        // 启用IPv6转发
        if err := c.enableIPv6Forward(eniNs); err != nil {
            return fmt.Errorf("enable IPv6 forward failed: %w", err)
        }
    }
    
    // 步骤6: 添加路由规则（兼容IPv6路由）
    if requestIPv6 {
        // 添加IPv6默认路由
        if err := c.addIPv6DefaultRoute(eniNs, ipv6Gateway); err != nil {
            return fmt.Errorf("add IPv6 default route failed: %w", err)
        }
        // 添加IPv6路由表规则
        if err := c.addIPv6RouteRules(eniNs, ipv6Addr); err != nil {
            return fmt.Errorf("add IPv6 route rules failed: %w", err)
        }
    }
}
```
2. **网络接口配置**
修改bci-cni-driver代码库
```
// bci-cni-driver/cmd/plugins/bci-cni/main.go
func (p *bciCniPlugin) setupContainerVeth(ctx context.Context, netns ns.NetNS, ifName string, mtu int, pr *current.Result) ([]*current.Interface, []*current.Interface, error) {
    // 兼容IPv6网络配置
}
```
3. **双栈IP删除**
修改bci-cni-driver代码库
```
// bci-cni-driver/cmd/plugins/bci-cni/main.go
func (p *bciCniPlugin) cmdDel(args *skel.CmdArgs) error{
    // 兼容删除IPv6辅助IP
}
```

#### ******* IPv4/IPv6资源回收
**主要改造点：**
1. **IP回收策略增强**
修改bci-cni-driver代码库
```go
// bci-cni-driver/pkg/nodeagent/ipam/ipam.go
func (ipam *IPAM) ReleaseIP(ctx context.Context, podName string, containerID string) error {
    // 步骤1: 查找Pod占用的IPv4和IPv6地址
    var releasedIPs []string
    
    // 查找IPv4和IPv6地址
    for userID, userPool := range ipam.BciNode.Status.EniMultiIP.Used {
        for eniID, eniInfo := range userPool {
            for ip, ipInfo := range eniInfo.PrivateIPAddresses {
                if ipInfo.ContainerID == containerID {
                    releasedIPs = append(releasedIPs, ip)
                }
            }
            for ip, ipInfo := range eniInfo.PrivateIPv6Addresses {
                if ipInfo.ContainerID == containerID {
                    releasedIPs = append(releasedIPs, ip)
                }
            }
        }
    }
    
    // 步骤2: 批量释放IPv4和IPv6地址（系统自动识别）
    if len(releasedIPs) > 0 {
        if err := ipam.releaseIPs(ctx, releasedIPs, containerID); err != nil {
            return fmt.Errorf("failed to release IPs %v: %w", releasedIPs, err)
        }
        klog.V(3).Infof("Successfully released IPs for container %s: %v", containerID, releasedIPs)
    }
    
    return nil
}

// 示例：批量删除IPv4和IPv6地址
func (ipam *IPAM) releaseIPs(ctx context.Context, ips []string, containerID string) error {
    // 可以同时包含IPv4和IPv6地址，例如：
    // ips = ["*************", "2001:db8::100", "*************"]
    // 系统会自动识别并处理
    
    deleteArgs := &eni.BatchDeletePrivateIPArgs{
        EniID:              ipam.getEniID(),
        PrivateIPAddresses: ips, // 混合IPv4和IPv6地址
    }
    
    return ipam.eniClient.BatchDeletePrivateIP(ctx, deleteArgs, ipam.signOption)
}
```

2. **ENI回收增强**

```go
// bci-resource-controller/pkg/controller/eni/eni_controller.go
func (c *Controller) gcEniPrivateIP(node *corev1.Node, userID string) error {
    // 步骤1: 获取ENI的IPv4和IPv6使用情况
    eniUsage := c.getEniUsage(node, userID)
    
    // 步骤2: 计算需要回收的IP数量
    toReleaseIPv4 := c.calculateReleaseCount(eniUsage.IPv4Used, eniUsage.IPv4Total)
    toReleaseIPv6 := c.calculateReleaseCount(eniUsage.IPv6Used, eniUsage.IPv6Total)
    
    // 步骤3: 批量释放辅助IP（IPv4和IPv6混合删除）
    var toReleaseIPs []string
    if toReleaseIPv4 > 0 {
        ipv4List := c.getUnusedIPv4List(userID, eniUsage.EniID, toReleaseIPv4)
        toReleaseIPs = append(toReleaseIPs, ipv4List...)
    }
    
    if toReleaseIPv6 > 0 {
        ipv6List := c.getUnusedIPv6List(userID, eniUsage.EniID, toReleaseIPv6)
        toReleaseIPs = append(toReleaseIPs, ipv6List...)
    }
    
    if len(toReleaseIPs) > 0 {
        if err := c.releaseEniPrivateIPs(userID, eniUsage.EniID, toReleaseIPs); err != nil {
            return err
        }
    }
    
    return nil
}
```

3. **CRD状态清理**
修改bci-cni-driver代码库

```go
// bci-cni-driver/pkg/nodeagent/controllers/bcinode_controller.go
func (r *BciNodeReconciler) cleanupIPStatus(ctx context.Context, bciNode *networkingv1.BciNode) error {
    // 清理IPv4地址状态
    for userID, userPool := range bciNode.Status.EniMultiIP.Used {
        for eniID, eniInfo := range userPool {
            for ip, ipInfo := range eniInfo.PrivateIPAddresses {
                if r.isPodDeleted(ipInfo.Owner) {
                    delete(eniInfo.PrivateIPAddresses, ip)
                }
            }
            // 清理IPv6地址状态
            for ip, ipInfo := range eniInfo.PrivateIPv6Addresses {
                if r.isPodDeleted(ipInfo.Owner) {
                    delete(eniInfo.PrivateIPv6Addresses, ip)
                }
            }
        }
    }
    
    return r.Update(ctx, bciNode)
}
```

4. **资源回收流程**

```mermaid
sequenceDiagram
    participant CNI as bci-cni
    participant NA as Node Agent
    participant CRD as BciNode CRD
    participant ENI as ENI Controller
    participant VPC as 百度云VPC

    Note over CNI,VPC: Pod删除触发双栈IP回收
    CNI->>NA: CmdDel（释放双栈IP）
    NA->>NA: 查找Pod占用的IPv4+IPv6地址
    NA->>CRD: 标记IPv4+IPv6地址为待释放
    NA->>CNI: 返回释放成功

    Note over NA,VPC: 异步回收流程
    NA->>NA: 验证Pod已删除
    NA->>CRD: 从status中移除IPv4+IPv6地址
    ENI->>CRD: 监听IP使用变化
        ENI->>ENI: 计算需要回收的辅助IP
    
    alt 需要回收辅助IP
        ENI->>VPC: 批量释放辅助IP（IPv4和IPv6混合）
        VPC->>ENI: 返回释放结果
    end
    
    ENI->>CRD: 更新BciNode spec中的IP池
```

### 3.2.5 基于isIpv6参数的接口实现要点

**SDK接口扩展方案**
基于百度云VPC现有接口，仅需在SDK层面增加`isIpv6`参数支持：
修改bci-cni-driver代码库

```go
// bci-cni-driver/pkg/bcesdk/eni/batch_add_private_ip.go
type BatchPrivateIPArgs struct {
    EniID                 string   `json:"-"`                          // URL路径参数，不在JSON中传递
    PrivateIPAddresses    []string `json:"privateIpAddresses"`         // 指定IP地址列表（IPv4或IPv6）
    PrivateIPAddressCount int      `json:"privateIpAddressCount,omitempty"` // 自动分配数量
    IsIpv6                bool     `json:"isIpv6"`                     // 关键参数：区分IPv4/IPv6
}

// 接口调用示例：IPv4辅助IP分配
args := &BatchPrivateIPArgs{
    EniID:                 "eni-xxxxx",
    PrivateIPAddressCount: 5,
    IsIpv6:                false, // IPv4
}

// 接口调用示例：IPv6辅助IP分配  
args := &BatchPrivateIPArgs{
    EniID:                 "eni-xxxxx", 
    PrivateIPAddressCount: 5,
    IsIpv6:                true,  // IPv6
}
```

**API路径规范**
辅助IP操作使用统一的API路径：
- **批量添加**：`POST /v1/eni/{eniId}/privateIp/batchAdd` （通过`isIpv6`参数区分）
- **批量删除**：`POST /v1/eni/{eniId}/privateIp/batchDelete` （系统自动识别IPv4/IPv6）
- **查询ENI**：`GET /v1/eni/{eniId}` （返回IPv4和IPv6信息）

**参数设计原则**
1. **向后兼容**：现有IPv4调用不需要修改，默认`isIpv6=false`
2. **类型统一**：IPv4和IPv6地址都使用`[]string`类型，避免重复定义结构体
3. **接口差异化**：
   - **批量添加**：需要`isIpv6`参数区分IPv4/IPv6操作
   - **批量删除**：无需`isIpv6`参数，系统根据IP地址格式自动判断
4. **错误处理**：IPv4和IPv6操作使用相同的错误处理逻辑和重试机制

**兼容性处理**
修改bci-cni-driver代码库
```go
// 批量添加接口 - 需要isIpv6参数
func (c *Client) BatchAddPrivateIP(ctx context.Context, args *BatchPrivateIPArgs, signOpt *bce.SignOption) (*BatchAddPrivateIPResult, error) {
    // 如果没有显式设置isIpv6，默认为false（IPv4）
    if args.IsIpv6 == false {
        // IPv4处理逻辑（保持现有行为）
    } else {
        // IPv6处理逻辑（新增行为）
    }
    
    path := "v1/eni/" + args.EniID + "/privateIp/batchAdd"
    // ... 现有实现保持不变
}

// 批量删除接口 - 不需要isIpv6参数
func (c *Client) BatchDeletePrivateIP(ctx context.Context, args *BatchDeletePrivateIPArgs, signOpt *bce.SignOption) error {
    // 系统根据IP地址格式自动判断IPv4/IPv6
    // 可以在同一个请求中混合删除IPv4和IPv6地址
    
    path := "v1/eni/" + args.EniID + "/privateIp/batchDelete"
    // ... 现有实现保持不变
}
```

**错误处理策略**
IPv4和IPv6操作共享相同的错误处理机制：
- **限流重试**：使用相同的限流检测和重试逻辑
- **错误分类**：网络错误、参数错误、配额错误等统一处理
- **批量删除优势**：可以在单个请求中混合删除IPv4和IPv6地址，减少API调用次数
- **日志记录**：增加IPv4/IPv6标识，便于问题排查


### 3.2.6 错误处理和回滚机制

#### ******* IPv6分配失败处理
修改bci-cni-driver代码库
```go
// bci-cni-driver/pkg/nodeagent/ipam/ipam.go
func (ipam *IPAM) AllocateIPWithRetry(ctx context.Context, podName string, podNameSpace string, podID string, requestIPv6 bool) (*rpc.ENIMultiIPReply, error) {
    const maxRetries = 3
    var lastErr error
    
    for i := 0; i < maxRetries; i++ {
        reply, err := ipam.AllocateIP(ctx, podName, podNameSpace, podID, requestIPv6)
        if err == nil {
            return reply, nil
        }
        
        lastErr = err
        // 针对不同错误类型进行不同的处理
        if isIPv6PoolExhausted(err) {
            // IPv6地址池耗尽，触发紧急申请
            if err := ipam.requestEmergencyIPv6(ctx, podID); err != nil {
                ipam.logger.Error("Failed to request emergency IPv6", "error", err)
                // 回退到仅IPv4模式
                return ipam.AllocateIP(ctx, podName, podNameSpace, podID, false)
            }
        } else if isTransientError(err) {
            // 临时错误，等待重试
            time.Sleep(time.Second * time.Duration(i+1))
            continue
        } else {
            // 不可恢复错误，直接返回
            return nil, err
        }
    }
    
    return nil, fmt.Errorf("failed to allocate IP after %d retries: %w", maxRetries, lastErr)
}
```

#### ******* ENI创建失败回滚
```go
// bci-resource-controller/pkg/controller/eni/eni_controller.go
func (c *Controller) createEniWithRollback(node *corev1.Node, podInfo *entity.WaitEniPodInfo, enableIPv6 bool) error {
    // 步骤1: 创建ENI
    eniID, err := c.doCreateEni(node, podInfo, enableIPv6)
    if err != nil {
        return fmt.Errorf("failed to create ENI: %w", err)
    }
    
    // 步骤2: 绑定ENI到节点
    if err := c.attachEniToNode(node, eniID); err != nil {
        // 回滚：删除已创建的ENI
        if rollbackErr := c.deleteEni(eniID); rollbackErr != nil {
            c.logger.Error("Failed to rollback ENI creation", "eni", eniID, "error", rollbackErr)
        }
        return fmt.Errorf("failed to attach ENI %s: %w", eniID, err)
    }
    
    // 步骤3: 申请辅助IP
    if err := c.allocatePrivateIPs(eniID, enableIPv6); err != nil {
        // 回滚：分离并删除ENI
        if rollbackErr := c.detachAndDeleteEni(node, eniID); rollbackErr != nil {
            c.logger.Error("Failed to rollback ENI after IP allocation failure", "eni", eniID, "error", rollbackErr)
        }
        return fmt.Errorf("failed to allocate private IPs for ENI %s: %w", eniID, err)
    }
    
    return nil
}
```

#### ******* CRD更新失败处理
修改bci-cni-driver代码库
```go
// bci-cni-driver/pkg/nodeagent/controllers/bcinode_controller.go
func (r *BciNodeReconciler) updateBciNodeWithRetry(ctx context.Context, bciNode *networkingv1.BciNode, updateFunc func(*networkingv1.BciNode) error) error {
    const maxRetries = 5
    
    for i := 0; i < maxRetries; i++ {
        // 获取最新版本
        if err := r.Get(ctx, client.ObjectKeyFromObject(bciNode), bciNode); err != nil {
            return fmt.Errorf("failed to get latest BciNode: %w", err)
        }
        
        // 应用更新
        if err := updateFunc(bciNode); err != nil {
            return fmt.Errorf("failed to apply update: %w", err)
        }
        
        // 尝试更新
        if err := r.Update(ctx, bciNode); err != nil {
            if errors.IsConflict(err) {
                // 冲突错误，重试
                time.Sleep(time.Millisecond * 100 * time.Duration(i+1))
                continue
            }
            return fmt.Errorf("failed to update BciNode: %w", err)
        }
        
        return nil
    }
    
    return fmt.Errorf("failed to update BciNode after %d retries", maxRetries)
}
```

### 3.2.7 用户使用说明

#### ******* 基本使用
用户通过在Pod的annotation中添加`bci.baidu.com/bci-enable-ipv6: "true"`来启用IPv6功能：

```yaml
apiVersion: v1
kind: Pod
metadata:
  name: test-pod
  annotations:
    bci.baidu.com/bci-enable-ipv6: "true"
spec:
  containers:
  - name: test-container
    image: nginx:latest
```

#### ******* 使用限制
1. **资源限制**：
   - 单个ENI最多支持20个辅助IP（IPv4+IPv6总数）
   - 单个Pod只能分配一个IPv6地址
   - IPv6功能需要节点支持（标签：bci-ipv6-enabled=true）

2. **网络限制**：
   - 仅支持VPC内双栈通信
   - 不支持IPv6公网访问（需要EIP）
   - 网络策略需要适配IPv6地址

#### ******* 故障排查
1. **Pod pending状态**：
   ```bash
   # 检查节点是否支持IPv6
   kubectl get nodes -l bci-ipv6-enabled=true
   
   # 检查BciNode CRD中的IPv6地址池
   kubectl get bcinode <node-name> -o yaml
   ```

2. **IPv6连接失败**：
   ```bash
   # 检查容器内IPv6配置
   kubectl exec -it <pod-name> -- ip -6 addr show
   kubectl exec -it <pod-name> -- ip -6 route show
   ```

### 3.2.8 最佳实践建议

#### ******* 性能优化
1. **IP池预分配**：根据业务特点调整IPv6地址池大小
2. **批量操作**：优先使用批量API减少调用次数
3. **混合删除**：利用批量删除接口可混合处理IPv4和IPv6地址的特性，减少API调用
4. **监控告警**：设置IPv6地址池水位告警

#### ******* 安全建议
1. **网络策略**：配置适当的IPv6网络策略
2. **访问控制**：限制IPv6地址的访问范围
3. **日志审计**：记录IPv6地址分配和释放操作

#### ******* 运维建议
1. **渐进式部署**：先在测试环境验证IPv6功能
2. **兼容性测试**：确保应用程序支持IPv6
3. **监控覆盖**：添加IPv6相关的监控指标

# 4 可观测设计
## 4.1 指标

| 指标名称 | 类型 | 标签 | 描述 | 监控层级 |
|---------|------|------|------|----------|
| bci_ipv6_allocation_duration_seconds | Histogram | node, tenant, result | IPv6 地址分配耗时分布 | UseCase |
| bci_ipv6_pool_utilization_ratio | Gauge | node, tenant | IPv6 地址池使用率 (0-1) | Domain |
| bci_dual_stack_pod_total | Counter | node, tenant, status | 双栈 Pod 总数计数器 | Interface |
| bci_eni_ipv6_enabled_nodes | Gauge | cluster | 启用 IPv6 的节点数量 | Infrastructure |
| bci_ipv6_allocation_errors_total | Counter | node, tenant, error_type | IPv6 分配失败计数器 | UseCase |
| bci_tenant_lock_ipv6_duration_seconds | Histogram | tenant | IPv6 节点 tenant-lock 耗时 | UseCase |
| bci_crd_ipv6_update_duration_seconds | Histogram | node, operation | BciNode CRD IPv6 字段更新耗时 | Infrastructure |

## 4.2 事件

| 事件类型 | 触发条件 | 事件内容 | 架构层级 |
|---------|----------|----------|----------|
| IPv6PoolLowWatermark | IPv6 地址使用率 > 80% | 节点 {node} 租户 {tenant} IPv6 地址池水位告警 | UseCase |
| IPv6AllocationTimeout | IPv6 分配超时 > 30s | Pod {pod} IPv6 地址分配超时，节点 {node} | Interface |
| DualStackENICreated | 双栈 ENI 创建成功 | 节点 {node} 租户 {tenant} 双栈 ENI {eni_id} 创建完成 | Infrastructure |
| IPv6NodeLabelFailed | 节点 IPv6 标签更新失败 | 节点 {node} bci-ipv6-enabled 标签更新失败 | Infrastructure |
| TenantLockIPv6Conflict | IPv6 节点调度冲突 | 租户 {tenant} IPv6 节点调度冲突，候选节点不足 | UseCase |

## 4.3 关键日志

**日志架构设计：**
```go
// Domain 层日志接口
type Logger interface {
    InfoWithContext(ctx context.Context, msg string, fields ...Field)
    ErrorWithContext(ctx context.Context, msg string, err error, fields ...Field)
    WarnWithContext(ctx context.Context, msg string, fields ...Field)
}

// IPv6 扩展日志字段
type IPv6LogFields struct {
    TraceID     string
    Component   string  // tenant-lock/eni-manager/ipam/cni
    Operation   string  // alloc-ipv6/create-dual-eni/update-crd
    TenantID    string
    NodeID      string
    PodID       string
    IPv4Address string
    IPv6Address string
    ENIId       string
    Duration    time.Duration
    Error       error
}
```

**关键日志示例：**
```
# [UseCase层] tenant-lock IPv6 节点选择
INFO  [UseCase]/IPv6节点选择开始  component=tenant-lock operation=select-ipv6-node tenant=user123 pod=nginx-abc trace_id=req_001
WARN  [UseCase]/IPv6节点候选不足  component=tenant-lock operation=select-ipv6-node tenant=user123 available_nodes=0 trace_id=req_001
INFO  [UseCase]/创建IPv6节点      component=tenant-lock operation=create-ipv6-node node=********* duration=25s trace_id=req_001

# [Infrastructure层] 双栈 ENI 管理
INFO  [Infrastructure]/双栈ENI创建开始  component=eni-manager operation=create-dual-eni node=********* tenant=user123 trace_id=req_001
INFO  [Infrastructure]/IPv4辅助IP申请成功  component=eni-manager operation=add-ipv4-ips eni=eni-abc123 count=8 trace_id=req_001
INFO  [Infrastructure]/IPv6辅助IP申请成功  component=eni-manager operation=add-ipv6-ips eni=eni-abc123 count=8 trace_id=req_001
ERROR [Infrastructure]/CRD更新失败       component=eni-manager operation=update-crd node=********* error="conflict" trace_id=req_001

# [UseCase层] IPv6 IPAM 分配
INFO  [UseCase]/IPv6地址分配请求  component=ipam operation=alloc-ipv6 pod=nginx-abc tenant=user123 trace_id=req_002
INFO  [UseCase]/IPv6地址分配成功  component=ipam operation=alloc-ipv6 pod=nginx-abc ipv6=2400:da00::100 duration=15ms trace_id=req_002
WARN  [UseCase]/IPv6池水位告警    component=ipam operation=check-watermark node=********* utilization=0.85 trace_id=req_002

# [Interface层] CNI 双栈配置
INFO  [Interface]/双栈网络配置开始  component=cni operation=setup-dual-stack pod=nginx-abc container_id=abc123 trace_id=req_002
INFO  [Interface]/容器IPv6配置完成  component=cni operation=setup-ipv6 pod=nginx-abc ipv6=2400:da00::100 gateway=fe80::1 trace_id=req_002
ERROR [Interface]/双栈路由配置失败  component=cni operation=setup-routes pod=nginx-abc error="route exists" trace_id=req_002
```

# 5 总结与实施建议

## 5.1 方案总结

本设计方案为BCI Pod提供了完整的IPv4/IPv6双栈网络支持，核心特点包括：

### 5.1.1 技术架构
- **完全复用现有架构**：基于ENI+辅助IP机制，最大化代码复用
- **双栈IP管理**：同一ENI上同时管理IPv4和IPv6地址
- **扩展调度机制**：在tenant-lock基础上增加IPv6节点标签匹配
- **统一资源管理**：通过BciNode CRD统一管理双栈资源

### 5.1.2 关键功能
- **用户控制开关**：通过annotation `bci.baidu.com/bci-enable-ipv6` 控制功能启用
- **智能资源分配**：基于`isIpv6`参数的统一接口实现双栈IP的动态分配和回收
- **完全接口兼容**：基于百度云VPC现有接口，批量添加通过`isIpv6`参数区分，批量删除自动识别IPv4/IPv6
- **错误处理机制**：完善的失败重试和回滚机制
- **全面监控覆盖**：包含IPv6相关的指标、事件和日志

### 5.1.3 接口实现优势
- **零接口修改**：完全基于百度云现有API，通过`isIpv6`参数实现功能扩展
- **向后兼容**：现有IPv4调用逻辑无需任何修改，默认`isIpv6=false`
- **实现简洁**：避免定义独立的IPv6接口，减少代码复杂度
- **批量删除便利**：删除操作可混合处理IPv4和IPv6地址，无需区分调用
- **错误处理统一**：IPv4和IPv6操作共享相同的限流、重试、错误处理机制

### 5.1.4 修改范围
- **bci-resource-controller**：扩展Pod调度、ENI管理、基于`isIpv6`参数的IP分配逻辑
- **bci-cni-driver**：扩展CRD定义、IPAM功能、CNI插件，增加`isIpv6`参数支持
- **配置和监控**：更新相关配置文件和监控指标

## 5.2 实施建议

### 5.2.1 实施阶段
**阶段一：基础功能开发（4周）**
- 完成BciNode CRD字段扩展
- 实现bci-resource-controller IPv6调度逻辑
- 完成ENI Controller双栈ENI创建功能

**阶段二：IPAM和CNI开发（3周）**
- 实现Node Agent双栈IPAM功能
- 完成CNI插件IPv6网络配置
- 实现双栈资源回收机制

**阶段三：监控和测试（2周）**
- 完成监控指标和日志集成
- 编写单元测试和集成测试
- 性能测试和压力测试

**阶段四：部署和验证（1周）**
- 灰度部署到测试环境
- 功能验证和性能验证
- 文档更新和用户培训

### 5.2.2 风险控制
1. **向后兼容性**：确保现有IPv4业务不受影响
2. **性能影响**：监控双栈模式下的性能指标
3. **资源配额**：合理配置IPv6地址池大小
4. **故障处理**：完善的降级和回滚机制

### 5.2.3 成功标准
- **功能完整性**：用户可以通过annotation成功启用IPv6功能
- **性能稳定性**：双栈模式下Pod创建时间不超过IPv4模式的120%
- **资源利用率**：IPv6地址池利用率保持在合理范围（60%-80%）
- **监控覆盖度**：IPv6相关指标和日志完整覆盖

## 5.3 后续演进规划

### 5.3.1 短期规划（3个月）
- 支持IPv6 EIP绑定
- 优化双栈IP分配算法
- 增强网络策略支持

### 5.3.2 中期规划（6个月）
- 支持IPv6 LoadBalancer
- 实现跨VPC IPv6通信
- 增强安全组IPv6规则

### 5.3.3 长期规划（1年）
- 支持IPv6 NAT Gateway
- 实现IPv6网络服务网格
- 提供IPv6网络策略可视化

## 5.4 关键风险和缓解措施

### 5.4.1 技术风险
| 风险类型 | 风险描述 | 缓解措施 |
|---------|----------|----------|
| 性能风险 | 双栈模式可能影响网络性能 | 充分的性能测试和优化 |
| 兼容性风险 | 新功能可能影响现有业务 | 严格的向后兼容性测试 |
| 资源风险 | IPv6地址池管理复杂度增加 | 完善的资源管理和监控 |

### 5.4.2 运维风险
| 风险类型 | 风险描述 | 缓解措施 |
|---------|----------|----------|
| 部署风险 | 新功能部署可能影响现有服务 | 灰度部署和快速回滚机制 |
| 监控风险 | IPv6相关问题难以发现 | 完善的监控和告警机制 |
| 运维风险 | 故障排查复杂度增加 | 详细的故障排查手册和工具 |

本设计方案通过完全复用现有架构，最小化了开发成本和部署风险，为BCI提供了一个稳定、高性能的IPv6解决方案。特别是批量删除接口的设计，通过系统自动识别IPv4/IPv6地址格式，实现了混合批量删除，显著提升了资源回收效率。通过分阶段实施和严格的测试验证，可以确保功能的稳定性和可靠性。