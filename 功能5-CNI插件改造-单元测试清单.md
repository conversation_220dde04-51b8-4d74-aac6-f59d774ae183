# 功能5：CNI 插件改造 - 单元测试清单

## 测试概述
CNI插件改造主要包括双栈申请和网络接口配置两个核心功能。本清单涵盖IPv6双栈网络配置的完整测试场景。

## 测试分类

### 1. 双栈申请测试 (cmdAdd函数)

#### 1.1 IPv6需求检测测试
- [ ] `TestShouldRequestIPv6_ValidAnnotation` - 验证有效的IPv6 annotation解析
- [ ] `TestShouldRequestIPv6_InvalidAnnotation` - 验证无效的IPv6 annotation处理
- [ ] `TestShouldRequestIPv6_MissingAnnotation` - 验证缺少IPv6 annotation的情况
- [ ] `TestShouldRequestIPv6_EmptyPodArgs` - 验证空Pod参数的处理
- [ ] `TestShouldRequestIPv6_MultiplePods` - 验证多个Pod的IPv6需求检测

#### 1.2 IP地址分配测试
- [ ] `TestCmdAdd_IPv4OnlyAllocation` - 验证仅IPv4地址分配
- [ ] `TestCmdAdd_IPv6OnlyAllocation` - 验证仅IPv6地址分配
- [ ] `TestCmdAdd_DualStackAllocation` - 验证IPv4+IPv6双栈分配
- [ ] `TestCmdAdd_MultipleIPv4Addresses` - 验证多个IPv4地址分配
- [ ] `TestCmdAdd_IPv6AddressFormatValidation` - 验证IPv6地址格式检查
- [ ] `TestCmdAdd_IPv6GatewayParsing` - 验证IPv6网关解析
- [ ] `TestCmdAdd_DefaultIPv6Gateway` - 验证默认IPv6网关设置

#### 1.3 网络配置测试
- [ ] `TestCmdAdd_IPv6ForwardingEnabled` - 验证IPv6转发启用
- [ ] `TestCmdAdd_IPv6ForwardingFailure` - 验证IPv6转发失败处理
- [ ] `TestCmdAdd_IPv6RoutingRules` - 验证IPv6路由规则配置
- [ ] `TestCmdAdd_DualStackRouting` - 验证双栈路由规则
- [ ] `TestCmdAdd_IPv6UniqueRouteTable` - 验证IPv6独立路由表

#### 1.4 错误处理测试
- [ ] `TestCmdAdd_IPv6AllocationFailure` - 验证IPv6分配失败处理
- [ ] `TestCmdAdd_IPv6NetworkSetupFailure` - 验证IPv6网络设置失败
- [ ] `TestCmdAdd_IPv6ForwardingUnavailable` - 验证IPv6转发不可用
- [ ] `TestCmdAdd_IPv6RouteFailure` - 验证IPv6路由设置失败
- [ ] `TestCmdAdd_MixedFailureRollback` - 验证混合错误回滚

### 2. 网络接口配置测试 (setupContainerVeth函数)

#### 2.1 IPv6网络接口配置
- [ ] `TestSetupContainerVeth_IPv6AddressConfiguration` - 验证IPv6地址配置
- [ ] `TestSetupContainerVeth_IPv6AddressAddition` - 验证IPv6地址添加
- [ ] `TestSetupContainerVeth_IPv6AddressValidation` - 验证IPv6地址验证
- [ ] `TestSetupContainerVeth_IPv6InterfaceSelection` - 验证IPv6接口选择
- [ ] `TestSetupContainerVeth_IPv6DuplicateAddresses` - 验证重复IPv6地址处理

#### 2.2 IPv6路由配置
- [ ] `TestSetupContainerVeth_IPv6GatewayRoute` - 验证IPv6网关路由
- [ ] `TestSetupContainerVeth_IPv6DefaultRoute` - 验证IPv6默认路由
- [ ] `TestSetupContainerVeth_IPv6RouteReplacement` - 验证IPv6路由替换
- [ ] `TestSetupContainerVeth_IPv6RoutePriority` - 验证IPv6路由优先级
- [ ] `TestSetupContainerVeth_IPv6RouteConflict` - 验证IPv6路由冲突处理

#### 2.3 双栈网络配置
- [ ] `TestSetupContainerVeth_DualStackInterface` - 验证双栈接口配置
- [ ] `TestSetupContainerVeth_IPv4IPv6Coexistence` - 验证IPv4/IPv6共存
- [ ] `TestSetupContainerVeth_DualStackRouting` - 验证双栈路由配置
- [ ] `TestSetupContainerVeth_InterfaceSharing` - 验证接口共享
- [ ] `TestSetupContainerVeth_AddressIsolation` - 验证地址隔离

### 3. ENI网络命名空间配置测试 (setupEniNsVeth函数)

#### 3.1 IPv6ENI配置
- [ ] `TestSetupEniNsVeth_IPv6GatewayAddress` - 验证IPv6网关地址设置
- [ ] `TestSetupEniNsVeth_IPv6AddressExistence` - 验证IPv6地址存在性检查
- [ ] `TestSetupEniNsVeth_IPv6RouteSetup` - 验证IPv6路由设置
- [ ] `TestSetupEniNsVeth_IPv6VethConfiguration` - 验证IPv6 veth配置
- [ ] `TestSetupEniNsVeth_IPv6ForwardingInENI` - 验证ENI中的IPv6转发

#### 3.2 双栈ENI配置
- [ ] `TestSetupEniNsVeth_DualStackENI` - 验证双栈ENI配置
- [ ] `TestSetupEniNsVeth_IPv4IPv6Separation` - 验证IPv4/IPv6分离
- [ ] `TestSetupEniNsVeth_MixedAddressHandling` - 验证混合地址处理
- [ ] `TestSetupEniNsVeth_DualStackForwarding` - 验证双栈转发
- [ ] `TestSetupEniNsVeth_ENIAddressManagement` - 验证ENI地址管理

### 4. 网络清理测试 (cmdDel函数)

#### 4.1 IPv6地址释放
- [ ] `TestCmdDel_IPv6AddressRelease` - 验证IPv6地址释放
- [ ] `TestCmdDel_IPv6RouteCleanup` - 验证IPv6路由清理
- [ ] `TestCmdDel_IPv6RuleCleanup` - 验证IPv6规则清理
- [ ] `TestCmdDel_IPv6NamespaceCleanup` - 验证IPv6命名空间清理
- [ ] `TestCmdDel_IPv6ForwardingDisable` - 验证IPv6转发禁用

#### 4.2 双栈清理
- [ ] `TestCmdDel_DualStackCleanup` - 验证双栈清理
- [ ] `TestCmdDel_MixedAddressCleanup` - 验证混合地址清理
- [ ] `TestCmdDel_PartialCleanupFailure` - 验证部分清理失败处理
- [ ] `TestCmdDel_CleanupRollback` - 验证清理回滚
- [ ] `TestCmdDel_ResourceLeakPrevention` - 验证资源泄漏预防

### 5. 辅助函数测试

#### 5.1 IPv6检测函数
- [ ] `TestShouldRequestIPv6_AnnotationParsing` - 验证annotation解析
- [ ] `TestShouldRequestIPv6_BooleanConversion` - 验证布尔值转换
- [ ] `TestShouldRequestIPv6_CaseSensitivity` - 验证大小写敏感性
- [ ] `TestShouldRequestIPv6_WhitespaceHandling` - 验证空白字符处理
- [ ] `TestShouldRequestIPv6_EdgeCases` - 验证边界情况

#### 5.2 IPv6转发函数
- [ ] `TestEnableIPv6Forward_Success` - 验证IPv6转发成功启用
- [ ] `TestEnableIPv6Forward_FileNotFound` - 验证控制文件不存在
- [ ] `TestEnableIPv6Forward_PermissionDenied` - 验证权限拒绝
- [ ] `TestEnableIPv6Forward_WriteFailure` - 验证写入失败
- [ ] `TestEnableIPv6Forward_AlreadyEnabled` - 验证已启用状态

### 6. 集成测试

#### 6.1 端到端流程测试
- [ ] `TestCNIPlugin_DualStackE2E` - 验证双栈端到端流程
- [ ] `TestCNIPlugin_IPv6OnlyE2E` - 验证IPv6单栈端到端流程
- [ ] `TestCNIPlugin_FallbackToIPv4` - 验证回退到IPv4
- [ ] `TestCNIPlugin_AddDelCycle` - 验证添加-删除循环
- [ ] `TestCNIPlugin_ConcurrentRequests` - 验证并发请求处理

#### 6.2 性能测试
- [ ] `BenchmarkCNIPlugin_IPv6Setup` - IPv6设置性能基准
- [ ] `BenchmarkCNIPlugin_DualStackSetup` - 双栈设置性能基准
- [ ] `BenchmarkCNIPlugin_IPv6Cleanup` - IPv6清理性能基准
- [ ] `BenchmarkCNIPlugin_AddressAllocation` - 地址分配性能基准
- [ ] `BenchmarkCNIPlugin_RouteConfiguration` - 路由配置性能基准

### 7. 兼容性测试

#### 7.1 向后兼容性
- [ ] `TestCNIPlugin_IPv4BackwardCompatibility` - 验证IPv4向后兼容
- [ ] `TestCNIPlugin_LegacyConfiguration` - 验证旧配置兼容
- [ ] `TestCNIPlugin_MixedVersions` - 验证混合版本兼容
- [ ] `TestCNIPlugin_ConfigurationMigration` - 验证配置迁移
- [ ] `TestCNIPlugin_FeatureFlag` - 验证功能标志

#### 7.2 环境兼容性
- [ ] `TestCNIPlugin_IPv6Disabled` - 验证IPv6禁用环境
- [ ] `TestCNIPlugin_IPv6Unavailable` - 验证IPv6不可用环境
- [ ] `TestCNIPlugin_KernelVersions` - 验证不同内核版本
- [ ] `TestCNIPlugin_NetworkStacks` - 验证不同网络栈
- [ ] `TestCNIPlugin_ContainerRuntimes` - 验证不同容器运行时

## 测试工具和Mock

### Mock对象
- `MockNetlinkInterface` - 模拟netlink接口
- `MockIPAMInterface` - 模拟IPAM接口
- `MockNetworkNamespace` - 模拟网络命名空间
- `MockFileSystem` - 模拟文件系统

### 测试工具
- `IPv6AddressGenerator` - IPv6地址生成器
- `NetworkConfigBuilder` - 网络配置构建器
- `RouteTableValidator` - 路由表验证器
- `AddressValidator` - 地址验证器

## 测试数据

### IPv6地址样本
- `2001:db8::1` - 标准IPv6地址
- `fe80::1` - 链路本地地址
- `::1` - 回环地址
- `2001:db8::/64` - IPv6网络段

### 网关地址样本
- `fe80::1` - 默认IPv6网关
- `2001:db8::1` - 全球单播网关
- `***************` - IPv4网关

## 测试统计
- **总测试用例数**: 77
- **核心功能测试**: 32
- **错误处理测试**: 15
- **集成测试**: 10
- **性能测试**: 5
- **兼容性测试**: 10
- **辅助函数测试**: 5

## 优先级说明
- **P0 (高)**: 双栈申请、网络接口配置、地址释放
- **P1 (中)**: 错误处理、兼容性、集成测试
- **P2 (低)**: 性能测试、边界情况、辅助函数

## 测试环境要求
- 支持IPv6的Linux内核 (≥3.10)
- 启用IPv6的网络栈
- 容器运行时支持
- 网络命名空间支持
- root权限或CAP_NET_ADMIN能力 