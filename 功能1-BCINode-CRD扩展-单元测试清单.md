# 功能1-BCINode-CRD扩展-单元测试清单

## 功能概述
BCINode CRD扩展功能为现有的BCINode CRD增加了IPv6地址池支持，通过在AllocationEni结构中新增PrivateIPv6Addresses字段来实现双栈IP地址管理。

## 测试范围
- **修改文件**: `bci-cni-driver/apis/networking/v1/bcinode_types.go`
- **核心变更**: 在AllocationEni结构中增加PrivateIPv6Addresses字段
- **测试目标**: 验证CRD结构扩展的正确性和兼容性

## 单元测试清单

### 1. 数据结构验证测试
**测试类别**: 结构体字段验证
**测试目标**: 验证AllocationEni结构体字段完整性

#### 1.1 AllocationEni结构体字段验证
- **测试用例**: TestAllocationEniStructure
- **验证内容**: 
  - 验证PrivateIPv6Addresses字段存在
  - 验证字段类型为`map[string]AllocationIP`
  - 验证JSON标签正确性（privateIPv6Addresses）
  - 验证字段为可选字段（omitempty）
- **测试方法**: 使用反射验证结构体字段
- **期望结果**: 字段定义与设计方案一致

#### 1.2 字段类型兼容性验证
- **测试用例**: TestPrivateIPv6AddressesType  
- **验证内容**:
  - 验证PrivateIPv6Addresses与PrivateIPAddresses使用相同类型
  - 验证AllocationIP结构可以正确存储IPv6地址信息
  - 验证字段初始化时的零值行为
- **测试方法**: 创建AllocationEni实例，验证字段类型和初始值
- **期望结果**: 字段类型完全兼容，零值为nil

### 2. JSON序列化/反序列化测试
**测试类别**: 数据序列化验证
**测试目标**: 验证CRD对象的JSON处理正确性

#### 2.1 JSON序列化测试
- **测试用例**: TestAllocationEniJSONMarshal
- **验证内容**:
  - 验证包含IPv6地址的AllocationEni对象能正确序列化
  - 验证privateIPv6Addresses字段在JSON中的正确表示
  - 验证空IPv6地址池时的omitempty行为
- **测试数据**: 
  - 包含IPv6地址的AllocationEni对象
  - 空IPv6地址池的AllocationEni对象
- **期望结果**: JSON格式正确，字段名称符合预期

#### 2.2 JSON反序列化测试
- **测试用例**: TestAllocationEniJSONUnmarshal
- **验证内容**:
  - 验证JSON数据能正确反序列化为AllocationEni对象
  - 验证IPv6地址数据的正确解析
  - 验证向后兼容性（不包含IPv6字段的旧JSON数据）
- **测试数据**:
  - 包含IPv6地址的JSON数据
  - 仅包含IPv4地址的JSON数据（向后兼容）
- **期望结果**: 反序列化成功，数据完整性保持

### 3. 业务逻辑验证测试
**测试类别**: 业务场景验证
**测试目标**: 验证IPv6地址池的业务逻辑正确性

#### 3.1 IPv6地址池操作测试
- **测试用例**: TestIPv6AddressPoolOperations
- **验证内容**:
  - 验证IPv6地址添加到地址池
  - 验证IPv6地址从地址池删除
  - 验证地址池的并发安全性
- **测试场景**:
  - 单个IPv6地址添加/删除
  - 批量IPv6地址操作
  - 混合IPv4/IPv6地址操作
- **期望结果**: 操作成功，数据一致性保持

#### 3.2 AllocationIP兼容性测试
- **测试用例**: TestAllocationIPCompatibility
- **验证内容**:
  - 验证AllocationIP结构可以正确存储IPv6地址
  - 验证IPv6地址格式验证
  - 验证与现有IPv4逻辑的兼容性
- **测试数据**:
  - 有效的IPv6地址格式
  - 无效的IPv6地址格式
  - 混合IPv4/IPv6地址数据
- **期望结果**: IPv6地址正确存储，格式验证生效

### 4. CRD对象完整性测试
**测试类别**: 对象完整性验证
**测试目标**: 验证BciNode CRD对象的完整性

#### 4.1 BciNode对象创建测试
- **测试用例**: TestBciNodeWithIPv6
- **验证内容**:
  - 验证包含IPv6地址的BciNode对象创建
  - 验证spec和status字段的IPv6支持
  - 验证对象的深拷贝行为
- **测试场景**:
  - 创建包含IPv6地址池的BciNode对象
  - 验证对象的DeepCopy方法
- **期望结果**: 对象创建成功，数据完整性保持

#### 4.2 CRD版本兼容性测试
- **测试用例**: TestCRDVersionCompatibility
- **验证内容**:
  - 验证新版本CRD与旧版本的兼容性
  - 验证升级场景下的数据迁移
  - 验证API版本的向后兼容性
- **测试场景**:
  - 旧版本CRD对象升级到新版本
  - 新版本CRD对象降级到旧版本
- **期望结果**: 版本兼容性良好，数据不丢失

### 5. 性能和边界条件测试
**测试类别**: 性能和异常情况验证
**测试目标**: 验证极端情况下的行为

#### 5.1 大规模数据测试
- **测试用例**: TestLargeScaleIPv6Pool
- **验证内容**:
  - 验证大量IPv6地址的存储性能
  - 验证序列化/反序列化性能
  - 验证内存使用情况
- **测试数据**: 包含1000+个IPv6地址的地址池
- **期望结果**: 性能符合预期，内存使用合理

#### 5.2 边界条件测试
- **测试用例**: TestBoundaryConditions
- **验证内容**:
  - 验证空IPv6地址池的处理
  - 验证IPv6地址格式边界情况
  - 验证并发访问的安全性
- **测试场景**:
  - nil地址池
  - 空字符串地址
  - 格式错误的IPv6地址
- **期望结果**: 边界条件处理正确，无panic或异常

## 测试环境要求
- **Go版本**: 1.18+
- **测试框架**: 使用Go标准测试框架
- **依赖包**: 
  - `k8s.io/apimachinery/pkg/apis/meta/v1`
  - `encoding/json`
  - `reflect`
  - `testing`

## 测试覆盖率要求
- **代码覆盖率**: ≥90%
- **分支覆盖率**: ≥85%
- **重点覆盖**: 新增的PrivateIPv6Addresses字段相关逻辑

## 测试执行策略
1. **优先级**: 高优先级测试（结构体验证、JSON序列化）
2. **执行顺序**: 按测试类别顺序执行
3. **并发执行**: 支持并发测试执行
4. **失败处理**: 详细的错误信息和堆栈跟踪 