syntax = "proto3";

package rpc;

option go_package = "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/rpc;rpc";

// The service definition.
service CniBackend {
    rpc AllocateIP (Request) returns (Response) {
    }
    rpc ReleaseIP (Request) returns (Response) {
    }
    rpc CheckIP (Request) returns (Response) {
    }
}

enum IPType {
  ENIMultiIPType = 0;
}

message Request {
  string K8sPodName = 1;
  string K8sPodNamespace = 2;
  string K8sPodInfraContainerID = 3;
  string Netns = 4;
  string IfName = 5;
}


message Response {
  bool IsSuccess = 1;
  string ErrMsg = 2;
  IPType IPType = 3;
  oneof NetworkInfo {
      ENIMultiIPReply ENIMultiIP = 4;
  }
}

message ENIMultiIPReply {
  string IP = 1;
  string Mac = 2;
  string Gateway = 3;
  string EniNetns = 4;
  bool EniRequireUniqueRouteTable = 5;
  int32 EniRouteTable = 6;
  repeated string MultiIPs = 7;
  string IPv6IP = 8;
}