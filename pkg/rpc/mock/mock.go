// /*
// Copyright 2022 Baidu, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
// */

// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/rpc (interfaces: CniBackendClient,CniBackendServer)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
	rpc "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/rpc"
)

// MockCniBackendClient is a mock of CniBackendClient interface.
type MockCniBackendClient struct {
	ctrl     *gomock.Controller
	recorder *MockCniBackendClientMockRecorder
}

// MockCniBackendClientMockRecorder is the mock recorder for MockCniBackendClient.
type MockCniBackendClientMockRecorder struct {
	mock *MockCniBackendClient
}

// NewMockCniBackendClient creates a new mock instance.
func NewMockCniBackendClient(ctrl *gomock.Controller) *MockCniBackendClient {
	mock := &MockCniBackendClient{ctrl: ctrl}
	mock.recorder = &MockCniBackendClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCniBackendClient) EXPECT() *MockCniBackendClientMockRecorder {
	return m.recorder
}

// AllocateIP mocks base method.
func (m *MockCniBackendClient) AllocateIP(arg0 context.Context, arg1 *rpc.Request, arg2 ...grpc.CallOption) (*rpc.Response, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AllocateIP", varargs...)
	ret0, _ := ret[0].(*rpc.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AllocateIP indicates an expected call of AllocateIP.
func (mr *MockCniBackendClientMockRecorder) AllocateIP(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AllocateIP", reflect.TypeOf((*MockCniBackendClient)(nil).AllocateIP), varargs...)
}

// CheckIP mocks base method.
func (m *MockCniBackendClient) CheckIP(arg0 context.Context, arg1 *rpc.Request, arg2 ...grpc.CallOption) (*rpc.Response, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckIP", varargs...)
	ret0, _ := ret[0].(*rpc.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckIP indicates an expected call of CheckIP.
func (mr *MockCniBackendClientMockRecorder) CheckIP(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIP", reflect.TypeOf((*MockCniBackendClient)(nil).CheckIP), varargs...)
}

// ReleaseIP mocks base method.
func (m *MockCniBackendClient) ReleaseIP(arg0 context.Context, arg1 *rpc.Request, arg2 ...grpc.CallOption) (*rpc.Response, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ReleaseIP", varargs...)
	ret0, _ := ret[0].(*rpc.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReleaseIP indicates an expected call of ReleaseIP.
func (mr *MockCniBackendClientMockRecorder) ReleaseIP(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReleaseIP", reflect.TypeOf((*MockCniBackendClient)(nil).ReleaseIP), varargs...)
}

// MockCniBackendServer is a mock of CniBackendServer interface.
type MockCniBackendServer struct {
	ctrl     *gomock.Controller
	recorder *MockCniBackendServerMockRecorder
}

// MockCniBackendServerMockRecorder is the mock recorder for MockCniBackendServer.
type MockCniBackendServerMockRecorder struct {
	mock *MockCniBackendServer
}

// NewMockCniBackendServer creates a new mock instance.
func NewMockCniBackendServer(ctrl *gomock.Controller) *MockCniBackendServer {
	mock := &MockCniBackendServer{ctrl: ctrl}
	mock.recorder = &MockCniBackendServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCniBackendServer) EXPECT() *MockCniBackendServerMockRecorder {
	return m.recorder
}

// AllocateIP mocks base method.
func (m *MockCniBackendServer) AllocateIP(arg0 context.Context, arg1 *rpc.Request) (*rpc.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AllocateIP", arg0, arg1)
	ret0, _ := ret[0].(*rpc.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AllocateIP indicates an expected call of AllocateIP.
func (mr *MockCniBackendServerMockRecorder) AllocateIP(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AllocateIP", reflect.TypeOf((*MockCniBackendServer)(nil).AllocateIP), arg0, arg1)
}

// CheckIP mocks base method.
func (m *MockCniBackendServer) CheckIP(arg0 context.Context, arg1 *rpc.Request) (*rpc.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckIP", arg0, arg1)
	ret0, _ := ret[0].(*rpc.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckIP indicates an expected call of CheckIP.
func (mr *MockCniBackendServerMockRecorder) CheckIP(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIP", reflect.TypeOf((*MockCniBackendServer)(nil).CheckIP), arg0, arg1)
}

// ReleaseIP mocks base method.
func (m *MockCniBackendServer) ReleaseIP(arg0 context.Context, arg1 *rpc.Request) (*rpc.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReleaseIP", arg0, arg1)
	ret0, _ := ret[0].(*rpc.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReleaseIP indicates an expected call of ReleaseIP.
func (mr *MockCniBackendServerMockRecorder) ReleaseIP(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReleaseIP", reflect.TypeOf((*MockCniBackendServer)(nil).ReleaseIP), arg0, arg1)
}
