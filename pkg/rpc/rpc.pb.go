// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: pkg/rpc/rpc.proto

package rpc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type IPType int32

const (
	IPType_ENIMultiIPType IPType = 0
)

// Enum value maps for IPType.
var (
	IPType_name = map[int32]string{
		0: "ENIMultiIPType",
	}
	IPType_value = map[string]int32{
		"ENIMultiIPType": 0,
	}
)

func (x IPType) Enum() *IPType {
	p := new(IPType)
	*p = x
	return p
}

func (x IPType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (IPType) Descriptor() protoreflect.EnumDescriptor {
	return file_pkg_rpc_rpc_proto_enumTypes[0].Descriptor()
}

func (IPType) Type() protoreflect.EnumType {
	return &file_pkg_rpc_rpc_proto_enumTypes[0]
}

func (x IPType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use IPType.Descriptor instead.
func (IPType) EnumDescriptor() ([]byte, []int) {
	return file_pkg_rpc_rpc_proto_rawDescGZIP(), []int{0}
}

type Request struct {
	state                  protoimpl.MessageState `protogen:"open.v1"`
	K8SPodName             string                 `protobuf:"bytes,1,opt,name=K8sPodName,proto3" json:"K8sPodName,omitempty"`
	K8SPodNamespace        string                 `protobuf:"bytes,2,opt,name=K8sPodNamespace,proto3" json:"K8sPodNamespace,omitempty"`
	K8SPodInfraContainerID string                 `protobuf:"bytes,3,opt,name=K8sPodInfraContainerID,proto3" json:"K8sPodInfraContainerID,omitempty"`
	Netns                  string                 `protobuf:"bytes,4,opt,name=Netns,proto3" json:"Netns,omitempty"`
	IfName                 string                 `protobuf:"bytes,5,opt,name=IfName,proto3" json:"IfName,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *Request) Reset() {
	*x = Request{}
	mi := &file_pkg_rpc_rpc_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request) ProtoMessage() {}

func (x *Request) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_rpc_rpc_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request.ProtoReflect.Descriptor instead.
func (*Request) Descriptor() ([]byte, []int) {
	return file_pkg_rpc_rpc_proto_rawDescGZIP(), []int{0}
}

func (x *Request) GetK8SPodName() string {
	if x != nil {
		return x.K8SPodName
	}
	return ""
}

func (x *Request) GetK8SPodNamespace() string {
	if x != nil {
		return x.K8SPodNamespace
	}
	return ""
}

func (x *Request) GetK8SPodInfraContainerID() string {
	if x != nil {
		return x.K8SPodInfraContainerID
	}
	return ""
}

func (x *Request) GetNetns() string {
	if x != nil {
		return x.Netns
	}
	return ""
}

func (x *Request) GetIfName() string {
	if x != nil {
		return x.IfName
	}
	return ""
}

type Response struct {
	state     protoimpl.MessageState `protogen:"open.v1"`
	IsSuccess bool                   `protobuf:"varint,1,opt,name=IsSuccess,proto3" json:"IsSuccess,omitempty"`
	ErrMsg    string                 `protobuf:"bytes,2,opt,name=ErrMsg,proto3" json:"ErrMsg,omitempty"`
	IPType    IPType                 `protobuf:"varint,3,opt,name=IPType,proto3,enum=rpc.IPType" json:"IPType,omitempty"`
	// Types that are valid to be assigned to NetworkInfo:
	//
	//	*Response_ENIMultiIP
	NetworkInfo   isResponse_NetworkInfo `protobuf_oneof:"NetworkInfo"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Response) Reset() {
	*x = Response{}
	mi := &file_pkg_rpc_rpc_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Response) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response) ProtoMessage() {}

func (x *Response) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_rpc_rpc_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response.ProtoReflect.Descriptor instead.
func (*Response) Descriptor() ([]byte, []int) {
	return file_pkg_rpc_rpc_proto_rawDescGZIP(), []int{1}
}

func (x *Response) GetIsSuccess() bool {
	if x != nil {
		return x.IsSuccess
	}
	return false
}

func (x *Response) GetErrMsg() string {
	if x != nil {
		return x.ErrMsg
	}
	return ""
}

func (x *Response) GetIPType() IPType {
	if x != nil {
		return x.IPType
	}
	return IPType_ENIMultiIPType
}

func (x *Response) GetNetworkInfo() isResponse_NetworkInfo {
	if x != nil {
		return x.NetworkInfo
	}
	return nil
}

func (x *Response) GetENIMultiIP() *ENIMultiIPReply {
	if x != nil {
		if x, ok := x.NetworkInfo.(*Response_ENIMultiIP); ok {
			return x.ENIMultiIP
		}
	}
	return nil
}

type isResponse_NetworkInfo interface {
	isResponse_NetworkInfo()
}

type Response_ENIMultiIP struct {
	ENIMultiIP *ENIMultiIPReply `protobuf:"bytes,4,opt,name=ENIMultiIP,proto3,oneof"`
}

func (*Response_ENIMultiIP) isResponse_NetworkInfo() {}

type ENIMultiIPReply struct {
	state                      protoimpl.MessageState `protogen:"open.v1"`
	IP                         string                 `protobuf:"bytes,1,opt,name=IP,proto3" json:"IP,omitempty"`
	Mac                        string                 `protobuf:"bytes,2,opt,name=Mac,proto3" json:"Mac,omitempty"`
	Gateway                    string                 `protobuf:"bytes,3,opt,name=Gateway,proto3" json:"Gateway,omitempty"`
	EniNetns                   string                 `protobuf:"bytes,4,opt,name=EniNetns,proto3" json:"EniNetns,omitempty"`
	EniRequireUniqueRouteTable bool                   `protobuf:"varint,5,opt,name=EniRequireUniqueRouteTable,proto3" json:"EniRequireUniqueRouteTable,omitempty"`
	EniRouteTable              int32                  `protobuf:"varint,6,opt,name=EniRouteTable,proto3" json:"EniRouteTable,omitempty"`
	MultiIPs                   []string               `protobuf:"bytes,7,rep,name=MultiIPs,proto3" json:"MultiIPs,omitempty"`
	IPv6IP                     string                 `protobuf:"bytes,8,opt,name=IPv6IP,proto3" json:"IPv6IP,omitempty"`
	unknownFields              protoimpl.UnknownFields
	sizeCache                  protoimpl.SizeCache
}

func (x *ENIMultiIPReply) Reset() {
	*x = ENIMultiIPReply{}
	mi := &file_pkg_rpc_rpc_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ENIMultiIPReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ENIMultiIPReply) ProtoMessage() {}

func (x *ENIMultiIPReply) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_rpc_rpc_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ENIMultiIPReply.ProtoReflect.Descriptor instead.
func (*ENIMultiIPReply) Descriptor() ([]byte, []int) {
	return file_pkg_rpc_rpc_proto_rawDescGZIP(), []int{2}
}

func (x *ENIMultiIPReply) GetIP() string {
	if x != nil {
		return x.IP
	}
	return ""
}

func (x *ENIMultiIPReply) GetMac() string {
	if x != nil {
		return x.Mac
	}
	return ""
}

func (x *ENIMultiIPReply) GetGateway() string {
	if x != nil {
		return x.Gateway
	}
	return ""
}

func (x *ENIMultiIPReply) GetEniNetns() string {
	if x != nil {
		return x.EniNetns
	}
	return ""
}

func (x *ENIMultiIPReply) GetEniRequireUniqueRouteTable() bool {
	if x != nil {
		return x.EniRequireUniqueRouteTable
	}
	return false
}

func (x *ENIMultiIPReply) GetEniRouteTable() int32 {
	if x != nil {
		return x.EniRouteTable
	}
	return 0
}

func (x *ENIMultiIPReply) GetMultiIPs() []string {
	if x != nil {
		return x.MultiIPs
	}
	return nil
}

func (x *ENIMultiIPReply) GetIPv6IP() string {
	if x != nil {
		return x.IPv6IP
	}
	return ""
}

var File_pkg_rpc_rpc_proto protoreflect.FileDescriptor

const file_pkg_rpc_rpc_proto_rawDesc = "" +
	"\n" +
	"\x11pkg/rpc/rpc.proto\x12\x03rpc\"\xb9\x01\n" +
	"\aRequest\x12\x1e\n" +
	"\n" +
	"K8sPodName\x18\x01 \x01(\tR\n" +
	"K8sPodName\x12(\n" +
	"\x0fK8sPodNamespace\x18\x02 \x01(\tR\x0fK8sPodNamespace\x126\n" +
	"\x16K8sPodInfraContainerID\x18\x03 \x01(\tR\x16K8sPodInfraContainerID\x12\x14\n" +
	"\x05Netns\x18\x04 \x01(\tR\x05Netns\x12\x16\n" +
	"\x06IfName\x18\x05 \x01(\tR\x06IfName\"\xac\x01\n" +
	"\bResponse\x12\x1c\n" +
	"\tIsSuccess\x18\x01 \x01(\bR\tIsSuccess\x12\x16\n" +
	"\x06ErrMsg\x18\x02 \x01(\tR\x06ErrMsg\x12#\n" +
	"\x06IPType\x18\x03 \x01(\x0e2\v.rpc.IPTypeR\x06IPType\x126\n" +
	"\n" +
	"ENIMultiIP\x18\x04 \x01(\v2\x14.rpc.ENIMultiIPReplyH\x00R\n" +
	"ENIMultiIPB\r\n" +
	"\vNetworkInfo\"\x83\x02\n" +
	"\x0fENIMultiIPReply\x12\x0e\n" +
	"\x02IP\x18\x01 \x01(\tR\x02IP\x12\x10\n" +
	"\x03Mac\x18\x02 \x01(\tR\x03Mac\x12\x18\n" +
	"\aGateway\x18\x03 \x01(\tR\aGateway\x12\x1a\n" +
	"\bEniNetns\x18\x04 \x01(\tR\bEniNetns\x12>\n" +
	"\x1aEniRequireUniqueRouteTable\x18\x05 \x01(\bR\x1aEniRequireUniqueRouteTable\x12$\n" +
	"\rEniRouteTable\x18\x06 \x01(\x05R\rEniRouteTable\x12\x1a\n" +
	"\bMultiIPs\x18\a \x03(\tR\bMultiIPs\x12\x16\n" +
	"\x06IPv6IP\x18\b \x01(\tR\x06IPv6IP*\x1c\n" +
	"\x06IPType\x12\x12\n" +
	"\x0eENIMultiIPType\x10\x002\x8f\x01\n" +
	"\n" +
	"CniBackend\x12+\n" +
	"\n" +
	"AllocateIP\x12\f.rpc.Request\x1a\r.rpc.Response\"\x00\x12*\n" +
	"\tReleaseIP\x12\f.rpc.Request\x1a\r.rpc.Response\"\x00\x12(\n" +
	"\aCheckIP\x12\f.rpc.Request\x1a\r.rpc.Response\"\x00B7Z5icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/rpc;rpcb\x06proto3"

var (
	file_pkg_rpc_rpc_proto_rawDescOnce sync.Once
	file_pkg_rpc_rpc_proto_rawDescData []byte
)

func file_pkg_rpc_rpc_proto_rawDescGZIP() []byte {
	file_pkg_rpc_rpc_proto_rawDescOnce.Do(func() {
		file_pkg_rpc_rpc_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pkg_rpc_rpc_proto_rawDesc), len(file_pkg_rpc_rpc_proto_rawDesc)))
	})
	return file_pkg_rpc_rpc_proto_rawDescData
}

var file_pkg_rpc_rpc_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_pkg_rpc_rpc_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_pkg_rpc_rpc_proto_goTypes = []any{
	(IPType)(0),             // 0: rpc.IPType
	(*Request)(nil),         // 1: rpc.Request
	(*Response)(nil),        // 2: rpc.Response
	(*ENIMultiIPReply)(nil), // 3: rpc.ENIMultiIPReply
}
var file_pkg_rpc_rpc_proto_depIdxs = []int32{
	0, // 0: rpc.Response.IPType:type_name -> rpc.IPType
	3, // 1: rpc.Response.ENIMultiIP:type_name -> rpc.ENIMultiIPReply
	1, // 2: rpc.CniBackend.AllocateIP:input_type -> rpc.Request
	1, // 3: rpc.CniBackend.ReleaseIP:input_type -> rpc.Request
	1, // 4: rpc.CniBackend.CheckIP:input_type -> rpc.Request
	2, // 5: rpc.CniBackend.AllocateIP:output_type -> rpc.Response
	2, // 6: rpc.CniBackend.ReleaseIP:output_type -> rpc.Response
	2, // 7: rpc.CniBackend.CheckIP:output_type -> rpc.Response
	5, // [5:8] is the sub-list for method output_type
	2, // [2:5] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_pkg_rpc_rpc_proto_init() }
func file_pkg_rpc_rpc_proto_init() {
	if File_pkg_rpc_rpc_proto != nil {
		return
	}
	file_pkg_rpc_rpc_proto_msgTypes[1].OneofWrappers = []any{
		(*Response_ENIMultiIP)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pkg_rpc_rpc_proto_rawDesc), len(file_pkg_rpc_rpc_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pkg_rpc_rpc_proto_goTypes,
		DependencyIndexes: file_pkg_rpc_rpc_proto_depIdxs,
		EnumInfos:         file_pkg_rpc_rpc_proto_enumTypes,
		MessageInfos:      file_pkg_rpc_rpc_proto_msgTypes,
	}.Build()
	File_pkg_rpc_rpc_proto = out.File
	file_pkg_rpc_rpc_proto_goTypes = nil
	file_pkg_rpc_rpc_proto_depIdxs = nil
}
