// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: pkg/rpc/rpc.proto

package rpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	CniBackend_AllocateIP_FullMethodName = "/rpc.CniBackend/AllocateIP"
	CniBackend_ReleaseIP_FullMethodName  = "/rpc.CniBackend/ReleaseIP"
	CniBackend_CheckIP_FullMethodName    = "/rpc.CniBackend/CheckIP"
)

// CniBackendClient is the client API for CniBackend service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// The service definition.
type CniBackendClient interface {
	AllocateIP(ctx context.Context, in *Request, opts ...grpc.CallOption) (*Response, error)
	ReleaseIP(ctx context.Context, in *Request, opts ...grpc.CallOption) (*Response, error)
	CheckIP(ctx context.Context, in *Request, opts ...grpc.CallOption) (*Response, error)
}

type cniBackendClient struct {
	cc grpc.ClientConnInterface
}

func NewCniBackendClient(cc grpc.ClientConnInterface) CniBackendClient {
	return &cniBackendClient{cc}
}

func (c *cniBackendClient) AllocateIP(ctx context.Context, in *Request, opts ...grpc.CallOption) (*Response, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Response)
	err := c.cc.Invoke(ctx, CniBackend_AllocateIP_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cniBackendClient) ReleaseIP(ctx context.Context, in *Request, opts ...grpc.CallOption) (*Response, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Response)
	err := c.cc.Invoke(ctx, CniBackend_ReleaseIP_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cniBackendClient) CheckIP(ctx context.Context, in *Request, opts ...grpc.CallOption) (*Response, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Response)
	err := c.cc.Invoke(ctx, CniBackend_CheckIP_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CniBackendServer is the server API for CniBackend service.
// All implementations must embed UnimplementedCniBackendServer
// for forward compatibility.
//
// The service definition.
type CniBackendServer interface {
	AllocateIP(context.Context, *Request) (*Response, error)
	ReleaseIP(context.Context, *Request) (*Response, error)
	CheckIP(context.Context, *Request) (*Response, error)
	mustEmbedUnimplementedCniBackendServer()
}

// UnimplementedCniBackendServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedCniBackendServer struct{}

func (UnimplementedCniBackendServer) AllocateIP(context.Context, *Request) (*Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AllocateIP not implemented")
}
func (UnimplementedCniBackendServer) ReleaseIP(context.Context, *Request) (*Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReleaseIP not implemented")
}
func (UnimplementedCniBackendServer) CheckIP(context.Context, *Request) (*Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckIP not implemented")
}
func (UnimplementedCniBackendServer) mustEmbedUnimplementedCniBackendServer() {}
func (UnimplementedCniBackendServer) testEmbeddedByValue()                    {}

// UnsafeCniBackendServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CniBackendServer will
// result in compilation errors.
type UnsafeCniBackendServer interface {
	mustEmbedUnimplementedCniBackendServer()
}

func RegisterCniBackendServer(s grpc.ServiceRegistrar, srv CniBackendServer) {
	// If the following call pancis, it indicates UnimplementedCniBackendServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&CniBackend_ServiceDesc, srv)
}

func _CniBackend_AllocateIP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CniBackendServer).AllocateIP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CniBackend_AllocateIP_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CniBackendServer).AllocateIP(ctx, req.(*Request))
	}
	return interceptor(ctx, in, info, handler)
}

func _CniBackend_ReleaseIP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CniBackendServer).ReleaseIP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CniBackend_ReleaseIP_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CniBackendServer).ReleaseIP(ctx, req.(*Request))
	}
	return interceptor(ctx, in, info, handler)
}

func _CniBackend_CheckIP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CniBackendServer).CheckIP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CniBackend_CheckIP_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CniBackendServer).CheckIP(ctx, req.(*Request))
	}
	return interceptor(ctx, in, info, handler)
}

// CniBackend_ServiceDesc is the grpc.ServiceDesc for CniBackend service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CniBackend_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "rpc.CniBackend",
	HandlerType: (*CniBackendServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AllocateIP",
			Handler:    _CniBackend_AllocateIP_Handler,
		},
		{
			MethodName: "ReleaseIP",
			Handler:    _CniBackend_ReleaseIP_Handler,
		},
		{
			MethodName: "CheckIP",
			Handler:    _CniBackend_CheckIP_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pkg/rpc/rpc.proto",
}
