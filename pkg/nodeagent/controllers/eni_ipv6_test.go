package controllers

import (
	"context"
	"net"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/vishvananda/netlink"

	networkingv1 "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/apis/networking/v1"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/netlinkwrapper"
)

// MockNetlinkWrapper 是 netlinkwrapper.NetlinkWrapper 的模拟实现
type MockNetlinkWrapper struct {
	mock.Mock
}

func (m *MockNetlinkWrapper) AddrList(link netlink.Link, family int) ([]netlink.Addr, error) {
	args := m.Called(link, family)
	return args.Get(0).([]netlink.Addr), args.Error(1)
}

func (m *MockNetlinkWrapper) RouteList(link netlink.Link, family int) ([]netlink.Route, error) {
	args := m.Called(link, family)
	return args.Get(0).([]netlink.Route), args.Error(1)
}

func (m *MockNetlinkWrapper) RouteAdd(route *netlink.Route) error {
	args := m.Called(route)
	return args.Error(0)
}

func (m *MockNetlinkWrapper) RouteReplace(route *netlink.Route) error {
	args := m.Called(route)
	return args.Error(0)
}

func (m *MockNetlinkWrapper) AddrAdd(link netlink.Link, addr *netlink.Addr) error {
	args := m.Called(link, addr)
	return args.Error(0)
}

func (m *MockNetlinkWrapper) LinkByName(name string) (netlink.Link, error) {
	args := m.Called(name)
	return args.Get(0).(netlink.Link), args.Error(1)
}

func (m *MockNetlinkWrapper) RouteDel(route *netlink.Route) error {
	args := m.Called(route)
	return args.Error(0)
}

// MockLink 是 netlink.Link 的模拟实现
type MockLink struct {
	mock.Mock
	index int
	name  string
}

func (m *MockLink) Attrs() *netlink.LinkAttrs {
	return &netlink.LinkAttrs{
		Index: m.index,
		Name:  m.name,
	}
}

func (m *MockLink) Type() string {
	return "mock"
}

func TestCalculateIPv6GatewayFromHostRoute(t *testing.T) {
	reconciler := &BciNodeReconciler{}

	tests := []struct {
		name     string
		ip       string
		expected string
	}{
		{
			name:     "IPv6 address in 240c:4085:4:3101::/64",
			ip:       "240c:4085:4:3101::2",
			expected: "240c:4085:4:3101::1",
		},
		{
			name:     "IPv6 address in 240c:4085:4:3101::/64 with different host part",
			ip:       "240c:4085:4:3101::6",
			expected: "240c:4085:4:3101::1",
		},
		{
			name:     "Different subnet",
			ip:       "2001:db8:85a3::8a2e",
			expected: "2001:db8:85a3::1",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ip := net.ParseIP(tt.ip)
			expected := net.ParseIP(tt.expected)
			
			result := reconciler.calculateIPv6GatewayFromHostRoute(ip)
			
			assert.Equal(t, expected, result, "Gateway calculation mismatch")
		})
	}
}

func TestIsInSameIPv6Subnet(t *testing.T) {
	reconciler := &BciNodeReconciler{}

	tests := []struct {
		name     string
		ip1      string
		ip2      string
		expected bool
	}{
		{
			name:     "Same /64 subnet",
			ip1:      "240c:4085:4:3101::2",
			ip2:      "240c:4085:4:3101::1",
			expected: true,
		},
		{
			name:     "Same /64 subnet different host parts",
			ip1:      "240c:4085:4:3101::6",
			ip2:      "240c:4085:4:3101::1",
			expected: true,
		},
		{
			name:     "Different subnets",
			ip1:      "240c:4085:4:3101::2",
			ip2:      "240c:4085:4:3102::1",
			expected: false,
		},
		{
			name:     "IPv4 addresses",
			ip1:      "***********",
			ip2:      "***********",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ip1 := net.ParseIP(tt.ip1)
			ip2 := net.ParseIP(tt.ip2)
			
			result := reconciler.isInSameIPv6Subnet(ip1, ip2)
			
			assert.Equal(t, tt.expected, result, "Subnet check mismatch")
		})
	}
}

func TestInferIPv6Subnet(t *testing.T) {
	reconciler := &BciNodeReconciler{}

	tests := []struct {
		name     string
		ip       string
		gateway  string
		expected string
	}{
		{
			name:     "Same /64 subnet",
			ip:       "240c:4085:4:3101::2",
			gateway:  "240c:4085:4:3101::1",
			expected: "240c:4085:4:3101::/64",
		},
		{
			name:     "Different subnets",
			ip:       "240c:4085:4:3101::2",
			gateway:  "240c:4085:4:3102::1",
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ip := net.ParseIP(tt.ip)
			gateway := net.ParseIP(tt.gateway)
			
			result := reconciler.inferIPv6Subnet(ip, gateway)
			
			if tt.expected == "" {
				assert.Nil(t, result, "Expected nil subnet")
			} else {
				_, expectedNet, _ := net.ParseCIDR(tt.expected)
				assert.Equal(t, expectedNet, result, "Subnet inference mismatch")
			}
		})
	}
}
