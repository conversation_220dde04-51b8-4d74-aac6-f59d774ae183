/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package controllers

import (
	"context"
	"reflect"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"

	networkingv1 "icode.baidu.com/baidu/bci2/bci-cni-driver/apis/networking/v1"
	metadata "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/meta-data"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/logger"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/nodeagent/ipam"
	netlinkwrapper "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/netlink"
)

const (
	PFSEnabledNodeLabel     = "bci-node-pfs-enabled"
	PFSEnabledNodeLabelTrue = "true"
)

var (
	RequireUniqueRouteTable bool = false
)

// BciNodeReconciler reconciles a BciNode object
type BciNodeReconciler struct {
	client.Client
	UnCachedClient client.Client
	Scheme         *runtime.Scheme
	bciNodeCache   networkingv1.BciNode
	ipamCtrl       *ipam.IPAM
	nodeName       string
	nlink          netlinkwrapper.Interface
	metaclient     metadata.Interface
}

// +kubebuilder:rbac:groups=networking.bci.cloud.baidu.com,resources=bcinodes,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=networking.bci.cloud.baidu.com,resources=bcinodes/status,verbs=get;update;patch
// +kubebuilder:rbac:groups=networking.bci.cloud.baidu.com,resources=bcinodes/finalizers,verbs=update

// Reconcile is part of the main kubernetes reconciliation loop which aims to
// move the current state of the cluster closer to the desired state.
// TODO(user): Modify the Reconcile function to compare the state specified by
// the BciNode object against the actual cluster state, and then
// perform operations to make the cluster state reflect the state specified by
// the user.
//
// For more details, check Reconcile and its Result here:
// - https://pkg.go.dev/sigs.k8s.io/controller-runtime@v0.10.0/pkg/reconcile
func (r *BciNodeReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	logger.Infof(ctx, "reconcile %s, %s, %s", req.Name, req.Namespace, req.NamespacedName)

	var (
		userAllocAdd = networkingv1.AllocationMap{}
		userAllocDel = networkingv1.AllocationMap{}
	)
	// get crd
	bciNode := networkingv1.BciNode{}
	if err := r.Get(ctx, req.NamespacedName, &bciNode); err != nil {
		if client.IgnoreNotFound(err) != nil {
			logger.Errorf(ctx, "unable to get bciNode", err)
		}
		return ctrl.Result{}, nil
	}
	if r.ipamCtrl.BciNode == nil {
		// only status need to be recovery after reboot
		// beacuse status in bcinode may not up to date
		r.ipamCtrl.RecoveryFromReboot(ctx, &bciNode.Spec)
	}
	// check crd delete event
	if bciNode.DeletionTimestamp != nil {
		// if still have pod on node, delete events will make pod no network
		logger.Infof(ctx, "get crd delete events !!!!")
		r.bciNodeCache = networkingv1.BciNode{}
		r.ipamCtrl.BciNode = nil
		tmp := bciNode.DeepCopy()
		tmp.Finalizers = nil

		_ = r.HandleDel(ctx, bciNode.Spec.EniMultiIP.Pool)
		r.ipamCtrl.DeleteUserIdlePool(ctx, bciNode.Spec.EniMultiIP.Pool)

		err := r.Update(ctx, tmp)
		if err != nil {
			logger.Errorf(ctx, "update bci node spec error: %v", err)
		}

		return ctrl.Result{}, nil
	}

	r.ipamCtrl.Lock()
	// WaitReleaseIP will be update every time bcinode changed
	r.ipamCtrl.WaitReleaseIP = bciNode.Status.DeepCopy().WaitReleaseIP
	logger.Infof(ctx, "controller updated wait release ip: %v", r.ipamCtrl.WaitReleaseIP)
	r.ipamCtrl.Unlock()

	if reflect.DeepEqual(r.bciNodeCache.Spec, bciNode.Spec) {
		return ctrl.Result{}, nil
	}

	r.ipamCtrl.Lock()
	// spec will be update every time bcinode changed
	r.ipamCtrl.BciNode.Spec = *bciNode.Spec.DeepCopy()
	r.ipamCtrl.Unlock()

	userAllocAdd, userAllocDel = r.DiffAllocationMap(r.bciNodeCache.Spec.EniMultiIP.Pool, bciNode.Spec.EniMultiIP.Pool)
	logger.Infof(ctx, "add user: %+v", userAllocAdd)
	logger.Infof(ctx, "del user: %+v", userAllocDel)
	r.bciNodeCache = *bciNode.DeepCopy()

	// rebuild queue
	r.ipamCtrl.RebuildUserIdlePool(ctx, bciNode.Spec.EniMultiIP.Pool)

	err := r.SetupEni(ctx, userAllocAdd, userAllocDel)
	if err != nil {
		// if set up failed, only remove deleted eni
		// r.ipamCtrl.UpdateUserIdlePool(ctx, networkingv1.AllocationMap{}, userAllocDel)
		return ctrl.Result{}, err
	}
	// after init eni success
	// r.ipamCtrl.UpdateUserIdlePool(ctx, userAllocAdd, userAllocDel)

	logger.Infof(ctx, "reconcile finished")
	return ctrl.Result{}, nil
}

func (r *BciNodeReconciler) SetupEni(ctx context.Context, addEnis networkingv1.AllocationMap, delEnis networkingv1.AllocationMap) error {
	// Considering:
	// 1. an node may have more than one users
	// we only manipilate eni dev when crd is changed
	if len(delEnis) != 0 {
		err := r.HandleDel(ctx, delEnis)
		if err != nil {
			return err
		}
	}

	if len(addEnis) != 0 {
		errEnis, addErr := r.HandleAdd(ctx, addEnis)
		if addErr != nil {
			err := r.ipamCtrl.MarkEniNotReady(ctx, errEnis)
			if err != nil {
				logger.Errorf(ctx, "update not ready enis status error: %v", err)
			}
			return addErr
		}
	}

	return nil
}

func NewBciNodeController(client, unCachedClient client.Client, schem *runtime.Scheme, ipamCtrl *ipam.IPAM, nodeName string) *BciNodeReconciler {
	return &BciNodeReconciler{
		Client:         client,
		UnCachedClient: unCachedClient,
		Scheme:         schem,
		ipamCtrl:       ipamCtrl,
		nodeName:       nodeName,
		nlink:          netlinkwrapper.New(),
		metaclient:     metadata.NewClient("", ""),
	}
}

// SetupWithManager sets up the controller with the Manager.
func (r *BciNodeReconciler) SetupWithManager(mgr ctrl.Manager) error {
	return ctrl.NewControllerManagedBy(mgr).
		For(&networkingv1.BciNode{}).
		Complete(r)
}

// for bci2.1:
// 1. eni privateIP fixed number
// 2. node may have more than one eni to support different subnet
// not open dynamic private IP number support.
func (r *BciNodeReconciler) DiffAllocationMap(
	old networkingv1.AllocationMap,
	new networkingv1.AllocationMap,
) (networkingv1.AllocationMap, networkingv1.AllocationMap) {
	add := networkingv1.AllocationMap{}
	del := networkingv1.AllocationMap{}
	// get add user
	for userid, userEnis := range new {
		if _, ok := old[userid]; !ok {
			add[userid] = userEnis
		} else {
			for eniID, enis := range new[userid] {
				if _, ok := old[userid][eniID]; !ok {
					if _, ok := add[userid][eniID]; !ok {
						add[userid] = networkingv1.UserAllocationEnis{}
					}
					add[userid][eniID] = enis.DeepCopy()
				}
			}
		}
	}
	// get del user
	for userid, userEnis := range old {
		if _, ok := new[userid]; !ok {
			del[userid] = userEnis
		} else {
			for eniID, enis := range old[userid] {
				if _, ok := new[userid][eniID]; !ok {
					if _, ok := del[userid][eniID]; !ok {
						del[userid] = networkingv1.UserAllocationEnis{}
					}
					del[userid][eniID] = enis.DeepCopy()
				}
			}
		}
	}

	return add, del
}

func (r *BciNodeReconciler) isPFSNode(ctx context.Context, nodeName string) (bool, error) {
	var (
		node = &corev1.Node{}
	)

	err := r.UnCachedClient.Get(ctx, types.NamespacedName{
		Name: nodeName,
	}, node)

	if err != nil {
		logger.Errorf(ctx, "isPFSNode: failed to get node %v: %v", nodeName, err)
		return false, err
	}

	if node.Labels == nil {
		node.Labels = make(map[string]string)
	}

	if node.Labels[PFSEnabledNodeLabel] == PFSEnabledNodeLabelTrue {
		return true, nil
	}

	return false, nil
}

func (r *BciNodeReconciler) requireUniqueRouteTable(ctx context.Context, nodeName string) (bool, error) {
	return r.isPFSNode(ctx, nodeName)
}

func (r *BciNodeReconciler) CheckRequireUniqueRouteTable(ctx context.Context, nodeName string) error {
	res, err := r.requireUniqueRouteTable(ctx, nodeName)
	if err != nil {
		return err
	}

	RequireUniqueRouteTable = res
	ipam.RequireUniqueRouteTable = res

	logger.Infof(ctx, "node %v requirement of unique route table: %v", nodeName, RequireUniqueRouteTable)
	return nil
}
