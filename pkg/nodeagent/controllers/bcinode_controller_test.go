/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package controllers

import (
	"context"
	"net"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	corev1 "k8s.io/api/core/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"

	networkingv1 "icode.baidu.com/baidu/bci2/bci-cni-driver/apis/networking/v1"
	metadata "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/meta-data"
	mockmetadata "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/meta-data/mock"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/nodeagent/ipam"
	mockipam "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/nodeagent/ipam/mock"
	netlinkwrapper "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/netlink"
	mocknetlink "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/netlink/mock"
)

func setupEnv(t *testing.T) (
	*gomock.Controller,
	*mockipam.MockRuntimeClient,
	*mocknetlink.MockInterface,
	*mockmetadata.MockInterface,
) {
	ctrl := gomock.NewController(t)

	client := mockipam.NewMockRuntimeClient(ctrl)
	nlink := mocknetlink.NewMockInterface(ctrl)
	meta := mockmetadata.NewMockInterface(ctrl)

	return ctrl, client, nlink, meta
}

func TestNewBciNodeController(t *testing.T) {
	type args struct {
		client         client.Client
		uncachedClient client.Client
		schem          *runtime.Scheme
		ipamCtrl       *ipam.IPAM
		nodeName       string
	}
	tests := []struct {
		name string
		args args
		want *BciNodeReconciler
	}{
		{
			name: "正常流程",
			args: args{
				nodeName: "node",
			},
			want: &BciNodeReconciler{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewBciNodeController(tt.args.client, tt.args.uncachedClient,
				tt.args.schem, tt.args.ipamCtrl, tt.args.nodeName); !reflect.DeepEqual(got, tt.want) && got == nil {
				t.Errorf("NewBciNodeController() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestBciNodeReconciler_isPFSNode(t *testing.T) {
	type fields struct {
		ctrl           *gomock.Controller
		UnCachedClient client.Client
		Scheme         *runtime.Scheme
		bciNodeCache   networkingv1.BciNode
		ipamCtrl       *ipam.IPAM
		nodeName       string
		nlink          netlinkwrapper.Interface
		metaclient     metadata.Interface
	}
	type args struct {
		ctx      context.Context
		nodeName string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    bool
		wantErr bool
	}{
		{
			name: "正常流程",
			fields: func() fields {
				ctrl, client, _, _ := setupEnv(t)

				client.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).SetArg(2, corev1.Node{
					TypeMeta: v1.TypeMeta{
						Kind:       "",
						APIVersion: "",
					},
					ObjectMeta: v1.ObjectMeta{
						Name:            "",
						GenerateName:    "",
						Namespace:       "",
						SelfLink:        "",
						UID:             "",
						ResourceVersion: "",
						Generation:      0,
						CreationTimestamp: v1.Time{
							Time: time.Time{},
						},
						DeletionTimestamp: &v1.Time{
							Time: time.Time{},
						},
						DeletionGracePeriodSeconds: nil,
						Labels:                     nil,
						Annotations: map[string]string{
							"": "",
						},
						OwnerReferences: []v1.OwnerReference{},
						Finalizers:      []string{},
						ClusterName:     "",
						ManagedFields:   []v1.ManagedFieldsEntry{},
					},
					Spec: corev1.NodeSpec{
						PodCIDR:            "",
						PodCIDRs:           []string{},
						ProviderID:         "",
						Unschedulable:      false,
						Taints:             []corev1.Taint{},
						DoNotUseExternalID: "",
					},
				}).Return(nil)

				return fields{
					ctrl:           ctrl,
					UnCachedClient: client,
					Scheme:         &runtime.Scheme{},
					ipamCtrl:       &ipam.IPAM{},
					nodeName:       "node",
					nlink:          nil,
					metaclient:     nil,
				}
			}(),
			args: args{
				ctx:      context.TODO(),
				nodeName: "node",
			},
			want:    false,
			wantErr: false,
		},
		{
			name: "正常流程",
			fields: func() fields {
				ctrl, client, _, _ := setupEnv(t)

				client.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).SetArg(2, corev1.Node{
					TypeMeta: v1.TypeMeta{
						Kind:       "",
						APIVersion: "",
					},
					ObjectMeta: v1.ObjectMeta{
						Name:            "",
						GenerateName:    "",
						Namespace:       "",
						SelfLink:        "",
						UID:             "",
						ResourceVersion: "",
						Generation:      0,
						CreationTimestamp: v1.Time{
							Time: time.Time{},
						},
						DeletionTimestamp: &v1.Time{
							Time: time.Time{},
						},
						DeletionGracePeriodSeconds: nil,
						Labels: map[string]string{
							PFSEnabledNodeLabel: PFSEnabledNodeLabelTrue,
						},
						Annotations: map[string]string{
							"": "",
						},
						OwnerReferences: []v1.OwnerReference{},
						Finalizers:      []string{},
						ClusterName:     "",
						ManagedFields:   []v1.ManagedFieldsEntry{},
					},
					Spec: corev1.NodeSpec{
						PodCIDR:            "",
						PodCIDRs:           []string{},
						ProviderID:         "",
						Unschedulable:      false,
						Taints:             []corev1.Taint{},
						DoNotUseExternalID: "",
					},
				}).Return(nil)

				return fields{
					ctrl:           ctrl,
					UnCachedClient: client,
					Scheme:         &runtime.Scheme{},
					ipamCtrl:       &ipam.IPAM{},
					nodeName:       "node",
					nlink:          nil,
					metaclient:     nil,
				}
			}(),
			args: args{
				ctx:      context.TODO(),
				nodeName: "node",
			},
			want:    true,
			wantErr: false,
		},
		{
			name: "出错流程",
			fields: func() fields {
				ctrl, client, _, _ := setupEnv(t)

				client.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(net.ErrClosed)

				return fields{
					ctrl:           ctrl,
					UnCachedClient: client,
					Scheme:         &runtime.Scheme{},
					ipamCtrl:       &ipam.IPAM{},
					nodeName:       "node",
					nlink:          nil,
					metaclient:     nil,
				}
			}(),
			args: args{
				ctx:      context.TODO(),
				nodeName: "node",
			},
			want:    false,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}

			r := &BciNodeReconciler{
				UnCachedClient: tt.fields.UnCachedClient,
				Scheme:         tt.fields.Scheme,
				bciNodeCache:   tt.fields.bciNodeCache,
				ipamCtrl:       tt.fields.ipamCtrl,
				nodeName:       tt.fields.nodeName,
				nlink:          tt.fields.nlink,
				metaclient:     tt.fields.metaclient,
			}
			got, err := r.isPFSNode(tt.args.ctx, tt.args.nodeName)
			if (err != nil) != tt.wantErr {
				t.Errorf("BciNodeReconciler.isPFSNode() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("BciNodeReconciler.isPFSNode() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestBciNodeReconciler_requireUniqueRouteTable(t *testing.T) {
	type fields struct {
		ctrl           *gomock.Controller
		UnCachedClient client.Client
		Scheme         *runtime.Scheme
		bciNodeCache   networkingv1.BciNode
		ipamCtrl       *ipam.IPAM
		nodeName       string
		nlink          netlinkwrapper.Interface
		metaclient     metadata.Interface
	}
	type args struct {
		ctx      context.Context
		nodeName string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    bool
		wantErr bool
	}{
		{
			fields: func() fields {
				ctrl, client, _, _ := setupEnv(t)

				client.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(net.ErrClosed)

				return fields{
					ctrl:           ctrl,
					UnCachedClient: client,
					Scheme:         &runtime.Scheme{},
					ipamCtrl:       &ipam.IPAM{},
					nodeName:       "node",
					nlink:          nil,
					metaclient:     nil,
				}
			}(),
			args: args{
				ctx:      context.TODO(),
				nodeName: "node",
			},
			want:    false,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}

			r := &BciNodeReconciler{
				UnCachedClient: tt.fields.UnCachedClient,
				Scheme:         tt.fields.Scheme,
				bciNodeCache:   tt.fields.bciNodeCache,
				ipamCtrl:       tt.fields.ipamCtrl,
				nodeName:       tt.fields.nodeName,
				nlink:          tt.fields.nlink,
				metaclient:     tt.fields.metaclient,
			}
			got, err := r.requireUniqueRouteTable(tt.args.ctx, tt.args.nodeName)
			if (err != nil) != tt.wantErr {
				t.Errorf("BciNodeReconciler.requireUniqueRouteTable() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("BciNodeReconciler.requireUniqueRouteTable() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestBciNodeReconciler_CheckRequireUniqueRouteTable(t *testing.T) {
	type fields struct {
		ctrl           *gomock.Controller
		UnCachedClient client.Client
		Scheme         *runtime.Scheme
		bciNodeCache   networkingv1.BciNode
		ipamCtrl       *ipam.IPAM
		nodeName       string
		nlink          netlinkwrapper.Interface
		metaclient     metadata.Interface
	}
	type args struct {
		ctx      context.Context
		nodeName string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "正常流程",
			fields: func() fields {
				ctrl, client, _, _ := setupEnv(t)

				client.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).SetArg(2, corev1.Node{
					TypeMeta: v1.TypeMeta{
						Kind:       "",
						APIVersion: "",
					},
					ObjectMeta: v1.ObjectMeta{
						Name:            "",
						GenerateName:    "",
						Namespace:       "",
						SelfLink:        "",
						UID:             "",
						ResourceVersion: "",
						Generation:      0,
						CreationTimestamp: v1.Time{
							Time: time.Time{},
						},
						DeletionTimestamp: &v1.Time{
							Time: time.Time{},
						},
						DeletionGracePeriodSeconds: nil,
						Labels:                     nil,
						Annotations: map[string]string{
							"": "",
						},
						OwnerReferences: []v1.OwnerReference{},
						Finalizers:      []string{},
						ClusterName:     "",
						ManagedFields:   []v1.ManagedFieldsEntry{},
					},
					Spec: corev1.NodeSpec{
						PodCIDR:            "",
						PodCIDRs:           []string{},
						ProviderID:         "",
						Unschedulable:      false,
						Taints:             []corev1.Taint{},
						DoNotUseExternalID: "",
					},
				}).Return(nil)

				return fields{
					ctrl:           ctrl,
					UnCachedClient: client,
					Scheme:         &runtime.Scheme{},
					ipamCtrl:       &ipam.IPAM{},
					nodeName:       "node",
					nlink:          nil,
					metaclient:     nil,
				}
			}(),
			args: args{
				ctx:      context.TODO(),
				nodeName: "node",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}
			r := &BciNodeReconciler{
				UnCachedClient: tt.fields.UnCachedClient,
				Scheme:         tt.fields.Scheme,
				bciNodeCache:   tt.fields.bciNodeCache,
				ipamCtrl:       tt.fields.ipamCtrl,
				nodeName:       tt.fields.nodeName,
				nlink:          tt.fields.nlink,
				metaclient:     tt.fields.metaclient,
			}
			if err := r.CheckRequireUniqueRouteTable(tt.args.ctx, tt.args.nodeName); (err != nil) != tt.wantErr {
				t.Errorf("BciNodeReconciler.CheckRequireUniqueRouteTable() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
