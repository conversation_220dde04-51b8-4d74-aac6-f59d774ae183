/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package server

import (
	"context"
	"net"
	"syscall"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"sigs.k8s.io/controller-runtime/pkg/manager"

	netwrapper "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/net"
	mocknet "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/net/mock"
	oswrapper "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/os"
	mockos "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/os/mock"
)

func TestNewGrpcServer(t *testing.T) {
	type args struct {
		mgr manager.Manager
	}
	tests := []struct {
		name string
		args args
		want RPCServer
	}{
		{
			name: "normal case",
			args: args{
				mgr: nil,
			},
			want: &rpcServer{
				netWrapper: netwrapper.New(),
				osWrapper:  oswrapper.New(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewGrpcServer(tt.args.mgr, nil); got == nil {
				t.Errorf("NewGrpcServer() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_rpcServer_Start(t *testing.T) {
	type fields struct {
		ctrl       *gomock.Controller
		mgr        manager.Manager
		osWrapper  oswrapper.Interface
		netWrapper netwrapper.Interface
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "normal case",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				mockos := mockos.NewMockInterface(ctrl)
				mocknet := mocknet.NewMockInterface(ctrl)

				l, _ := net.Listen("tcp4", "127.0.0.1:")

				gomock.InOrder(
					mockos.EXPECT().MkdirAll(gomock.Any(), gomock.Any()).Return(nil),
					mockos.EXPECT().Remove(GrpcSocketFilePath).Return(nil),
					mocknet.EXPECT().Listen("unix", GrpcSocketFilePath).Return(l, nil),
				)

				return fields{
					ctrl:       ctrl,
					osWrapper:  mockos,
					netWrapper: mocknet,
				}
			}(),
			args: func() args {
				ctx, cancel := context.WithTimeout(context.TODO(), time.Second)
				defer cancel()

				return args{
					ctx: ctx,
				}
			}(),
			wantErr: false,
		},
		{
			name: "remove socket returns ENOENT",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				mockos := mockos.NewMockInterface(ctrl)
				mocknet := mocknet.NewMockInterface(ctrl)

				l, _ := net.Listen("tcp4", "127.0.0.1:")

				gomock.InOrder(
					mockos.EXPECT().MkdirAll(gomock.Any(), gomock.Any()).Return(nil),
					mockos.EXPECT().Remove(GrpcSocketFilePath).Return(syscall.ENOENT),
					mocknet.EXPECT().Listen("unix", GrpcSocketFilePath).Return(l, nil),
				)

				return fields{
					ctrl:       ctrl,
					osWrapper:  mockos,
					netWrapper: mocknet,
				}
			}(),
			args: func() args {
				ctx, cancel := context.WithTimeout(context.TODO(), time.Second)
				defer cancel()

				return args{
					ctx: ctx,
				}
			}(),
			wantErr: false,
		},
		{
			name: "remove socket file failed",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				mockos := mockos.NewMockInterface(ctrl)
				mocknet := mocknet.NewMockInterface(ctrl)

				gomock.InOrder(
					mockos.EXPECT().MkdirAll(gomock.Any(), gomock.Any()).Return(nil),
					mockos.EXPECT().Remove(GrpcSocketFilePath).Return(syscall.EPERM),
				)

				return fields{
					ctrl:       ctrl,
					osWrapper:  mockos,
					netWrapper: mocknet,
				}
			}(),
			args: func() args {
				ctx, cancel := context.WithTimeout(context.TODO(), time.Second)
				defer cancel()

				return args{
					ctx: ctx,
				}
			}(),
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}

			s := &rpcServer{
				mgr:        tt.fields.mgr,
				osWrapper:  tt.fields.osWrapper,
				netWrapper: tt.fields.netWrapper,
			}
			if err := s.Start(tt.args.ctx); (err != nil) != tt.wantErr {
				t.Errorf("rpcServer.Start() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
