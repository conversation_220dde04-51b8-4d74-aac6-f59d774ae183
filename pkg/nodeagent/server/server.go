/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package server

import (
	"context"
	"os"
	"path/filepath"

	"google.golang.org/grpc"
	"sigs.k8s.io/controller-runtime/pkg/manager"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/logger"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/nodeagent/ipam"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/rpc"
	netwrapper "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/net"
	oswrapper "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/os"
)

const (
	GrpcSocketFilePath = "/var/run/bci-cni/bci-cni.socket"
)

var _ RPCServer = &rpcServer{}

type RPCServer interface {
	manager.Runnable
	rpc.CniBackendServer
}

type rpcServer struct {
	rpc.UnimplementedCniBackendServer
	mgr        manager.Manager
	osWrapper  oswrapper.Interface
	netWrapper netwrapper.Interface
	ipamCtrl   ipam.Interface
}

func NewGrpcServer(mgr manager.Manager, ipamCtrl *ipam.IPAM) RPCServer {
	return &rpcServer{
		mgr:        mgr,
		netWrapper: netwrapper.New(),
		osWrapper:  oswrapper.New(),
		ipamCtrl:   ipamCtrl,
	}
}

func (s *rpcServer) Start(ctx context.Context) error {
	ctx = logger.EnsureTraceIDInCtx(ctx)

	var (
		grpcSocketFileDir = filepath.Dir(GrpcSocketFilePath)
	)

	err := s.osWrapper.MkdirAll(grpcSocketFileDir, 0700)
	if err != nil {
		return err
	}

	err = s.osWrapper.Remove(GrpcSocketFilePath)
	if err != nil && !os.IsNotExist(err) {
		logger.Errorf(ctx, "failed to remove %v: %v", GrpcSocketFilePath, err)
		return err
	}

	l, err := s.netWrapper.Listen("unix", GrpcSocketFilePath)
	if err != nil {
		return err
	}

	grpcServer := grpc.NewServer()

	rpc.RegisterCniBackendServer(grpcServer, s)

	go func() {
		err = grpcServer.Serve(l)
		if err != nil {
			logger.Fatalf(ctx, "failed to start grpc server: %v", err)
		}
	}()

	select {
	case <-ctx.Done():
		logger.Infof(ctx, "shutdown grpc server")
		return nil
	}
}
