/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package server

import (
	"context"
	"net"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"sigs.k8s.io/controller-runtime/pkg/manager"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/nodeagent/ipam"
	mockipam "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/nodeagent/ipam/mock"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/rpc"
	netwrapper "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/net"
	oswrapper "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/os"
)

func Test_rpcServer_AllocateIP(t *testing.T) {
	type fields struct {
		ctrl       *gomock.Controller
		mgr        manager.Manager
		osWrapper  oswrapper.Interface
		netWrapper netwrapper.Interface
		ipamCtrl   ipam.Interface
	}
	type args struct {
		ctx context.Context
		req *rpc.Request
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *rpc.Response
		wantErr bool
	}{
		{
			name: "empty pod name",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				ipam := mockipam.NewMockInterface(ctrl)

				return fields{
					ipamCtrl: ipam,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				req: &rpc.Request{
					K8SPodName:             "",
					K8SPodNamespace:        "",
					K8SPodInfraContainerID: "",
					Netns:                  "",
					IfName:                 "",
				},
			},
			want: &rpc.Response{
				IsSuccess:   false,
				ErrMsg:      "podName is empty",
				IPType:      0,
				NetworkInfo: nil,
			},
			wantErr: false,
		},
		{
			name: "success case",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				ipam := mockipam.NewMockInterface(ctrl)

				gomock.InOrder(
					ipam.EXPECT().AllocateIP(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&rpc.ENIMultiIPReply{}, nil),
				)

				return fields{
					ipamCtrl: ipam,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				req: &rpc.Request{
					K8SPodName:             "busybox",
					K8SPodNamespace:        "default",
					K8SPodInfraContainerID: "",
					Netns:                  "",
					IfName:                 "",
				},
			},
			want: &rpc.Response{
				IsSuccess: true,
				ErrMsg:    "",
				IPType:    0,
				NetworkInfo: &rpc.Response_ENIMultiIP{
					ENIMultiIP: &rpc.ENIMultiIPReply{},
				},
			},
			wantErr: false,
		},
		{
			name: "failed case",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				ipam := mockipam.NewMockInterface(ctrl)

				gomock.InOrder(
					ipam.EXPECT().AllocateIP(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, net.ErrClosed),
				)

				return fields{
					ipamCtrl: ipam,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				req: &rpc.Request{
					K8SPodName:             "busybox",
					K8SPodNamespace:        "default",
					K8SPodInfraContainerID: "",
					Netns:                  "",
					IfName:                 "",
				},
			},
			want: &rpc.Response{
				IsSuccess:   false,
				ErrMsg:      net.ErrClosed.Error(),
				IPType:      0,
				NetworkInfo: nil,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}

			s := &rpcServer{
				mgr:        tt.fields.mgr,
				osWrapper:  tt.fields.osWrapper,
				netWrapper: tt.fields.netWrapper,
				ipamCtrl:   tt.fields.ipamCtrl,
			}

			got, err := s.AllocateIP(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("rpcServer.AllocateIP() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("rpcServer.AllocateIP() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_rpcServer_ReleaseIP(t *testing.T) {
	type fields struct {
		ctrl       *gomock.Controller
		mgr        manager.Manager
		osWrapper  oswrapper.Interface
		netWrapper netwrapper.Interface
		ipamCtrl   ipam.Interface
	}
	type args struct {
		ctx context.Context
		req *rpc.Request
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *rpc.Response
		wantErr bool
	}{
		{
			name: "success case",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				ipam := mockipam.NewMockInterface(ctrl)

				gomock.InOrder(
					ipam.EXPECT().ReleaseIP(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
				)

				return fields{
					ipamCtrl: ipam,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				req: &rpc.Request{
					K8SPodName:             "busybox",
					K8SPodNamespace:        "default",
					K8SPodInfraContainerID: "",
					Netns:                  "",
					IfName:                 "",
				},
			},
			want: &rpc.Response{
				IsSuccess: true,
				ErrMsg:    "",
				IPType:    0,
				NetworkInfo: &rpc.Response_ENIMultiIP{
					ENIMultiIP: &rpc.ENIMultiIPReply{
						EniRequireUniqueRouteTable: ipam.RequireUniqueRouteTable,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "failed case",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				ipam := mockipam.NewMockInterface(ctrl)

				gomock.InOrder(
					ipam.EXPECT().ReleaseIP(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(net.ErrClosed),
				)

				return fields{
					ipamCtrl: ipam,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				req: &rpc.Request{
					K8SPodName:             "busybox",
					K8SPodNamespace:        "default",
					K8SPodInfraContainerID: "",
					Netns:                  "",
					IfName:                 "",
				},
			},
			want: &rpc.Response{
				IsSuccess:   false,
				ErrMsg:      net.ErrClosed.Error(),
				IPType:      0,
				NetworkInfo: nil,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		if tt.fields.ctrl != nil {
			defer tt.fields.ctrl.Finish()
		}

		t.Run(tt.name, func(t *testing.T) {
			s := &rpcServer{
				mgr:        tt.fields.mgr,
				osWrapper:  tt.fields.osWrapper,
				netWrapper: tt.fields.netWrapper,
				ipamCtrl:   tt.fields.ipamCtrl,
			}
			got, err := s.ReleaseIP(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("rpcServer.ReleaseIP() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("rpcServer.ReleaseIP() = %v, want %v", got, tt.want)
			}
		})
	}
}
