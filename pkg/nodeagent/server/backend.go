/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package server

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/logger"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/nodeagent/ipam"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/rpc"
)

func (s *rpcServer) AllocateIP(ctx context.Context, req *rpc.Request) (*rpc.Response, error) {
	ctx = logger.EnsureTraceIDInCtx(ctx)

	logger.Infof(ctx, "---> allocate ip request: %s/%s", req.GetK8SPodNamespace(), req.GetK8SPodName())
	defer logger.Infof(ctx, "<--- alloce ip end\n")
	podName := req.GetK8SPodName()
	podNameSpace := req.GetK8SPodNamespace()
	podId := req.GetK8SPodInfraContainerID()

	if podName == "" {
		return &rpc.Response{
			ErrMsg: fmt.Sprintf("podName is empty"),
		}, nil
	}
	eniMultiIPReply, err := s.ipamCtrl.AllocateIP(ctx, podName, podNameSpace, podId)
	if err != nil {
		logger.Errorf(ctx, err.Error())
		return &rpc.Response{
			IsSuccess: false,
			ErrMsg:    err.Error(),
		}, nil
	}

	return &rpc.Response{
		IsSuccess: true,
		IPType:    0,
		NetworkInfo: &rpc.Response_ENIMultiIP{
			ENIMultiIP: eniMultiIPReply,
		},
	}, nil
}

func (s *rpcServer) ReleaseIP(ctx context.Context, req *rpc.Request) (*rpc.Response, error) {
	ctx = logger.EnsureTraceIDInCtx(ctx)

	logger.Infof(ctx, "---> release ip request, %s/%s", req.GetK8SPodNamespace(), req.GetK8SPodName())
	defer logger.Infof(ctx, "<--- release ip end\n")

	podName := req.GetK8SPodName()
	podNameSpace := req.GetK8SPodNamespace()
	podId := req.GetK8SPodInfraContainerID()

	if podName == "" {
		return &rpc.Response{
			ErrMsg: fmt.Sprintf("podName is empty"),
		}, nil
	}

	err := s.ipamCtrl.ReleaseIP(ctx, podName, podNameSpace, podId)
	if err != nil {
		logger.Errorf(ctx, err.Error())
		return &rpc.Response{
			IsSuccess: false,
			ErrMsg:    err.Error(),
		}, nil
	}

	return &rpc.Response{
		IsSuccess: true,
		NetworkInfo: &rpc.Response_ENIMultiIP{
			ENIMultiIP: &rpc.ENIMultiIPReply{
				EniRequireUniqueRouteTable: ipam.RequireUniqueRouteTable,
			},
		},
	}, nil
}

func (s *rpcServer) CheckIP(ctx context.Context, req *rpc.Request) (*rpc.Response, error) {
	return &rpc.Response{}, nil
}
