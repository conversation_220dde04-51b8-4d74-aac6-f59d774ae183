/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package enilink

import (
	"fmt"
	"regexp"
	"strconv"
	"time"

	"github.com/vishvananda/netlink"

	netlinkwrapper "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/netlink"
)

const (
	EniRouteTableBase = 127
)

var (
	eniLinkNameMatcher = regexp.MustCompile(`eth(\d+)`)
)

func GetEniLinkNameSuffix(intf netlink.Link) (int, error) {
	matches := eniLinkNameMatcher.FindStringSubmatch(intf.Attrs().Name)
	if len(matches) != 2 {
		return -1, fmt.Errorf("invalid interface name: %v", intf.Attrs().Name)
	}

	index, err := strconv.ParseInt(matches[1], 10, 32)
	if err != nil {
		return -1, fmt.Errorf("error parsing interface link index: %v", err)
	}

	return int(index), nil
}

func FindEniLinkByHWAddress(nlink netlinkwrapper.Interface, hardwareAddr string) (netlink.Link, error) {
	var (
		eniLink netlink.Link

		i = 0
	)

	// list all interfaces, and find Eni by hardware address
	for {
		if i >= 3 {
			return nil, fmt.Errorf("eni with hardware address %v not found", hardwareAddr)
		}

		interfaces, err := nlink.LinkList()
		if err != nil {
			return nil, err
		}

		for _, intf := range interfaces {
			if intf.Attrs().HardwareAddr.String() == hardwareAddr {
				eniLink = intf
				break
			}
		}

		if eniLink == nil {
			i++
			time.Sleep(time.Second)
			continue
		}

		return eniLink, nil
	}
}
