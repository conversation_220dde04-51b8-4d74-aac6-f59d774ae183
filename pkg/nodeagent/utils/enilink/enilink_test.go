/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package enilink

import (
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/vishvananda/netlink"
	netlinkwrapper "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/netlink"
	mocknetlink "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/netlink/mock"
)

func setupEnv(t *testing.T) (
	*gomock.Controller,
	*mocknetlink.MockInterface,
) {
	ctrl := gomock.NewController(t)
	nlink := mocknetlink.NewMockInterface(ctrl)
	return ctrl, nlink
}

func TestGetEniInterfaceNameSuffix(t *testing.T) {
	type args struct {
		intf netlink.Link
	}
	tests := []struct {
		name    string
		args    args
		want    int
		wantErr bool
	}{
		{
			name: "正常流程",
			args: args{
				intf: &netlink.Dummy{
					LinkAttrs: netlink.LinkAttrs{
						Index:        0,
						Name:         "eth1",
						HardwareAddr: []byte{},
					},
				},
			},
			want:    1,
			wantErr: false,
		},
		{
			name: "正常流程",
			args: args{
				intf: &netlink.Dummy{
					LinkAttrs: netlink.LinkAttrs{
						Index:        0,
						Name:         "bond0",
						HardwareAddr: []byte{},
					},
				},
			},
			want:    -1,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetEniLinkNameSuffix(tt.args.intf)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetEniInterfaceNameSuffix() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetEniInterfaceNameSuffix() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestFindEniLinkByHWAddress(t *testing.T) {
	type args struct {
		ctrl         *gomock.Controller
		nlink        netlinkwrapper.Interface
		hardwareAddr string
	}
	tests := []struct {
		name    string
		args    args
		want    netlink.Link
		wantErr bool
	}{
		{
			name: "失败",
			args: func() args {
				ctrl, nlink := setupEnv(t)

				gomock.InOrder(
					nlink.EXPECT().LinkList().Return(nil, netlink.ErrNotImplemented),
				)

				return args{
					ctrl:         ctrl,
					nlink:        nlink,
					hardwareAddr: "",
				}
			}(),
			want:    nil,
			wantErr: true,
		},
		{
			name: "一次成功",
			args: func() args {
				ctrl, nlink := setupEnv(t)

				gomock.InOrder(
					nlink.EXPECT().LinkList().Return([]netlink.Link{
						&netlink.Bond{},
					}, nil),
				)

				return args{
					ctrl:         ctrl,
					nlink:        nlink,
					hardwareAddr: "",
				}
			}(),
			want:    &netlink.Bond{},
			wantErr: false,
		},
		{
			name: "重试后失败",
			args: func() args {
				ctrl, nlink := setupEnv(t)

				gomock.InOrder(
					nlink.EXPECT().LinkList().Return([]netlink.Link{}, nil),
					nlink.EXPECT().LinkList().Return([]netlink.Link{}, nil),
					nlink.EXPECT().LinkList().Return([]netlink.Link{}, nil),
				)

				return args{
					ctrl:         ctrl,
					nlink:        nlink,
					hardwareAddr: "",
				}
			}(),
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		if tt.args.ctrl != nil {
			defer tt.args.ctrl.Finish()
		}

		t.Run(tt.name, func(t *testing.T) {
			got, err := FindEniLinkByHWAddress(tt.args.nlink, tt.args.hardwareAddr)
			if (err != nil) != tt.wantErr {
				t.Errorf("FindEniLinkByHWAddress() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("FindEniLinkByHWAddress() = %v, want %v", got, tt.want)
			}
		})
	}
}
