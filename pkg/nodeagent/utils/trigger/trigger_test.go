/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package trigger

import (
	"fmt"
	"log"
	"reflect"
	"sync"
	"testing"
	"time"
)

func TestTrigger_needsDelay(t *testing.T) {
	type fields struct {
		trigger       bool
		params        Parameters
		lastTrigger   time.Time
		wakeupChan    chan struct{}
		closeChan     chan struct{}
		numFolds      int
		foldedReasons reasonStack
	}
	tests := []struct {
		name   string
		fields fields
		want   bool
		want1  time.Duration
	}{
		{
			name: "minInterval == 0",
			fields: fields{
				trigger: false,
				params: Parameters{
					MinInterval:   0,
					TriggerFunc:   func([]string) { panic("not implemented") },
					ShutdownFunc:  func() { panic("not implemented") },
					Name:          "",
					sleepInterval: 0,
				},
				lastTrigger:   time.Time{},
				wakeupChan:    make(chan struct{}),
				closeChan:     make(chan struct{}),
				numFolds:      0,
				foldedReasons: map[string]struct{}{},
			},
			want: false,
		},
		{
			name: "minInterval != 0",
			fields: fields{
				trigger: false,
				params: Parameters{
					MinInterval:   time.Minute,
					TriggerFunc:   func([]string) { panic("not implemented") },
					ShutdownFunc:  func() { panic("not implemented") },
					Name:          "",
					sleepInterval: 0,
				},
				lastTrigger:   time.Now(),
				wakeupChan:    make(chan struct{}),
				closeChan:     make(chan struct{}),
				numFolds:      0,
				foldedReasons: map[string]struct{}{},
			},
			want: true,
		},
		{
			name: "minInterval != 0",
			fields: fields{
				trigger: false,
				params: Parameters{
					MinInterval:   time.Minute,
					TriggerFunc:   func([]string) { panic("not implemented") },
					ShutdownFunc:  func() { panic("not implemented") },
					Name:          "",
					sleepInterval: 0,
				},
				lastTrigger:   time.Now().Add(time.Hour * -1),
				wakeupChan:    make(chan struct{}),
				closeChan:     make(chan struct{}),
				numFolds:      0,
				foldedReasons: map[string]struct{}{},
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tr := &Trigger{
				trigger:       tt.fields.trigger,
				params:        tt.fields.params,
				lastTrigger:   tt.fields.lastTrigger,
				wakeupChan:    tt.fields.wakeupChan,
				closeChan:     tt.fields.closeChan,
				numFolds:      tt.fields.numFolds,
				foldedReasons: tt.fields.foldedReasons,
			}
			got, _ := tr.needsDelay()
			if got != tt.want {
				t.Errorf("Trigger.needsDelay() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestLongTrigger(tt *testing.T) {
	var (
		mutex     sync.Mutex
		triggered int
	)

	t, _ := NewTrigger(Parameters{
		TriggerFunc: func(reasons []string) {
			mutex.Lock()
			triggered++
			mutex.Unlock()
			time.Sleep(time.Second)
		},
		sleepInterval: time.Millisecond,
	})

	// Trigger multiple times and sleep in between to guarantee that the
	// background routine probed in the meantime
	for i := 0; i < 5; i++ {
		t.Trigger()
		time.Sleep(time.Millisecond * 20)
	}

	mutex.Lock()
	triggeredCopy := triggered
	mutex.Unlock()

	if triggeredCopy != 1 {
		tt.Error("LongTrigger")
	}

	t.Shutdown()
}

func TestMinInterval(tt *testing.T) {
	var (
		mutex     sync.Mutex
		triggered int
	)

	t, _ := NewTrigger(Parameters{
		TriggerFunc: func(reasons []string) {
			mutex.Lock()
			triggered++
			mutex.Unlock()
		},
		MinInterval:   time.Millisecond * 500,
		sleepInterval: time.Millisecond,
		ShutdownFunc:  func() {},
	})

	// Trigger multiple times and sleep in between to guarantee that the
	// background routine probed in the meantime
	for i := 0; i < 5; i++ {
		t.Trigger()
		time.Sleep(time.Millisecond * 20)
	}

	mutex.Lock()
	triggeredCopy := triggered
	mutex.Unlock()
	if triggeredCopy != 1 {
		tt.Error("TestMinInterval")
	}

	t.Shutdown()
}

func TestLongLongTrigger(*testing.T) {
	f := func(reasons []string) {
		log.Println("triggered:", reasons, "len:", len(reasons))
	}

	t, err := NewTrigger(Parameters{
		Name:        "bci-test",
		MinInterval: time.Second * 5,
		TriggerFunc: f,
	})
	if err != nil {
		log.Fatal("NewTrigger", err)
	}

	for i := 0; i < 3; i++ {
		t.TriggerWithReason(fmt.Sprintf("%d", i))
		time.Sleep(time.Second * 1)
	}

	time.Sleep(time.Second * 10)

	t.Shutdown()
}

func TestNewTrigger(t *testing.T) {
	type args struct {
		p Parameters
	}
	tests := []struct {
		name    string
		args    args
		want    *Trigger
		wantErr bool
	}{
		{
			args: args{
				p: Parameters{
					MinInterval:   0,
					TriggerFunc:   nil,
					ShutdownFunc:  func() { panic("not implemented") },
					Name:          "",
					sleepInterval: 0,
				},
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := NewTrigger(tt.args.p)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewTrigger() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewTrigger() = %v, want %v", got, tt.want)
			}
		})
	}
}
