/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package trigger

import (
	"fmt"
	"sync"
	"time"
)

type Parameters struct {
	// MinInterval is the minimum required interval between invocations of
	// TriggerFunc
	MinInterval time.Duration

	// TriggerFunc is the function to be called when Trigger() is called
	// while respecting MinInterval and serialization
	TriggerFunc func(reasons []string)

	// ShutdownFunc is called when the trigger is shut down
	ShutdownFunc func()

	// Name is the unique name of the trigger. It must be provided in a
	// format compatible to be used as prometheus name string.
	Name string

	// sleepInterval controls the waiter sleep duration. This parameter is
	// only exposed to tests
	sleepInterval time.Duration
}

type Trigger struct {
	mutex sync.Mutex

	trigger bool

	// params are the user specified parameters
	params Parameters

	// lastTrigger is the timestamp of the last invoked trigger
	lastTrigger time.Time

	// wakeupCan is used to wake up the background trigger routine
	wakeupChan chan struct{}

	// closeChan is used to stop the background trigger routine
	closeChan chan struct{}

	// numFolds is the current count of folds that happened into the
	// currently scheduled trigger
	numFolds int

	// foldedReasons is the sum of all unique reasons folded together.
	foldedReasons reasonStack
}

type reasonStack map[string]struct{}

func newReasonStack() reasonStack {
	return map[string]struct{}{}
}

func (r reasonStack) add(reason string) {
	r[reason] = struct{}{}
}

func (r reasonStack) slice() []string {
	var (
		result = make([]string, len(r))
		i      = 0
	)

	for reason := range r {
		result[i] = reason
		i++
	}

	return result
}

// NewTrigger returns a new trigger based on the provided parameters
func NewTrigger(p Parameters) (*Trigger, error) {
	if p.sleepInterval == 0 {
		p.sleepInterval = time.Second
	}

	if p.TriggerFunc == nil {
		return nil, fmt.Errorf("trigger function is nil")
	}

	t := &Trigger{
		params:        p,
		wakeupChan:    make(chan struct{}, 1),
		closeChan:     make(chan struct{}, 1),
		foldedReasons: newReasonStack(),
	}

	// Guarantee that initial trigger has no delay
	if p.MinInterval > time.Duration(0) {
		t.lastTrigger = time.Now().Add(-1 * p.MinInterval)
	}

	go t.wait()

	return t, nil
}

// Trigger triggers the call to TriggerFunc as specified in the parameters
// provided to NewTrigger(). It respects MinInterval and ensures that calls to
// TriggerFunc are serialized. This function is non-blocking and will return
// immediately before TriggerFunc is potentially triggered and has completed.
func (t *Trigger) TriggerWithReason(reason string) {
	t.mutex.Lock()
	t.trigger = true
	t.numFolds++
	t.foldedReasons.add(reason)
	t.mutex.Unlock()

	select {
	case t.wakeupChan <- struct{}{}:
	default:
	}
}

// Trigger triggers the call to TriggerFunc as specified in the parameters
// provided to NewTrigger(). It respects MinInterval and ensures that calls to
// TriggerFunc are serialized. This function is non-blocking and will return
// immediately before TriggerFunc is potentially triggered and has completed.
func (t *Trigger) Trigger() {
	t.TriggerWithReason("")
}

// Shutdown stops the trigger mechanism
func (t *Trigger) Shutdown() {
	close(t.closeChan)
}

// needsDelay returns whether and how long of a delay is required to fullfil
// MinInterval
func (t *Trigger) needsDelay() (bool, time.Duration) {
	if t.params.MinInterval == time.Duration(0) {
		return false, 0
	}

	sleepTime := time.Since(t.lastTrigger.Add(t.params.MinInterval))
	return sleepTime < 0, sleepTime * -1
}

func (t *Trigger) wait() {
	var (
		tick = time.NewTicker(t.params.sleepInterval)
	)

	for {
		// reset trigger status
		t.mutex.Lock()
		triggerEnabled := t.trigger
		t.trigger = false
		t.mutex.Unlock()

		if triggerEnabled {
			if delayNeeded, delay := t.needsDelay(); delayNeeded {
				time.Sleep(delay)
			}

			t.mutex.Lock()
			t.lastTrigger = time.Now()
			t.numFolds = 0
			reasons := t.foldedReasons.slice()
			t.foldedReasons = newReasonStack()
			t.mutex.Unlock()

			// real shoot
			t.params.TriggerFunc(reasons)

		}

		select {
		case <-t.wakeupChan:
		case <-tick.C:
		case <-t.closeChan:
			tick.Stop()
			shutdownFunc := t.params.ShutdownFunc
			if shutdownFunc != nil {
				shutdownFunc()
			}
			return
		}
	}
}
