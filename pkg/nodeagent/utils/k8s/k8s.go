package k8s

/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

import v1 "k8s.io/api/core/v1"

func IsPodFinished(pod *v1.Pod) bool {
	// Ref: https://github.com/projectcalico/libcalico-go/blob/9bbd69b5de2b6df62f4508d7557ddb67b1be1dc2/lib/backend/k8s/conversion/conversion.go#L157-L171
	switch pod.Status.Phase {
	case v1.PodFailed, v1.PodSucceeded, v1.PodPhase("Completed"):
		return true
	}
	return false
}
