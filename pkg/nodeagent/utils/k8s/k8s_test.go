package k8s

import (
	"testing"

	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

func TestIsPodFinished(t *testing.T) {
	type args struct {
		pod *v1.Pod
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "pod failed",
			args: args{
				pod: &v1.Pod{
					TypeMeta: metav1.TypeMeta{
						Kind:       "",
						APIVersion: "",
					},
					ObjectMeta: metav1.ObjectMeta{
						Name:                       "",
						GenerateName:               "",
						Namespace:                  "",
						SelfLink:                   "",
						UID:                        "",
						ResourceVersion:            "",
						Generation:                 0,
						CreationTimestamp:          metav1.Time{},
						DeletionTimestamp:          &metav1.Time{},
						DeletionGracePeriodSeconds: nil,
						Labels:                     map[string]string{},
						Annotations:                map[string]string{},
					},
					Spec: v1.PodSpec{
						Volumes:                       []v1.Volume{},
						InitContainers:                []v1.Container{},
						Containers:                    []v1.Container{},
						EphemeralContainers:           []v1.EphemeralContainer{},
						RestartPolicy:                 "",
						TerminationGracePeriodSeconds: nil,
						ActiveDeadlineSeconds:         nil,
						DNSPolicy:                     "",
						NodeSelector: map[string]string{
							"": "",
						},
						ServiceAccountName:           "",
						DeprecatedServiceAccount:     "",
						AutomountServiceAccountToken: nil,
						NodeName:                     "",
						HostNetwork:                  false,
						HostPID:                      false,
						HostIPC:                      false,
						ShareProcessNamespace:        nil,
						SecurityContext: &v1.PodSecurityContext{
							SELinuxOptions: &v1.SELinuxOptions{
								User:  "",
								Role:  "",
								Type:  "",
								Level: "",
							},
							WindowsOptions: &v1.WindowsSecurityContextOptions{
								GMSACredentialSpecName: nil,
								GMSACredentialSpec:     nil,
								RunAsUserName:          nil,
								HostProcess:            nil,
							},
							RunAsUser:           nil,
							RunAsGroup:          nil,
							RunAsNonRoot:        nil,
							SupplementalGroups:  []int64{},
							FSGroup:             nil,
							Sysctls:             []v1.Sysctl{},
							FSGroupChangePolicy: nil,
							SeccompProfile: &v1.SeccompProfile{
								Type:             "",
								LocalhostProfile: nil,
							},
						},
						ImagePullSecrets: []v1.LocalObjectReference{},
						Hostname:         "",
						Subdomain:        "",
						Affinity: &v1.Affinity{
							NodeAffinity: &v1.NodeAffinity{
								RequiredDuringSchedulingIgnoredDuringExecution: &v1.NodeSelector{
									NodeSelectorTerms: []v1.NodeSelectorTerm{},
								},
								PreferredDuringSchedulingIgnoredDuringExecution: []v1.PreferredSchedulingTerm{},
							},
							PodAffinity: &v1.PodAffinity{
								RequiredDuringSchedulingIgnoredDuringExecution:  []v1.PodAffinityTerm{},
								PreferredDuringSchedulingIgnoredDuringExecution: []v1.WeightedPodAffinityTerm{},
							},
							PodAntiAffinity: &v1.PodAntiAffinity{
								RequiredDuringSchedulingIgnoredDuringExecution:  []v1.PodAffinityTerm{},
								PreferredDuringSchedulingIgnoredDuringExecution: []v1.WeightedPodAffinityTerm{},
							},
						},
						SchedulerName:     "",
						Tolerations:       []v1.Toleration{},
						HostAliases:       []v1.HostAlias{},
						PriorityClassName: "",
						Priority:          nil,
						DNSConfig: &v1.PodDNSConfig{
							Nameservers: []string{},
							Searches:    []string{},
							Options:     []v1.PodDNSConfigOption{},
						},
						ReadinessGates:     []v1.PodReadinessGate{},
						RuntimeClassName:   nil,
						EnableServiceLinks: nil,
						PreemptionPolicy:   nil,
						Overhead: map[v1.ResourceName]resource.Quantity{
							"": {
								Format: "",
							},
						},
						TopologySpreadConstraints: []v1.TopologySpreadConstraint{},
						SetHostnameAsFQDN:         nil,
					},
					Status: v1.PodStatus{
						Phase:                      v1.PodFailed,
						Conditions:                 []v1.PodCondition{},
						Message:                    "",
						Reason:                     "",
						NominatedNodeName:          "",
						HostIP:                     "",
						PodIP:                      "",
						PodIPs:                     []v1.PodIP{},
						InitContainerStatuses:      []v1.ContainerStatus{},
						ContainerStatuses:          []v1.ContainerStatus{},
						QOSClass:                   "",
						EphemeralContainerStatuses: []v1.ContainerStatus{},
					},
				},
			},
			want: true,
		},
		{
			name: "pod running",
			args: args{
				pod: &v1.Pod{
					TypeMeta: metav1.TypeMeta{
						Kind:       "",
						APIVersion: "",
					},
					ObjectMeta: metav1.ObjectMeta{
						Name:                       "",
						GenerateName:               "",
						Namespace:                  "",
						SelfLink:                   "",
						UID:                        "",
						ResourceVersion:            "",
						Generation:                 0,
						CreationTimestamp:          metav1.Time{},
						DeletionTimestamp:          &metav1.Time{},
						DeletionGracePeriodSeconds: nil,
						Labels:                     map[string]string{},
						Annotations:                map[string]string{},
					},
					Spec: v1.PodSpec{
						Volumes:                       []v1.Volume{},
						InitContainers:                []v1.Container{},
						Containers:                    []v1.Container{},
						EphemeralContainers:           []v1.EphemeralContainer{},
						RestartPolicy:                 "",
						TerminationGracePeriodSeconds: nil,
						ActiveDeadlineSeconds:         nil,
						DNSPolicy:                     "",
						NodeSelector: map[string]string{
							"": "",
						},
						ServiceAccountName:           "",
						DeprecatedServiceAccount:     "",
						AutomountServiceAccountToken: nil,
						NodeName:                     "",
						HostNetwork:                  false,
						HostPID:                      false,
						HostIPC:                      false,
						ShareProcessNamespace:        nil,
						SecurityContext: &v1.PodSecurityContext{
							SELinuxOptions: &v1.SELinuxOptions{
								User:  "",
								Role:  "",
								Type:  "",
								Level: "",
							},
							WindowsOptions: &v1.WindowsSecurityContextOptions{
								GMSACredentialSpecName: nil,
								GMSACredentialSpec:     nil,
								RunAsUserName:          nil,
								HostProcess:            nil,
							},
							RunAsUser:           nil,
							RunAsGroup:          nil,
							RunAsNonRoot:        nil,
							SupplementalGroups:  []int64{},
							FSGroup:             nil,
							Sysctls:             []v1.Sysctl{},
							FSGroupChangePolicy: nil,
							SeccompProfile: &v1.SeccompProfile{
								Type:             "",
								LocalhostProfile: nil,
							},
						},
						ImagePullSecrets: []v1.LocalObjectReference{},
						Hostname:         "",
						Subdomain:        "",
						Affinity: &v1.Affinity{
							NodeAffinity: &v1.NodeAffinity{
								RequiredDuringSchedulingIgnoredDuringExecution: &v1.NodeSelector{
									NodeSelectorTerms: []v1.NodeSelectorTerm{},
								},
								PreferredDuringSchedulingIgnoredDuringExecution: []v1.PreferredSchedulingTerm{},
							},
							PodAffinity: &v1.PodAffinity{
								RequiredDuringSchedulingIgnoredDuringExecution:  []v1.PodAffinityTerm{},
								PreferredDuringSchedulingIgnoredDuringExecution: []v1.WeightedPodAffinityTerm{},
							},
							PodAntiAffinity: &v1.PodAntiAffinity{
								RequiredDuringSchedulingIgnoredDuringExecution:  []v1.PodAffinityTerm{},
								PreferredDuringSchedulingIgnoredDuringExecution: []v1.WeightedPodAffinityTerm{},
							},
						},
						SchedulerName:     "",
						Tolerations:       []v1.Toleration{},
						HostAliases:       []v1.HostAlias{},
						PriorityClassName: "",
						Priority:          nil,
						DNSConfig: &v1.PodDNSConfig{
							Nameservers: []string{},
							Searches:    []string{},
							Options:     []v1.PodDNSConfigOption{},
						},
						ReadinessGates:     []v1.PodReadinessGate{},
						RuntimeClassName:   nil,
						EnableServiceLinks: nil,
						PreemptionPolicy:   nil,
						Overhead: map[v1.ResourceName]resource.Quantity{
							"": {
								Format: "",
							},
						},
						TopologySpreadConstraints: []v1.TopologySpreadConstraint{},
						SetHostnameAsFQDN:         nil,
					},
					Status: v1.PodStatus{
						Phase:                      v1.PodRunning,
						Conditions:                 []v1.PodCondition{},
						Message:                    "",
						Reason:                     "",
						NominatedNodeName:          "",
						HostIP:                     "",
						PodIP:                      "",
						PodIPs:                     []v1.PodIP{},
						InitContainerStatuses:      []v1.ContainerStatus{},
						ContainerStatuses:          []v1.ContainerStatus{},
						QOSClass:                   "",
						EphemeralContainerStatuses: []v1.ContainerStatus{},
					},
				},
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := IsPodFinished(tt.args.pod); got != tt.want {
				t.Errorf("IsPodFinished() = %v, want %v", got, tt.want)
			}
		})
	}
}
