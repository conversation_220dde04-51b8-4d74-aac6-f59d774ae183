/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package ipam

import (
	"context"
	"net"
	"reflect"
	"sync"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/controller-runtime/pkg/cache"
	"sigs.k8s.io/controller-runtime/pkg/client"

	networkingv1 "icode.baidu.com/baidu/bci2/bci-cni-driver/apis/networking/v1"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/logger"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/nodeagent/ipam/mock"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/nodeagent/utils/trigger"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/queue"
	netlinkwrapper "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/netlink"
)

func Test_regularizeSecurityGroups(t *testing.T) {
	type args struct {
		securityGroupList string
	}
	tests := []struct {
		name string
		args args
		want []string
	}{
		{
			name: "case 1",
			args: args{
				securityGroupList: "g-aaa,g-bbb",
			},
			want: []string{"g-aaa", "g-bbb"},
		},
		{
			name: "case 2",
			args: args{
				securityGroupList: "g-aaa, g-bbb",
			},
			want: []string{"g-aaa", "g-bbb"},
		},
		{
			name: "case 3",
			args: args{
				securityGroupList: "g-ccc,g-aaa, g-bbb",
			},
			want: []string{"g-aaa", "g-bbb", "g-ccc"},
		},
		{
			name: "case 3",
			args: args{
				securityGroupList: "g-ccc,g-aaa, g-bbb,g-aaa",
			},
			want: []string{"g-aaa", "g-bbb", "g-ccc"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := regularizeSecurityGroups(tt.args.securityGroupList); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("regularizeSecurityGroups() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_regularizeSecurityGroupsToStringKey(t *testing.T) {
	type args struct {
		securityGroupList string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "case 1",
			args: args{
				securityGroupList: "g-ccc,g-aaa, g-bbb",
			},
			want: "g-aaa,g-bbb,g-ccc",
		},
		{
			name: "case 2",
			args: args{
				securityGroupList: "g-aaa,g-bbb",
			},
			want: "g-aaa,g-bbb",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := regularizeSecurityGroupsToStringKey(tt.args.securityGroupList); got != tt.want {
				t.Errorf("regularizeSecurityGroupsToStringKey() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_securityGroupListToStringKey(t *testing.T) {
	type args struct {
		securityGroups []string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "case 1",
			args: args{
				securityGroups: []string{"g-aaa", "g-bbb"},
			},
			want: "g-aaa,g-bbb",
		},
		{
			name: "case 2",
			args: args{
				securityGroups: []string{"g-bbb", "g-aaa"},
			},
			want: "g-aaa,g-bbb",
		},
		{
			name: "case 3",
			args: args{
				securityGroups: []string{" g-bbb   ", "g-aaa"},
			},
			want: "g-aaa,g-bbb",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := securityGroupListToStringKey(tt.args.securityGroups); got != tt.want {
				t.Errorf("securityGroupListToStringKey() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestIPAM_getEniIDByInfraContainerID(t *testing.T) {
	type fields struct {
		lock          sync.RWMutex
		BciNodeStatus networkingv1.BciNodeStatus
		BciNodeSpec   networkingv1.BciNodeSpec
		BciNode       *networkingv1.BciNode
		UserIdlePool  map[string]map[string]*queue.Queue
		Lookup        map[string]bool
		MarkIPUsed    map[string]string
		Cache         cache.Cache
		Client        client.Client
	}
	type args struct {
		podNameSpaceName string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   string
	}{
		{
			name: "case 1",
			fields: fields{
				lock: sync.RWMutex{},
				BciNode: &networkingv1.BciNode{
					Status: networkingv1.BciNodeStatus{
						EniMultiIP: networkingv1.EniMultiIPStatus{
							Used: map[string]networkingv1.UserAllocationEnis{
								"********************************": map[string]*networkingv1.AllocationEni{
									"eni-0vkhfb98xm78": {
										UserID:           "********************************",
										EniID:            "eni-0vkhfb98xm78",
										MacAddress:       "",
										SubnetID:         "",
										SecurityGroupIDs: []string{},
										VpcID:            "",
										VpcCIDR:          "",
										PrimaryIPAddress: "",
										PrivateIPAddresses: map[string]networkingv1.AllocationIP{
											"*************": {
												UserID:      "********************************",
												EniID:       "eni-0vkhfb98xm78",
												EIP:         nil,
												Owner:       "********************************/xxx",
												ContainerID: "2704632908dbacba91297d1e19c092f4e9586f616e77f4611788c5ce50570dc6",
											},
										},
									},
								},
							},
							NotReady: []string{},
						},
					},
				},
				UserIdlePool: map[string]map[string]*queue.Queue{},
				Lookup:       map[string]bool{},
				MarkIPUsed:   map[string]string{},
				Cache:        nil,
				Client:       nil,
			},
			args: args{
				podNameSpaceName: "********************************/xxx",
			},
			want: "eni-0vkhfb98xm78",
		},
		{
			name: "case 2",
			fields: fields{
				lock: sync.RWMutex{},
				BciNode: &networkingv1.BciNode{
					Status: networkingv1.BciNodeStatus{
						EniMultiIP: networkingv1.EniMultiIPStatus{
							Used: map[string]networkingv1.UserAllocationEnis{
								"********************************": map[string]*networkingv1.AllocationEni{
									"eni-0vkhfb98xm78": {
										UserID:           "********************************",
										EniID:            "eni-0vkhfb98xm78",
										MacAddress:       "",
										SubnetID:         "",
										SecurityGroupIDs: []string{},
										VpcID:            "",
										VpcCIDR:          "",
										PrimaryIPAddress: "",
										PrivateIPAddresses: map[string]networkingv1.AllocationIP{
											"*************": {
												UserID:      "********************************",
												EniID:       "eni-0vkhfb98xm78",
												EIP:         nil,
												Owner:       "",
												ContainerID: "2704632908dbacba91297d1e19c092f4e9586f616e77f4611788c5ce50570dc6",
											},
										},
									},
								},
							},
							NotReady: []string{},
						},
					},
				},
				UserIdlePool: map[string]map[string]*queue.Queue{},
				Lookup:       map[string]bool{},
				MarkIPUsed:   map[string]string{},
				Cache:        nil,
				Client:       nil,
			},
			args: args{
				podNameSpaceName: "xxx",
			},
			want: "",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ipam := &IPAM{
				lock:         tt.fields.lock,
				BciNode:      tt.fields.BciNode,
				UserIdlePool: tt.fields.UserIdlePool,
				Lookup:       tt.fields.Lookup,
				Cache:        tt.fields.Cache,
				Client:       tt.fields.Client,
			}
			if got := ipam.getEniIDByPodNameSpaceName(tt.args.podNameSpaceName); got != tt.want {
				t.Errorf("IPAM.getEniIDByInfraContainerID() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestIPAM_getPodSubnetIDAndSecID(t *testing.T) {
	type fields struct {
		ctrl          *gomock.Controller
		lock          sync.RWMutex
		BciNodeStatus networkingv1.BciNodeStatus
		BciNodeSpec   networkingv1.BciNodeSpec
		BciNode       networkingv1.BciNode
		UserIdlePool  map[string]map[string]*queue.Queue
		Lookup        map[string]bool
		MarkIPUsed    map[string]string
		Cache         cache.Cache
		Client        client.Client
	}
	type args struct {
		ctx          context.Context
		userID       string
		podName      string
		podNamespace string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    string
		want1   string
		wantErr bool
	}{
		{
			name: "case 1",
			fields: func() fields {
				ctrl, cache, client := setupEnv(t)

				gomock.InOrder(
					cache.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).SetArg(2, corev1.Pod{
						TypeMeta: v1.TypeMeta{
							Kind:       "",
							APIVersion: "",
						},
						ObjectMeta: v1.ObjectMeta{
							Name:            "",
							GenerateName:    "",
							Namespace:       "",
							SelfLink:        "",
							UID:             "",
							ResourceVersion: "",
							Generation:      0,
							CreationTimestamp: v1.Time{
								Time: time.Time{},
							},
							DeletionTimestamp: &v1.Time{
								Time: time.Time{},
							},
							DeletionGracePeriodSeconds: nil,
							Labels:                     map[string]string{},
							Annotations: map[string]string{
								"cross-vpc-eni.cce.io/subnetID":         "sbn-1",
								"cross-vpc-eni.cce.io/securityGroupIDs": "g-aaa,g-bbb",
							},
							OwnerReferences: []v1.OwnerReference{},
							Finalizers:      []string{},
							ClusterName:     "",
							ManagedFields:   []v1.ManagedFieldsEntry{},
						},
						Spec: corev1.PodSpec{
							Volumes:                       []corev1.Volume{},
							InitContainers:                []corev1.Container{},
							Containers:                    []corev1.Container{},
							EphemeralContainers:           []corev1.EphemeralContainer{},
							RestartPolicy:                 "",
							TerminationGracePeriodSeconds: nil,
							ActiveDeadlineSeconds:         nil,
							DNSPolicy:                     "",
							NodeSelector: map[string]string{
								"": "",
							},
							ServiceAccountName:           "",
							DeprecatedServiceAccount:     "",
							AutomountServiceAccountToken: nil,
							NodeName:                     "",
							HostNetwork:                  false,
							HostPID:                      false,
							HostIPC:                      false,
							ShareProcessNamespace:        nil,
							SecurityContext: &corev1.PodSecurityContext{
								SELinuxOptions: &corev1.SELinuxOptions{
									User:  "",
									Role:  "",
									Type:  "",
									Level: "",
								},
								WindowsOptions: &corev1.WindowsSecurityContextOptions{
									GMSACredentialSpecName: nil,
									GMSACredentialSpec:     nil,
									RunAsUserName:          nil,
									HostProcess:            nil,
								},
								RunAsUser:           nil,
								RunAsGroup:          nil,
								RunAsNonRoot:        nil,
								SupplementalGroups:  []int64{},
								FSGroup:             nil,
								Sysctls:             []corev1.Sysctl{},
								FSGroupChangePolicy: nil,
								SeccompProfile: &corev1.SeccompProfile{
									Type:             "",
									LocalhostProfile: nil,
								},
							},
							ImagePullSecrets: []corev1.LocalObjectReference{},
							Hostname:         "",
							Subdomain:        "",
							Affinity: &corev1.Affinity{
								NodeAffinity: &corev1.NodeAffinity{
									RequiredDuringSchedulingIgnoredDuringExecution: &corev1.NodeSelector{
										NodeSelectorTerms: []corev1.NodeSelectorTerm{},
									},
									PreferredDuringSchedulingIgnoredDuringExecution: []corev1.PreferredSchedulingTerm{},
								},
								PodAffinity: &corev1.PodAffinity{
									RequiredDuringSchedulingIgnoredDuringExecution:  []corev1.PodAffinityTerm{},
									PreferredDuringSchedulingIgnoredDuringExecution: []corev1.WeightedPodAffinityTerm{},
								},
								PodAntiAffinity: &corev1.PodAntiAffinity{
									RequiredDuringSchedulingIgnoredDuringExecution:  []corev1.PodAffinityTerm{},
									PreferredDuringSchedulingIgnoredDuringExecution: []corev1.WeightedPodAffinityTerm{},
								},
							},
							SchedulerName:     "",
							Tolerations:       []corev1.Toleration{},
							HostAliases:       []corev1.HostAlias{},
							PriorityClassName: "",
							Priority:          nil,
							DNSConfig: &corev1.PodDNSConfig{
								Nameservers: []string{},
								Searches:    []string{},
								Options:     []corev1.PodDNSConfigOption{},
							},
							ReadinessGates:     []corev1.PodReadinessGate{},
							RuntimeClassName:   nil,
							EnableServiceLinks: nil,
							PreemptionPolicy:   nil,
							Overhead: map[corev1.ResourceName]resource.Quantity{
								"": {
									Format: "",
								},
							},
							TopologySpreadConstraints: []corev1.TopologySpreadConstraint{},
							SetHostnameAsFQDN:         nil,
						},
						Status: corev1.PodStatus{
							Phase:             "",
							Conditions:        []corev1.PodCondition{},
							Message:           "",
							Reason:            "",
							NominatedNodeName: "",
							HostIP:            "",
							PodIP:             "",
							PodIPs:            []corev1.PodIP{},
							StartTime: &v1.Time{
								Time: time.Time{},
							},
							InitContainerStatuses:      []corev1.ContainerStatus{},
							ContainerStatuses:          []corev1.ContainerStatus{},
							QOSClass:                   "",
							EphemeralContainerStatuses: []corev1.ContainerStatus{},
						},
					}).Return(nil),
				)

				return fields{
					ctrl:          ctrl,
					lock:          sync.RWMutex{},
					BciNodeStatus: networkingv1.BciNodeStatus{},
					BciNodeSpec:   networkingv1.BciNodeSpec{},
					BciNode: networkingv1.BciNode{
						TypeMeta: v1.TypeMeta{
							Kind:       "",
							APIVersion: "",
						},
						ObjectMeta: v1.ObjectMeta{},
						Spec:       networkingv1.BciNodeSpec{},
						Status:     networkingv1.BciNodeStatus{},
					},
					UserIdlePool: map[string]map[string]*queue.Queue{},
					Lookup:       map[string]bool{},
					MarkIPUsed:   map[string]string{},
					Cache:        cache,
					Client:       client,
				}
			}(),
			args: args{
				ctx:          context.TODO(),
				podName:      "busybox",
				podNamespace: "default",
			},
			want:    "sbn-1",
			want1:   "g-aaa,g-bbb",
			wantErr: false,
		},
		{
			name: "case 2",
			fields: func() fields {
				ctrl, cache, client := setupEnv(t)

				gomock.InOrder(
					cache.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).SetArg(2, corev1.Pod{
						TypeMeta: v1.TypeMeta{
							Kind:       "",
							APIVersion: "",
						},
						ObjectMeta: v1.ObjectMeta{
							Name:            "",
							GenerateName:    "",
							Namespace:       "",
							SelfLink:        "",
							UID:             "",
							ResourceVersion: "",
							Generation:      0,
							CreationTimestamp: v1.Time{
								Time: time.Time{},
							},
							DeletionTimestamp: &v1.Time{
								Time: time.Time{},
							},
							DeletionGracePeriodSeconds: nil,
							Labels:                     map[string]string{},
							Annotations:                nil,
							OwnerReferences:            []v1.OwnerReference{},
							Finalizers:                 []string{},
							ClusterName:                "",
							ManagedFields:              []v1.ManagedFieldsEntry{},
						},
						Spec: corev1.PodSpec{
							Volumes:                       []corev1.Volume{},
							InitContainers:                []corev1.Container{},
							Containers:                    []corev1.Container{},
							EphemeralContainers:           []corev1.EphemeralContainer{},
							RestartPolicy:                 "",
							TerminationGracePeriodSeconds: nil,
							ActiveDeadlineSeconds:         nil,
							DNSPolicy:                     "",
							NodeSelector: map[string]string{
								"": "",
							},
							ServiceAccountName:           "",
							DeprecatedServiceAccount:     "",
							AutomountServiceAccountToken: nil,
							NodeName:                     "",
							HostNetwork:                  false,
							HostPID:                      false,
							HostIPC:                      false,
							ShareProcessNamespace:        nil,
							SecurityContext: &corev1.PodSecurityContext{
								SELinuxOptions: &corev1.SELinuxOptions{
									User:  "",
									Role:  "",
									Type:  "",
									Level: "",
								},
								WindowsOptions: &corev1.WindowsSecurityContextOptions{
									GMSACredentialSpecName: nil,
									GMSACredentialSpec:     nil,
									RunAsUserName:          nil,
									HostProcess:            nil,
								},
								RunAsUser:           nil,
								RunAsGroup:          nil,
								RunAsNonRoot:        nil,
								SupplementalGroups:  []int64{},
								FSGroup:             nil,
								Sysctls:             []corev1.Sysctl{},
								FSGroupChangePolicy: nil,
								SeccompProfile: &corev1.SeccompProfile{
									Type:             "",
									LocalhostProfile: nil,
								},
							},
							ImagePullSecrets: []corev1.LocalObjectReference{},
							Hostname:         "",
							Subdomain:        "",
							Affinity: &corev1.Affinity{
								NodeAffinity: &corev1.NodeAffinity{
									RequiredDuringSchedulingIgnoredDuringExecution: &corev1.NodeSelector{
										NodeSelectorTerms: []corev1.NodeSelectorTerm{},
									},
									PreferredDuringSchedulingIgnoredDuringExecution: []corev1.PreferredSchedulingTerm{},
								},
								PodAffinity: &corev1.PodAffinity{
									RequiredDuringSchedulingIgnoredDuringExecution:  []corev1.PodAffinityTerm{},
									PreferredDuringSchedulingIgnoredDuringExecution: []corev1.WeightedPodAffinityTerm{},
								},
								PodAntiAffinity: &corev1.PodAntiAffinity{
									RequiredDuringSchedulingIgnoredDuringExecution:  []corev1.PodAffinityTerm{},
									PreferredDuringSchedulingIgnoredDuringExecution: []corev1.WeightedPodAffinityTerm{},
								},
							},
							SchedulerName:     "",
							Tolerations:       []corev1.Toleration{},
							HostAliases:       []corev1.HostAlias{},
							PriorityClassName: "",
							Priority:          nil,
							DNSConfig: &corev1.PodDNSConfig{
								Nameservers: []string{},
								Searches:    []string{},
								Options:     []corev1.PodDNSConfigOption{},
							},
							ReadinessGates:     []corev1.PodReadinessGate{},
							RuntimeClassName:   nil,
							EnableServiceLinks: nil,
							PreemptionPolicy:   nil,
							Overhead: map[corev1.ResourceName]resource.Quantity{
								"": {
									Format: "",
								},
							},
							TopologySpreadConstraints: []corev1.TopologySpreadConstraint{},
							SetHostnameAsFQDN:         nil,
						},
						Status: corev1.PodStatus{
							Phase:             "",
							Conditions:        []corev1.PodCondition{},
							Message:           "",
							Reason:            "",
							NominatedNodeName: "",
							HostIP:            "",
							PodIP:             "",
							PodIPs:            []corev1.PodIP{},
							StartTime: &v1.Time{
								Time: time.Time{},
							},
							InitContainerStatuses:      []corev1.ContainerStatus{},
							ContainerStatuses:          []corev1.ContainerStatus{},
							QOSClass:                   "",
							EphemeralContainerStatuses: []corev1.ContainerStatus{},
						},
					}).Return(nil),
				)

				return fields{
					ctrl:          ctrl,
					lock:          sync.RWMutex{},
					BciNodeStatus: networkingv1.BciNodeStatus{},
					BciNodeSpec:   networkingv1.BciNodeSpec{},
					BciNode: networkingv1.BciNode{
						TypeMeta: v1.TypeMeta{
							Kind:       "",
							APIVersion: "",
						},
						ObjectMeta: v1.ObjectMeta{},
						Spec:       networkingv1.BciNodeSpec{},
						Status:     networkingv1.BciNodeStatus{},
					},
					UserIdlePool: map[string]map[string]*queue.Queue{},
					Lookup:       map[string]bool{},
					MarkIPUsed:   map[string]string{},
					Cache:        cache,
					Client:       client,
				}
			}(),
			args: args{
				ctx:          context.TODO(),
				podName:      "busybox",
				podNamespace: "default",
			},
			want:    "",
			want1:   "",
			wantErr: true,
		},
		{
			name: "subnet id empty",
			fields: func() fields {
				ctrl, cache, client := setupEnv(t)

				gomock.InOrder(
					cache.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).SetArg(2, corev1.Pod{
						TypeMeta: v1.TypeMeta{
							Kind:       "",
							APIVersion: "",
						},
						ObjectMeta: v1.ObjectMeta{
							Name:            "",
							GenerateName:    "",
							Namespace:       "",
							SelfLink:        "",
							UID:             "",
							ResourceVersion: "",
							Generation:      0,
							CreationTimestamp: v1.Time{
								Time: time.Time{},
							},
							DeletionTimestamp: &v1.Time{
								Time: time.Time{},
							},
							DeletionGracePeriodSeconds: nil,
							Labels:                     map[string]string{},
							Annotations: map[string]string{
								"cross-vpc-eni.cce.io/subnetID":         "",
								"cross-vpc-eni.cce.io/securityGroupIDs": "g-aaa,g-bbb",
							},
							OwnerReferences: []v1.OwnerReference{},
							Finalizers:      []string{},
							ClusterName:     "",
							ManagedFields:   []v1.ManagedFieldsEntry{},
						},
						Spec: corev1.PodSpec{
							Volumes:                       []corev1.Volume{},
							InitContainers:                []corev1.Container{},
							Containers:                    []corev1.Container{},
							EphemeralContainers:           []corev1.EphemeralContainer{},
							RestartPolicy:                 "",
							TerminationGracePeriodSeconds: nil,
							ActiveDeadlineSeconds:         nil,
							DNSPolicy:                     "",
							NodeSelector: map[string]string{
								"": "",
							},
							ServiceAccountName:           "",
							DeprecatedServiceAccount:     "",
							AutomountServiceAccountToken: nil,
							NodeName:                     "",
							HostNetwork:                  false,
							HostPID:                      false,
							HostIPC:                      false,
							ShareProcessNamespace:        nil,
							SecurityContext: &corev1.PodSecurityContext{
								SELinuxOptions: &corev1.SELinuxOptions{
									User:  "",
									Role:  "",
									Type:  "",
									Level: "",
								},
								WindowsOptions: &corev1.WindowsSecurityContextOptions{
									GMSACredentialSpecName: nil,
									GMSACredentialSpec:     nil,
									RunAsUserName:          nil,
									HostProcess:            nil,
								},
								RunAsUser:           nil,
								RunAsGroup:          nil,
								RunAsNonRoot:        nil,
								SupplementalGroups:  []int64{},
								FSGroup:             nil,
								Sysctls:             []corev1.Sysctl{},
								FSGroupChangePolicy: nil,
								SeccompProfile: &corev1.SeccompProfile{
									Type:             "",
									LocalhostProfile: nil,
								},
							},
							ImagePullSecrets: []corev1.LocalObjectReference{},
							Hostname:         "",
							Subdomain:        "",
							Affinity: &corev1.Affinity{
								NodeAffinity: &corev1.NodeAffinity{
									RequiredDuringSchedulingIgnoredDuringExecution: &corev1.NodeSelector{
										NodeSelectorTerms: []corev1.NodeSelectorTerm{},
									},
									PreferredDuringSchedulingIgnoredDuringExecution: []corev1.PreferredSchedulingTerm{},
								},
								PodAffinity: &corev1.PodAffinity{
									RequiredDuringSchedulingIgnoredDuringExecution:  []corev1.PodAffinityTerm{},
									PreferredDuringSchedulingIgnoredDuringExecution: []corev1.WeightedPodAffinityTerm{},
								},
								PodAntiAffinity: &corev1.PodAntiAffinity{
									RequiredDuringSchedulingIgnoredDuringExecution:  []corev1.PodAffinityTerm{},
									PreferredDuringSchedulingIgnoredDuringExecution: []corev1.WeightedPodAffinityTerm{},
								},
							},
							SchedulerName:     "",
							Tolerations:       []corev1.Toleration{},
							HostAliases:       []corev1.HostAlias{},
							PriorityClassName: "",
							Priority:          nil,
							DNSConfig: &corev1.PodDNSConfig{
								Nameservers: []string{},
								Searches:    []string{},
								Options:     []corev1.PodDNSConfigOption{},
							},
							ReadinessGates:     []corev1.PodReadinessGate{},
							RuntimeClassName:   nil,
							EnableServiceLinks: nil,
							PreemptionPolicy:   nil,
							Overhead: map[corev1.ResourceName]resource.Quantity{
								"": {
									Format: "",
								},
							},
							TopologySpreadConstraints: []corev1.TopologySpreadConstraint{},
							SetHostnameAsFQDN:         nil,
						},
						Status: corev1.PodStatus{
							Phase:             "",
							Conditions:        []corev1.PodCondition{},
							Message:           "",
							Reason:            "",
							NominatedNodeName: "",
							HostIP:            "",
							PodIP:             "",
							PodIPs:            []corev1.PodIP{},
							StartTime: &v1.Time{
								Time: time.Time{},
							},
							InitContainerStatuses:      []corev1.ContainerStatus{},
							ContainerStatuses:          []corev1.ContainerStatus{},
							QOSClass:                   "",
							EphemeralContainerStatuses: []corev1.ContainerStatus{},
						},
					}).Return(nil),
				)

				return fields{
					ctrl:          ctrl,
					lock:          sync.RWMutex{},
					BciNodeStatus: networkingv1.BciNodeStatus{},
					BciNodeSpec:   networkingv1.BciNodeSpec{},
					BciNode: networkingv1.BciNode{
						TypeMeta: v1.TypeMeta{
							Kind:       "",
							APIVersion: "",
						},
						ObjectMeta: v1.ObjectMeta{},
						Spec:       networkingv1.BciNodeSpec{},
						Status:     networkingv1.BciNodeStatus{},
					},
					UserIdlePool: map[string]map[string]*queue.Queue{},
					Lookup:       map[string]bool{},
					MarkIPUsed:   map[string]string{},
					Cache:        cache,
					Client:       client,
				}
			}(),
			args: args{
				ctx:          context.TODO(),
				podName:      "busybox",
				podNamespace: "default",
			},
			want:    "",
			want1:   "",
			wantErr: true,
		},
		{
			name: "security group empty",
			fields: func() fields {
				ctrl, cache, client := setupEnv(t)

				gomock.InOrder(
					cache.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).SetArg(2, corev1.Pod{
						TypeMeta: v1.TypeMeta{
							Kind:       "",
							APIVersion: "",
						},
						ObjectMeta: v1.ObjectMeta{
							Name:            "",
							GenerateName:    "",
							Namespace:       "",
							SelfLink:        "",
							UID:             "",
							ResourceVersion: "",
							Generation:      0,
							CreationTimestamp: v1.Time{
								Time: time.Time{},
							},
							DeletionTimestamp: &v1.Time{
								Time: time.Time{},
							},
							DeletionGracePeriodSeconds: nil,
							Labels:                     map[string]string{},
							Annotations: map[string]string{
								"cross-vpc-eni.cce.io/subnetID":         "sbn-1",
								"cross-vpc-eni.cce.io/securityGroupIDs": "",
							},
							OwnerReferences: []v1.OwnerReference{},
							Finalizers:      []string{},
							ClusterName:     "",
							ManagedFields:   []v1.ManagedFieldsEntry{},
						},
						Spec: corev1.PodSpec{
							Volumes:                       []corev1.Volume{},
							InitContainers:                []corev1.Container{},
							Containers:                    []corev1.Container{},
							EphemeralContainers:           []corev1.EphemeralContainer{},
							RestartPolicy:                 "",
							TerminationGracePeriodSeconds: nil,
							ActiveDeadlineSeconds:         nil,
							DNSPolicy:                     "",
							NodeSelector: map[string]string{
								"": "",
							},
							ServiceAccountName:           "",
							DeprecatedServiceAccount:     "",
							AutomountServiceAccountToken: nil,
							NodeName:                     "",
							HostNetwork:                  false,
							HostPID:                      false,
							HostIPC:                      false,
							ShareProcessNamespace:        nil,
							SecurityContext: &corev1.PodSecurityContext{
								SELinuxOptions: &corev1.SELinuxOptions{
									User:  "",
									Role:  "",
									Type:  "",
									Level: "",
								},
								WindowsOptions: &corev1.WindowsSecurityContextOptions{
									GMSACredentialSpecName: nil,
									GMSACredentialSpec:     nil,
									RunAsUserName:          nil,
									HostProcess:            nil,
								},
								RunAsUser:           nil,
								RunAsGroup:          nil,
								RunAsNonRoot:        nil,
								SupplementalGroups:  []int64{},
								FSGroup:             nil,
								Sysctls:             []corev1.Sysctl{},
								FSGroupChangePolicy: nil,
								SeccompProfile: &corev1.SeccompProfile{
									Type:             "",
									LocalhostProfile: nil,
								},
							},
							ImagePullSecrets: []corev1.LocalObjectReference{},
							Hostname:         "",
							Subdomain:        "",
							Affinity: &corev1.Affinity{
								NodeAffinity: &corev1.NodeAffinity{
									RequiredDuringSchedulingIgnoredDuringExecution: &corev1.NodeSelector{
										NodeSelectorTerms: []corev1.NodeSelectorTerm{},
									},
									PreferredDuringSchedulingIgnoredDuringExecution: []corev1.PreferredSchedulingTerm{},
								},
								PodAffinity: &corev1.PodAffinity{
									RequiredDuringSchedulingIgnoredDuringExecution:  []corev1.PodAffinityTerm{},
									PreferredDuringSchedulingIgnoredDuringExecution: []corev1.WeightedPodAffinityTerm{},
								},
								PodAntiAffinity: &corev1.PodAntiAffinity{
									RequiredDuringSchedulingIgnoredDuringExecution:  []corev1.PodAffinityTerm{},
									PreferredDuringSchedulingIgnoredDuringExecution: []corev1.WeightedPodAffinityTerm{},
								},
							},
							SchedulerName:     "",
							Tolerations:       []corev1.Toleration{},
							HostAliases:       []corev1.HostAlias{},
							PriorityClassName: "",
							Priority:          nil,
							DNSConfig: &corev1.PodDNSConfig{
								Nameservers: []string{},
								Searches:    []string{},
								Options:     []corev1.PodDNSConfigOption{},
							},
							ReadinessGates:     []corev1.PodReadinessGate{},
							RuntimeClassName:   nil,
							EnableServiceLinks: nil,
							PreemptionPolicy:   nil,
							Overhead: map[corev1.ResourceName]resource.Quantity{
								"": {
									Format: "",
								},
							},
							TopologySpreadConstraints: []corev1.TopologySpreadConstraint{},
							SetHostnameAsFQDN:         nil,
						},
						Status: corev1.PodStatus{
							Phase:             "",
							Conditions:        []corev1.PodCondition{},
							Message:           "",
							Reason:            "",
							NominatedNodeName: "",
							HostIP:            "",
							PodIP:             "",
							PodIPs:            []corev1.PodIP{},
							StartTime: &v1.Time{
								Time: time.Time{},
							},
							InitContainerStatuses:      []corev1.ContainerStatus{},
							ContainerStatuses:          []corev1.ContainerStatus{},
							QOSClass:                   "",
							EphemeralContainerStatuses: []corev1.ContainerStatus{},
						},
					}).Return(nil),
				)

				return fields{
					ctrl:          ctrl,
					lock:          sync.RWMutex{},
					BciNodeStatus: networkingv1.BciNodeStatus{},
					BciNodeSpec:   networkingv1.BciNodeSpec{},
					BciNode: networkingv1.BciNode{
						TypeMeta: v1.TypeMeta{
							Kind:       "",
							APIVersion: "",
						},
						ObjectMeta: v1.ObjectMeta{},
						Spec:       networkingv1.BciNodeSpec{},
						Status:     networkingv1.BciNodeStatus{},
					},
					UserIdlePool: map[string]map[string]*queue.Queue{},
					Lookup:       map[string]bool{},
					MarkIPUsed:   map[string]string{},
					Cache:        cache,
					Client:       client,
				}
			}(),
			args: args{
				ctx:          context.TODO(),
				podName:      "busybox",
				podNamespace: "default",
			},
			want:    "",
			want1:   "",
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}

			ipam := &IPAM{
				lock:         tt.fields.lock,
				BciNode:      &tt.fields.BciNode,
				UserIdlePool: tt.fields.UserIdlePool,
				Lookup:       tt.fields.Lookup,
				Cache:        tt.fields.Cache,
				Client:       tt.fields.Client,
			}
			got, got1, err := ipam.getPodSubnetIDAndSecIDs(tt.args.ctx, tt.args.userID, tt.args.podName, tt.args.podNamespace)
			if (err != nil) != tt.wantErr {
				t.Errorf("IPAM.getPodSubnetIDAndSecIDs() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("IPAM.getPodSubnetIDAndSecIDs() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("IPAM.getPodSubnetIDAndSecIDs() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestIPAM_StartUpdateStatusPeriod(t *testing.T) {
	type fields struct {
		lock           sync.RWMutex
		UserIdlePool   map[string]map[string]*queue.Queue
		Lookup         map[string]bool
		Cache          cache.Cache
		BciNode        *networkingv1.BciNode
		RefreshTrigger *trigger.Trigger
		Client         client.Client
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			fields: func() fields {
				var (
					ctx = context.TODO()
				)

				trigger, _ := trigger.NewTrigger(trigger.Parameters{
					MinInterval:  0,
					TriggerFunc:  func([]string) { logger.Info(ctx, "test triggered") },
					ShutdownFunc: func() { logger.Info(ctx, "shutdown") },
					Name:         "ut",
				})

				return fields{
					lock:           sync.RWMutex{},
					UserIdlePool:   map[string]map[string]*queue.Queue{},
					Lookup:         map[string]bool{},
					RefreshTrigger: trigger,
				}
			}(),
			args: args{
				ctx: context.TODO(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ForceUpdateCustomResourcePeriodBak := ForceUpdateCustomResourcePeriod
			ForceUpdateCustomResourcePeriod = time.Millisecond

			defer func() {
				ForceUpdateCustomResourcePeriod = ForceUpdateCustomResourcePeriodBak
			}()

			ipam := &IPAM{
				UserIdlePool:   tt.fields.UserIdlePool,
				Lookup:         tt.fields.Lookup,
				Cache:          tt.fields.Cache,
				BciNode:        tt.fields.BciNode,
				RefreshTrigger: tt.fields.RefreshTrigger,
				Client:         tt.fields.Client,
			}

			ipam.StartUpdateStatusPeriod(tt.args.ctx)
			time.Sleep(ForceUpdateCustomResourcePeriod)
		})
	}
}

func TestIPAM_UpdateStatus(t *testing.T) {
	type fields struct {
		ctrl           *gomock.Controller
		lock           sync.RWMutex
		UserIdlePool   map[string]map[string]*queue.Queue
		Lookup         map[string]bool
		Cache          cache.Cache
		BciNode        *networkingv1.BciNode
		RefreshTrigger *trigger.Trigger
		Client         client.Client
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			fields: func() fields {
				ctrl, cache, client := setupEnv(t)

				var (
					statusWriter = mock.NewMockRuntimeClientStatusWriter(ctrl)
					ctx          = context.TODO()
				)
				gomock.InOrder(
					cache.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).SetArg(2, networkingv1.BciNode{}).Return(nil),
					client.EXPECT().Status().Return(statusWriter),
					statusWriter.EXPECT().Update(gomock.Any(), gomock.Any()).Return(net.ErrClosed),
				)

				trigger, _ := trigger.NewTrigger(trigger.Parameters{
					MinInterval:  0,
					TriggerFunc:  func([]string) { logger.Info(ctx, "test triggered") },
					ShutdownFunc: func() { logger.Info(ctx, "shutdown") },
					Name:         "ut",
				})

				return fields{
					ctrl:         ctrl,
					lock:         sync.RWMutex{},
					UserIdlePool: map[string]map[string]*queue.Queue{},
					Lookup:       map[string]bool{},
					Cache:        cache,
					BciNode: &networkingv1.BciNode{
						TypeMeta: v1.TypeMeta{
							Kind:       "",
							APIVersion: "",
						},
						ObjectMeta: v1.ObjectMeta{},
						Spec: networkingv1.BciNodeSpec{
							InstanceID:   "",
							InstanceType: "",
							EniMultiIP:   networkingv1.EniMultiIPSpec{},
						},
						Status: networkingv1.BciNodeStatus{
							EniMultiIP: networkingv1.EniMultiIPStatus{},
						},
					},
					RefreshTrigger: trigger,
					Client:         client,
				}
			}(),
			args: args{
				ctx: context.TODO(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}

			ipam := &IPAM{
				UserIdlePool:   tt.fields.UserIdlePool,
				Lookup:         tt.fields.Lookup,
				Cache:          tt.fields.Cache,
				BciNode:        tt.fields.BciNode,
				RefreshTrigger: tt.fields.RefreshTrigger,
				Client:         tt.fields.Client,
			}
			ipam.UpdateStatus(tt.args.ctx, []string{})
		})
	}
}

func TestIPAM_gcIP(t *testing.T) {
	type fields struct {
		ctrl         *gomock.Controller
		lock         sync.RWMutex
		BciNode      networkingv1.BciNode
		UserIdlePool map[string]map[string]*queue.Queue
		Lookup       map[string]bool
		WaitGCPod    map[string]time.Time
		Cache        cache.Cache
		Client       client.Client
	}
	type args struct {
		ctx          context.Context
		podName      string
		podNamespace string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    string
		want1   string
		wantErr bool
	}{
		{
			name: "case 1",
			fields: func() fields {
				ctrl, cache, client := setupEnv(t)
				gomock.InOrder(
					cache.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).SetArg(1, corev1.PodList{
						Items: []corev1.Pod{
							{
								TypeMeta: v1.TypeMeta{
									Kind:       "",
									APIVersion: "",
								},
								ObjectMeta: v1.ObjectMeta{
									Name:            "",
									GenerateName:    "",
									Namespace:       "",
									SelfLink:        "",
									UID:             "",
									ResourceVersion: "",
									Generation:      0,
									CreationTimestamp: v1.Time{
										Time: time.Time{},
									},
									DeletionTimestamp: &v1.Time{
										Time: time.Time{},
									},
									DeletionGracePeriodSeconds: nil,
									Labels:                     map[string]string{},
									Annotations: map[string]string{
										"cross-vpc-eni.cce.io/subnetID":         "sbn-1",
										"cross-vpc-eni.cce.io/securityGroupIDs": "g-aaa,g-bbb",
									},
									OwnerReferences: []v1.OwnerReference{},
									Finalizers:      []string{},
									ClusterName:     "",
									ManagedFields:   []v1.ManagedFieldsEntry{},
								},
								Spec: corev1.PodSpec{
									Volumes:                       []corev1.Volume{},
									InitContainers:                []corev1.Container{},
									Containers:                    []corev1.Container{},
									EphemeralContainers:           []corev1.EphemeralContainer{},
									RestartPolicy:                 "",
									TerminationGracePeriodSeconds: nil,
									ActiveDeadlineSeconds:         nil,
									DNSPolicy:                     "",
									NodeSelector: map[string]string{
										"": "",
									},
									ServiceAccountName:           "",
									DeprecatedServiceAccount:     "",
									AutomountServiceAccountToken: nil,
									NodeName:                     "",
									HostNetwork:                  false,
									HostPID:                      false,
									HostIPC:                      false,
									ShareProcessNamespace:        nil,
									SecurityContext: &corev1.PodSecurityContext{
										SELinuxOptions: &corev1.SELinuxOptions{
											User:  "",
											Role:  "",
											Type:  "",
											Level: "",
										},
										WindowsOptions: &corev1.WindowsSecurityContextOptions{
											GMSACredentialSpecName: nil,
											GMSACredentialSpec:     nil,
											RunAsUserName:          nil,
											HostProcess:            nil,
										},
										RunAsUser:           nil,
										RunAsGroup:          nil,
										RunAsNonRoot:        nil,
										SupplementalGroups:  []int64{},
										FSGroup:             nil,
										Sysctls:             []corev1.Sysctl{},
										FSGroupChangePolicy: nil,
										SeccompProfile: &corev1.SeccompProfile{
											Type:             "",
											LocalhostProfile: nil,
										},
									},
									ImagePullSecrets: []corev1.LocalObjectReference{},
									Hostname:         "",
									Subdomain:        "",
									Affinity: &corev1.Affinity{
										NodeAffinity: &corev1.NodeAffinity{
											RequiredDuringSchedulingIgnoredDuringExecution: &corev1.NodeSelector{
												NodeSelectorTerms: []corev1.NodeSelectorTerm{},
											},
											PreferredDuringSchedulingIgnoredDuringExecution: []corev1.PreferredSchedulingTerm{},
										},
										PodAffinity: &corev1.PodAffinity{
											RequiredDuringSchedulingIgnoredDuringExecution:  []corev1.PodAffinityTerm{},
											PreferredDuringSchedulingIgnoredDuringExecution: []corev1.WeightedPodAffinityTerm{},
										},
										PodAntiAffinity: &corev1.PodAntiAffinity{
											RequiredDuringSchedulingIgnoredDuringExecution:  []corev1.PodAffinityTerm{},
											PreferredDuringSchedulingIgnoredDuringExecution: []corev1.WeightedPodAffinityTerm{},
										},
									},
									SchedulerName:     "",
									Tolerations:       []corev1.Toleration{},
									HostAliases:       []corev1.HostAlias{},
									PriorityClassName: "",
									Priority:          nil,
									DNSConfig: &corev1.PodDNSConfig{
										Nameservers: []string{},
										Searches:    []string{},
										Options:     []corev1.PodDNSConfigOption{},
									},
									ReadinessGates:     []corev1.PodReadinessGate{},
									RuntimeClassName:   nil,
									EnableServiceLinks: nil,
									PreemptionPolicy:   nil,
									Overhead: map[corev1.ResourceName]resource.Quantity{
										"": {
											Format: "",
										},
									},
									TopologySpreadConstraints: []corev1.TopologySpreadConstraint{},
									SetHostnameAsFQDN:         nil,
								},
								Status: corev1.PodStatus{
									Phase:             "",
									Conditions:        []corev1.PodCondition{},
									Message:           "",
									Reason:            "",
									NominatedNodeName: "",
									HostIP:            "",
									PodIP:             "",
									PodIPs:            []corev1.PodIP{},
									StartTime: &v1.Time{
										Time: time.Time{},
									},
									InitContainerStatuses:      []corev1.ContainerStatus{},
									ContainerStatuses:          []corev1.ContainerStatus{},
									QOSClass:                   "",
									EphemeralContainerStatuses: []corev1.ContainerStatus{},
								},
							},
						},
					}).Return(nil),
				)
				return fields{
					ctrl: ctrl,
					lock: sync.RWMutex{},
					BciNode: networkingv1.BciNode{
						TypeMeta: v1.TypeMeta{
							Kind:       "",
							APIVersion: "",
						},
						ObjectMeta: v1.ObjectMeta{},
						Spec:       networkingv1.BciNodeSpec{},
						Status: networkingv1.BciNodeStatus{
							EniMultiIP: networkingv1.EniMultiIPStatus{
								Used: map[string]networkingv1.UserAllocationEnis{
									"********************************": map[string]*networkingv1.AllocationEni{
										"eni-0vkhfb98xm78": {
											UserID:           "********************************",
											EniID:            "eni-0vkhfb98xm78",
											MacAddress:       "",
											SubnetID:         "",
											SecurityGroupIDs: []string{},
											VpcID:            "",
											VpcCIDR:          "",
											PrimaryIPAddress: "",
											PrivateIPAddresses: map[string]networkingv1.AllocationIP{
												"*************": {
													UserID:      "********************************",
													EniID:       "eni-0vkhfb98xm78",
													EIP:         nil,
													Owner:       "********************************/busybox",
													ContainerID: "2704632908dbacba91297d1e19c092f4e9586f616e77f4611788c5ce50570dc6",
												},
												"*************": {
													UserID:      "********************************",
													EniID:       "eni-0vkhfb98xm78",
													EIP:         nil,
													Owner:       "********************************/busybox2",
													ContainerID: "2704632908dbacba91297d1e19c092f4e9586f616e77f4611788c5ce50570dc6",
												},
												"*************": {
													UserID:      "********************************",
													EniID:       "eni-0vkhfb98xm78",
													EIP:         nil,
													Owner:       "********************************/busybox3",
													ContainerID: "2704632908dbacba91297d1e19c092f4e9586f616e77f4611788c5ce50570dc6",
												},
											},
										},
									},
								},
								NotReady: []string{},
							},
						},
					},
					UserIdlePool: map[string]map[string]*queue.Queue{},
					Lookup:       map[string]bool{},
					WaitGCPod:    map[string]time.Time{},
					Cache:        cache,
					Client:       client,
				}
			}(),
			args: args{
				ctx:          context.TODO(),
				podName:      "busybox",
				podNamespace: "default",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}

			ipam := &IPAM{
				lock:         tt.fields.lock,
				BciNode:      &tt.fields.BciNode,
				UserIdlePool: tt.fields.UserIdlePool,
				Lookup:       tt.fields.Lookup,
				WaitGCPod:    tt.fields.WaitGCPod,
				Cache:        tt.fields.Cache,
				Client:       tt.fields.Client,
			}
			ipam.WaitGCPod["********************************/busybox"] = time.Now()
			ipam.WaitGCPod["********************************/busybox2"] = time.Now()
			ipam.gcIP(tt.args.ctx)
		})
	}
}

func TestIPAM_isPodReleased(t *testing.T) {
	type fields struct {
		ctrl           *gomock.Controller
		lock           sync.RWMutex
		UserIdlePool   map[string]map[string]*queue.Queue
		Lookup         map[string]bool
		Cache          cache.Cache
		BciNode        *networkingv1.BciNode
		RefreshTrigger *trigger.Trigger
		Client         client.Client
	}
	type args struct {
		podNameSpaceName string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   string
	}{
		{
			fields: func() fields {
				ctrl, cache, client := setupEnv(t)

				return fields{
					ctrl:         ctrl,
					lock:         sync.RWMutex{},
					UserIdlePool: map[string]map[string]*queue.Queue{},
					Lookup:       map[string]bool{},
					Cache:        cache,
					BciNode: &networkingv1.BciNode{
						TypeMeta: v1.TypeMeta{
							Kind:       "",
							APIVersion: "",
						},
						ObjectMeta: v1.ObjectMeta{},
						Spec: networkingv1.BciNodeSpec{
							InstanceID:   "",
							InstanceType: "",
							EniMultiIP:   networkingv1.EniMultiIPSpec{},
						},
						Status: networkingv1.BciNodeStatus{
							EniMultiIP: networkingv1.EniMultiIPStatus{
								Used: map[string]networkingv1.UserAllocationEnis{
									"********************************": map[string]*networkingv1.AllocationEni{
										"eni-0vkhfb98xm78": {
											UserID:           "********************************",
											EniID:            "eni-0vkhfb98xm78",
											MacAddress:       "",
											SubnetID:         "",
											SecurityGroupIDs: []string{},
											VpcID:            "",
											VpcCIDR:          "",
											PrimaryIPAddress: "",
											PrivateIPAddresses: map[string]networkingv1.AllocationIP{
												"*************": {
													UserID:      "********************************",
													EniID:       "eni-0vkhfb98xm78",
													EIP:         nil,
													Owner:       "********************************/busybox",
													ContainerID: "2704632908dbacba91297d1e19c092f4e9586f616e77f4611788c5ce50570dc6",
												},
											},
										},
									},
								},
								NotReady: []string{},
							},
						},
					},
					Client: client,
				}
			}(),
			args: args{
				podNameSpaceName: "********************************/busybox",
			},
			want: "********************************",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}

			ipam := &IPAM{
				UserIdlePool:   tt.fields.UserIdlePool,
				Lookup:         tt.fields.Lookup,
				Cache:          tt.fields.Cache,
				BciNode:        tt.fields.BciNode,
				RefreshTrigger: tt.fields.RefreshTrigger,
				Client:         tt.fields.Client,
			}
			if userID, _ := ipam.isPodReleased(context.TODO(), "********************************/busybox"); userID != tt.want {
				t.Errorf("ipam.isPodReleased = %v, want %v", userID, tt.want)
			}
		})
	}
}

func TestIPAM_isIPAllocated(t *testing.T) {
	type fields struct {
		lock           sync.RWMutex
		UserIdlePool   map[string]map[string]*queue.Queue
		Lookup         map[string]bool
		Cache          cache.Cache
		BciNode        *networkingv1.BciNode
		RefreshTrigger *trigger.Trigger
		WaitGCPod      map[string]time.Time
		WaitReleaseIP  map[string]map[string]networkingv1.IPReleaseStatus
		Nlink          netlinkwrapper.Interface
		Client         client.Client
	}
	type args struct {
		userID string
		eniID  string
		ip     string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   bool
	}{
		{
			name: "case 1",
			fields: fields{
				lock:         sync.RWMutex{},
				UserIdlePool: map[string]map[string]*queue.Queue{},
				Lookup:       map[string]bool{},
				Cache:        nil,
				BciNode: &networkingv1.BciNode{
					Status: networkingv1.BciNodeStatus{
						EniMultiIP: networkingv1.EniMultiIPStatus{
							Used: map[string]networkingv1.UserAllocationEnis{
								"userID": map[string]*networkingv1.AllocationEni{
									"eniID": {
										PrivateIPAddresses: map[string]networkingv1.AllocationIP{
											"*******": {},
										},
									},
								},
							},
							NotReady: []string{},
						},
						WaitReleaseIP: map[string]map[string]networkingv1.IPReleaseStatus{},
					},
				},
				RefreshTrigger: &trigger.Trigger{},
				WaitGCPod:      map[string]time.Time{},
				WaitReleaseIP:  map[string]map[string]networkingv1.IPReleaseStatus{},
				Nlink:          nil,
				Client:         nil,
			},
			args: args{
				userID: "userID",
				eniID:  "eniID",
				ip:     "*******",
			},
			want: true,
		},
		{
			name: "case 2",
			fields: fields{
				lock:         sync.RWMutex{},
				UserIdlePool: map[string]map[string]*queue.Queue{},
				Lookup:       map[string]bool{},
				Cache:        nil,
				BciNode: &networkingv1.BciNode{
					Status: networkingv1.BciNodeStatus{
						EniMultiIP: networkingv1.EniMultiIPStatus{
							Used: map[string]networkingv1.UserAllocationEnis{
								"": map[string]*networkingv1.AllocationEni{
									"": {},
								},
							},
							NotReady: []string{},
						},
						WaitReleaseIP: map[string]map[string]networkingv1.IPReleaseStatus{},
					},
				},
				RefreshTrigger: &trigger.Trigger{},
				WaitGCPod:      map[string]time.Time{},
				WaitReleaseIP:  map[string]map[string]networkingv1.IPReleaseStatus{},
				Nlink:          nil,
				Client:         nil,
			},
			args: args{
				userID: "userID",
				eniID:  "eniID",
				ip:     "*******",
			},
			want: false,
		},
		{
			name: "case 3",
			fields: fields{
				lock:         sync.RWMutex{},
				UserIdlePool: map[string]map[string]*queue.Queue{},
				Lookup:       map[string]bool{},
				Cache:        nil,
				BciNode: &networkingv1.BciNode{
					Status: networkingv1.BciNodeStatus{
						EniMultiIP: networkingv1.EniMultiIPStatus{
							Used: map[string]networkingv1.UserAllocationEnis{
								"userID": map[string]*networkingv1.AllocationEni{
									"": {},
								},
							},
							NotReady: []string{},
						},
						WaitReleaseIP: map[string]map[string]networkingv1.IPReleaseStatus{},
					},
				},
				RefreshTrigger: &trigger.Trigger{},
				WaitGCPod:      map[string]time.Time{},
				WaitReleaseIP:  map[string]map[string]networkingv1.IPReleaseStatus{},
				Nlink:          nil,
				Client:         nil,
			},
			args: args{
				userID: "userID",
				eniID:  "eniID",
				ip:     "*******",
			},
			want: false,
		},
		{
			name: "case 4",
			fields: fields{
				lock:         sync.RWMutex{},
				UserIdlePool: map[string]map[string]*queue.Queue{},
				Lookup:       map[string]bool{},
				Cache:        nil,
				BciNode: &networkingv1.BciNode{
					Status: networkingv1.BciNodeStatus{
						EniMultiIP: networkingv1.EniMultiIPStatus{
							Used:     nil,
							NotReady: []string{},
						},
						WaitReleaseIP: map[string]map[string]networkingv1.IPReleaseStatus{},
					},
				},
				RefreshTrigger: &trigger.Trigger{},
				WaitGCPod:      map[string]time.Time{},
				WaitReleaseIP:  map[string]map[string]networkingv1.IPReleaseStatus{},
				Nlink:          nil,
				Client:         nil,
			},
			args: args{
				userID: "userID",
				eniID:  "eniID",
				ip:     "*******",
			},
			want: false,
		},
		{
			name: "case 5",
			fields: fields{
				lock:         sync.RWMutex{},
				UserIdlePool: map[string]map[string]*queue.Queue{},
				Lookup:       map[string]bool{},
				Cache:        nil,
				BciNode: &networkingv1.BciNode{
					Status: networkingv1.BciNodeStatus{
						EniMultiIP: networkingv1.EniMultiIPStatus{
							Used: map[string]networkingv1.UserAllocationEnis{
								"userID": nil,
							},
							NotReady: []string{},
						},
						WaitReleaseIP: map[string]map[string]networkingv1.IPReleaseStatus{},
					},
				},
				RefreshTrigger: &trigger.Trigger{},
				WaitGCPod:      map[string]time.Time{},
				WaitReleaseIP:  map[string]map[string]networkingv1.IPReleaseStatus{},
				Nlink:          nil,
				Client:         nil,
			},
			args: args{
				userID: "userID",
				eniID:  "eniID",
				ip:     "*******",
			},
			want: false,
		},
		{
			name: "case 6",
			fields: fields{
				lock:         sync.RWMutex{},
				UserIdlePool: map[string]map[string]*queue.Queue{},
				Lookup:       map[string]bool{},
				Cache:        nil,
				BciNode: &networkingv1.BciNode{
					Status: networkingv1.BciNodeStatus{
						EniMultiIP: networkingv1.EniMultiIPStatus{
							Used: map[string]networkingv1.UserAllocationEnis{
								"userID": map[string]*networkingv1.AllocationEni{
									"eniID": {
										PrivateIPAddresses: nil,
									},
								},
							},
							NotReady: []string{},
						},
						WaitReleaseIP: map[string]map[string]networkingv1.IPReleaseStatus{},
					},
				},
				RefreshTrigger: &trigger.Trigger{},
				WaitGCPod:      map[string]time.Time{},
				WaitReleaseIP:  map[string]map[string]networkingv1.IPReleaseStatus{},
				Nlink:          nil,
				Client:         nil,
			},
			args: args{
				userID: "userID",
				eniID:  "eniID",
				ip:     "*******",
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ipam := &IPAM{
				lock:           tt.fields.lock,
				UserIdlePool:   tt.fields.UserIdlePool,
				Lookup:         tt.fields.Lookup,
				Cache:          tt.fields.Cache,
				BciNode:        tt.fields.BciNode,
				RefreshTrigger: tt.fields.RefreshTrigger,
				WaitGCPod:      tt.fields.WaitGCPod,
				WaitReleaseIP:  tt.fields.WaitReleaseIP,
				Nlink:          tt.fields.Nlink,
				Client:         tt.fields.Client,
			}
			if got := ipam.isIPAllocated(tt.args.userID, tt.args.eniID, tt.args.ip); got != tt.want {
				t.Errorf("IPAM.isIPAllocated() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_splitUserIDAndEniID(t *testing.T) {
	type args struct {
		userIDColonEniID string
	}
	tests := []struct {
		name    string
		args    args
		want    string
		want1   string
		wantErr bool
	}{
		{
			name: "normal case",
			args: args{
				userIDColonEniID: "aa:bb",
			},
			want:    "aa",
			want1:   "bb",
			wantErr: false,
		},
		{
			name: "normal case",
			args: args{
				userIDColonEniID: "aabb",
			},
			want:    "",
			want1:   "",
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1, err := splitUserIDAndEniID(tt.args.userIDColonEniID)
			if (err != nil) != tt.wantErr {
				t.Errorf("splitUserIDAndEniID() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("splitUserIDAndEniID() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("splitUserIDAndEniID() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestIPAM_updatedLocalWaitReleaseIP(t *testing.T) {
	type fields struct {
		lock           sync.RWMutex
		UserIdlePool   map[string]map[string]*queue.Queue
		Lookup         map[string]bool
		Cache          cache.Cache
		BciNode        *networkingv1.BciNode
		RefreshTrigger *trigger.Trigger
		WaitGCPod      map[string]time.Time
		WaitReleaseIP  map[string]map[string]networkingv1.IPReleaseStatus
		Nlink          netlinkwrapper.Interface
		Client         client.Client
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   map[string]map[string]networkingv1.IPReleaseStatus
	}{
		{
			name: "all ip are marked",
			fields: fields{
				lock:         sync.RWMutex{},
				UserIdlePool: map[string]map[string]*queue.Queue{},
				Lookup:       map[string]bool{},
				Cache:        nil,
				BciNode: &networkingv1.BciNode{
					TypeMeta: v1.TypeMeta{
						Kind:       "",
						APIVersion: "",
					},
					ObjectMeta: v1.ObjectMeta{
						Name:            "",
						GenerateName:    "",
						Namespace:       "",
						SelfLink:        "",
						UID:             "",
						ResourceVersion: "",
						Generation:      0,
						CreationTimestamp: v1.Time{
							Time: time.Time{},
						},
						DeletionTimestamp: &v1.Time{
							Time: time.Time{},
						},
						DeletionGracePeriodSeconds: nil,
						Labels: map[string]string{
							"": "",
						},
						Annotations: map[string]string{
							"": "",
						},
						OwnerReferences: []v1.OwnerReference{},
						Finalizers:      []string{},
						ClusterName:     "",
						ManagedFields:   []v1.ManagedFieldsEntry{},
					},
					Spec: networkingv1.BciNodeSpec{
						InstanceID:   "",
						InstanceType: "",
						EniMultiIP: networkingv1.EniMultiIPSpec{
							Pool: map[string]networkingv1.UserAllocationEnis{
								"": map[string]*networkingv1.AllocationEni{
									"": {
										UserID:           "",
										EniID:            "",
										MacAddress:       "",
										SubnetID:         "",
										SecurityGroupIDs: []string{},
										VpcID:            "",
										VpcCIDR:          "",
										PrimaryIPAddress: "",
										PrivateIPAddresses: map[string]networkingv1.AllocationIP{
											"": {
												UserID:      "",
												EniID:       "",
												EIP:         nil,
												Owner:       "",
												ContainerID: "",
											},
										},
									},
								},
							},
							MinAllocate:       0,
							MaxAllocate:       0,
							PreAllocate:       0,
							MaxAboveWatermark: 0,
						},
					},
					Status: networkingv1.BciNodeStatus{
						EniMultiIP: networkingv1.EniMultiIPStatus{
							Used: map[string]networkingv1.UserAllocationEnis{
								"": map[string]*networkingv1.AllocationEni{
									"": {
										UserID:           "",
										EniID:            "",
										MacAddress:       "",
										SubnetID:         "",
										SecurityGroupIDs: []string{},
										VpcID:            "",
										VpcCIDR:          "",
										PrimaryIPAddress: "",
										PrivateIPAddresses: map[string]networkingv1.AllocationIP{
											"": {
												UserID:      "",
												EniID:       "",
												EIP:         nil,
												Owner:       "",
												ContainerID: "",
											},
										},
									},
								},
							},
							NotReady: []string{},
						},
						WaitReleaseIP: map[string]map[string]networkingv1.IPReleaseStatus{
							"": {
								"": "",
							},
						},
					},
				},
				RefreshTrigger: &trigger.Trigger{},
				WaitGCPod:      map[string]time.Time{},
				WaitReleaseIP: map[string]map[string]networkingv1.IPReleaseStatus{
					"user1:eni1": {
						"*******": networkingv1.IPAMDoNotRelease,
						"*******": networkingv1.IPAMReadyForRelease,
					},
				},
				Nlink:  nil,
				Client: nil,
			},
			args: args{
				ctx: context.TODO(),
			},
			want: map[string]map[string]networkingv1.IPReleaseStatus{
				"user1:eni1": {
					"*******": networkingv1.IPAMDoNotRelease,
					"*******": networkingv1.IPAMReadyForRelease,
				},
			},
		},
		{
			name: "normal case",
			fields: fields{
				lock:         sync.RWMutex{},
				UserIdlePool: map[string]map[string]*queue.Queue{},
				Lookup:       map[string]bool{},
				Cache:        nil,
				BciNode: &networkingv1.BciNode{
					TypeMeta: v1.TypeMeta{
						Kind:       "",
						APIVersion: "",
					},
					ObjectMeta: v1.ObjectMeta{
						Name:            "",
						GenerateName:    "",
						Namespace:       "",
						SelfLink:        "",
						UID:             "",
						ResourceVersion: "",
						Generation:      0,
						CreationTimestamp: v1.Time{
							Time: time.Time{},
						},
						DeletionTimestamp: &v1.Time{
							Time: time.Time{},
						},
						DeletionGracePeriodSeconds: nil,
						Labels: map[string]string{
							"": "",
						},
						Annotations: map[string]string{
							"": "",
						},
						OwnerReferences: []v1.OwnerReference{},
						Finalizers:      []string{},
						ClusterName:     "",
						ManagedFields:   []v1.ManagedFieldsEntry{},
					},
					Spec: networkingv1.BciNodeSpec{
						InstanceID:   "",
						InstanceType: "",
						EniMultiIP: networkingv1.EniMultiIPSpec{
							Pool: map[string]networkingv1.UserAllocationEnis{
								"": map[string]*networkingv1.AllocationEni{
									"": {
										UserID:           "",
										EniID:            "",
										MacAddress:       "",
										SubnetID:         "",
										SecurityGroupIDs: []string{},
										VpcID:            "",
										VpcCIDR:          "",
										PrimaryIPAddress: "",
										PrivateIPAddresses: map[string]networkingv1.AllocationIP{
											"": {
												UserID:      "",
												EniID:       "",
												EIP:         nil,
												Owner:       "",
												ContainerID: "",
											},
										},
									},
								},
							},
							MinAllocate:       0,
							MaxAllocate:       0,
							PreAllocate:       0,
							MaxAboveWatermark: 0,
						},
					},
					Status: networkingv1.BciNodeStatus{
						EniMultiIP: networkingv1.EniMultiIPStatus{
							Used: map[string]networkingv1.UserAllocationEnis{
								"user1": map[string]*networkingv1.AllocationEni{
									"eni1": {
										PrivateIPAddresses: map[string]networkingv1.AllocationIP{
											"*******": {},
										},
									},
								},
							},
							NotReady: []string{},
						},
						WaitReleaseIP: map[string]map[string]networkingv1.IPReleaseStatus{},
					},
				},
				RefreshTrigger: &trigger.Trigger{},
				WaitGCPod:      map[string]time.Time{},
				WaitReleaseIP: map[string]map[string]networkingv1.IPReleaseStatus{
					"user1:eni1": {
						"*******": networkingv1.IPAMReleased,
						"*******": networkingv1.IPAMMarkForRelease,
						"*******": networkingv1.IPAMMarkForRelease,
					},
				},
				Nlink:  nil,
				Client: nil,
			},
			args: args{
				ctx: context.TODO(),
			},
			want: map[string]map[string]networkingv1.IPReleaseStatus{
				"user1:eni1": {
					"*******": networkingv1.IPAMDoNotRelease,
					"*******": networkingv1.IPAMReadyForRelease,
				},
			},
		},
		{
			name: "abnormal case",
			fields: fields{
				lock:         sync.RWMutex{},
				UserIdlePool: map[string]map[string]*queue.Queue{},
				Lookup:       map[string]bool{},
				Cache:        nil,
				BciNode: &networkingv1.BciNode{
					TypeMeta: v1.TypeMeta{
						Kind:       "",
						APIVersion: "",
					},
					ObjectMeta: v1.ObjectMeta{
						Name:            "",
						GenerateName:    "",
						Namespace:       "",
						SelfLink:        "",
						UID:             "",
						ResourceVersion: "",
						Generation:      0,
						CreationTimestamp: v1.Time{
							Time: time.Time{},
						},
						DeletionTimestamp: &v1.Time{
							Time: time.Time{},
						},
						DeletionGracePeriodSeconds: nil,
						Labels: map[string]string{
							"": "",
						},
						Annotations: map[string]string{
							"": "",
						},
						OwnerReferences: []v1.OwnerReference{},
						Finalizers:      []string{},
						ClusterName:     "",
						ManagedFields:   []v1.ManagedFieldsEntry{},
					},
					Spec: networkingv1.BciNodeSpec{
						InstanceID:   "",
						InstanceType: "",
						EniMultiIP: networkingv1.EniMultiIPSpec{
							Pool: map[string]networkingv1.UserAllocationEnis{
								"": map[string]*networkingv1.AllocationEni{
									"": {
										UserID:           "",
										EniID:            "",
										MacAddress:       "",
										SubnetID:         "",
										SecurityGroupIDs: []string{},
										VpcID:            "",
										VpcCIDR:          "",
										PrimaryIPAddress: "",
										PrivateIPAddresses: map[string]networkingv1.AllocationIP{
											"": {
												UserID:      "",
												EniID:       "",
												EIP:         nil,
												Owner:       "",
												ContainerID: "",
											},
										},
									},
								},
							},
							MinAllocate:       0,
							MaxAllocate:       0,
							PreAllocate:       0,
							MaxAboveWatermark: 0,
						},
					},
					Status: networkingv1.BciNodeStatus{
						EniMultiIP: networkingv1.EniMultiIPStatus{
							Used: map[string]networkingv1.UserAllocationEnis{
								"user1": map[string]*networkingv1.AllocationEni{
									"eni1": {
										PrivateIPAddresses: map[string]networkingv1.AllocationIP{
											"*******": {},
										},
									},
								},
							},
							NotReady: []string{},
						},
						WaitReleaseIP: map[string]map[string]networkingv1.IPReleaseStatus{},
					},
				},
				RefreshTrigger: &trigger.Trigger{},
				WaitGCPod:      map[string]time.Time{},
				WaitReleaseIP: map[string]map[string]networkingv1.IPReleaseStatus{
					"user1:eni1": {
						"*******": networkingv1.IPAMReleased,
						"*******": networkingv1.IPAMMarkForRelease,
						"*******": networkingv1.IPAMMarkForRelease,
					},
					"user2eni2": {
						"*******": networkingv1.IPAMMarkForRelease,
					},
				},
				Nlink:  nil,
				Client: nil,
			},
			args: args{
				ctx: context.TODO(),
			},
			want: map[string]map[string]networkingv1.IPReleaseStatus{
				"user1:eni1": {
					"*******": networkingv1.IPAMDoNotRelease,
					"*******": networkingv1.IPAMReadyForRelease,
				},
				"user2eni2": {
					"*******": networkingv1.IPAMMarkForRelease,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ipam := &IPAM{
				lock:           tt.fields.lock,
				UserIdlePool:   tt.fields.UserIdlePool,
				Lookup:         tt.fields.Lookup,
				Cache:          tt.fields.Cache,
				BciNode:        tt.fields.BciNode,
				RefreshTrigger: tt.fields.RefreshTrigger,
				WaitGCPod:      tt.fields.WaitGCPod,
				WaitReleaseIP:  tt.fields.WaitReleaseIP,
				Nlink:          tt.fields.Nlink,
				Client:         tt.fields.Client,
			}
			if got := ipam.updatedLocalWaitReleaseIP(tt.args.ctx); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("IPAM.updatedLocalWaitReleaseIP() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestIPAM_getPodNamesOnCurrentNodeByUserID(t *testing.T) {
	type fields struct {
		ctrl           *gomock.Controller
		lock           sync.RWMutex
		UserIdlePool   map[string]map[string]*queue.Queue
		Lookup         map[string]bool
		Cache          cache.Cache
		BciNode        *networkingv1.BciNode
		RefreshTrigger *trigger.Trigger
		WaitGCPod      map[string]time.Time
		WaitReleaseIP  map[string]map[string]networkingv1.IPReleaseStatus
		Nlink          netlinkwrapper.Interface
		Client         client.Client
	}
	type args struct {
		ctx    context.Context
		userID string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    map[string]string
		wantErr bool
	}{
		{
			name: "Admin user Pod",
			fields: func() fields {
				ctrl, cache, client := setupEnv(t)

				gomock.InOrder(
					cache.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).SetArg(1, corev1.PodList{
						Items: []corev1.Pod{
							{
								TypeMeta: v1.TypeMeta{
									Kind:       "",
									APIVersion: "",
								},
								ObjectMeta: v1.ObjectMeta{
									Name:            "busybox0",
									GenerateName:    "",
									Namespace:       "kube-system",
									SelfLink:        "",
									UID:             "",
									ResourceVersion: "",
									Generation:      0,
									CreationTimestamp: v1.Time{
										Time: time.Time{},
									},
									DeletionTimestamp: &v1.Time{
										Time: time.Time{},
									},
									DeletionGracePeriodSeconds: nil,
									Labels: map[string]string{
										"": "",
									},
									Annotations: map[string]string{
										"": "",
									},
									OwnerReferences: []v1.OwnerReference{},
									Finalizers:      []string{},
									ClusterName:     "",
									ManagedFields:   []v1.ManagedFieldsEntry{},
								},
								Spec: corev1.PodSpec{},
								Status: corev1.PodStatus{
									Phase:             corev1.PodRunning,
									Conditions:        []corev1.PodCondition{},
									Message:           "",
									Reason:            "",
									NominatedNodeName: "",
									HostIP:            "",
									PodIP:             "*******",
								},
							},
							{
								TypeMeta: v1.TypeMeta{
									Kind:       "",
									APIVersion: "",
								},
								ObjectMeta: v1.ObjectMeta{
									Name:            "busybox1",
									GenerateName:    "",
									Namespace:       "kube-system",
									SelfLink:        "",
									UID:             "",
									ResourceVersion: "",
									Generation:      0,
									CreationTimestamp: v1.Time{
										Time: time.Time{},
									},
									DeletionTimestamp: &v1.Time{
										Time: time.Time{},
									},
									DeletionGracePeriodSeconds: nil,
									Labels: map[string]string{
										"": "",
									},
									Annotations: map[string]string{
										"": "",
									},
									OwnerReferences: []v1.OwnerReference{},
									Finalizers:      []string{},
									ClusterName:     "",
									ManagedFields:   []v1.ManagedFieldsEntry{},
								},
								Spec: corev1.PodSpec{},
								Status: corev1.PodStatus{
									Phase:             corev1.PodSucceeded,
									Conditions:        []corev1.PodCondition{},
									Message:           "",
									Reason:            "",
									NominatedNodeName: "",
									HostIP:            "",
									PodIP:             "*******",
								},
							},
						},
					}).Return(nil),
				)

				return fields{
					ctrl:         ctrl,
					lock:         sync.RWMutex{},
					UserIdlePool: map[string]map[string]*queue.Queue{},
					Lookup:       map[string]bool{},
					Cache:        cache,
					BciNode: &networkingv1.BciNode{
						Spec:   networkingv1.BciNodeSpec{},
						Status: networkingv1.BciNodeStatus{},
					},
					RefreshTrigger: &trigger.Trigger{},
					WaitGCPod: map[string]time.Time{
						"": {},
					},
					WaitReleaseIP: map[string]map[string]networkingv1.IPReleaseStatus{},
					Nlink:         nil,
					Client:        client,
				}
			}(),
			args: args{
				ctx:    context.TODO(),
				userID: ADMINUSERNAME,
			},
			want: map[string]string{
				"busybox0": "kube-system/*******",
			},
			wantErr: false,
		},
		{
			name: "User Pod",
			fields: func() fields {
				ctrl, cache, client := setupEnv(t)

				gomock.InOrder(
					cache.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).SetArg(1, corev1.PodList{
						Items: []corev1.Pod{
							{
								TypeMeta: v1.TypeMeta{
									Kind:       "",
									APIVersion: "",
								},
								ObjectMeta: v1.ObjectMeta{
									Name:            "p-aaa",
									GenerateName:    "",
									Namespace:       "abc",
									SelfLink:        "",
									UID:             "",
									ResourceVersion: "",
									Generation:      0,
									CreationTimestamp: v1.Time{
										Time: time.Time{},
									},
									DeletionTimestamp: &v1.Time{
										Time: time.Time{},
									},
									DeletionGracePeriodSeconds: nil,
									Labels: map[string]string{
										"": "",
									},
									Annotations: map[string]string{
										"": "",
									},
									OwnerReferences: []v1.OwnerReference{},
									Finalizers:      []string{},
									ClusterName:     "",
									ManagedFields:   []v1.ManagedFieldsEntry{},
								},
								Spec: corev1.PodSpec{},
								Status: corev1.PodStatus{
									Phase:             corev1.PodRunning,
									Conditions:        []corev1.PodCondition{},
									Message:           "",
									Reason:            "",
									NominatedNodeName: "",
									HostIP:            "",
									PodIP:             "*******",
								},
							},
							{
								TypeMeta: v1.TypeMeta{
									Kind:       "",
									APIVersion: "",
								},
								ObjectMeta: v1.ObjectMeta{
									Name:            "p-bbb",
									GenerateName:    "",
									Namespace:       "abc",
									SelfLink:        "",
									UID:             "",
									ResourceVersion: "",
									Generation:      0,
									CreationTimestamp: v1.Time{
										Time: time.Time{},
									},
									DeletionTimestamp: &v1.Time{
										Time: time.Time{},
									},
									DeletionGracePeriodSeconds: nil,
									Labels: map[string]string{
										"": "",
									},
									Annotations: map[string]string{
										"": "",
									},
									OwnerReferences: []v1.OwnerReference{},
									Finalizers:      []string{},
									ClusterName:     "",
									ManagedFields:   []v1.ManagedFieldsEntry{},
								},
								Spec: corev1.PodSpec{},
								Status: corev1.PodStatus{
									Phase:             corev1.PodSucceeded,
									Conditions:        []corev1.PodCondition{},
									Message:           "",
									Reason:            "",
									NominatedNodeName: "",
									HostIP:            "",
									PodIP:             "*******",
								},
							},
						},
					}).Return(nil),
				)

				return fields{
					ctrl:         ctrl,
					lock:         sync.RWMutex{},
					UserIdlePool: map[string]map[string]*queue.Queue{},
					Lookup:       map[string]bool{},
					Cache:        cache,
					BciNode: &networkingv1.BciNode{
						Spec:   networkingv1.BciNodeSpec{},
						Status: networkingv1.BciNodeStatus{},
					},
					RefreshTrigger: &trigger.Trigger{},
					WaitGCPod: map[string]time.Time{
						"": {},
					},
					WaitReleaseIP: map[string]map[string]networkingv1.IPReleaseStatus{},
					Nlink:         nil,
					Client:        client,
				}
			}(),
			args: args{
				ctx:    context.TODO(),
				userID: "abc",
			},
			want: map[string]string{
				"p-aaa": "abc/*******",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}

			ipam := &IPAM{
				lock:           tt.fields.lock,
				UserIdlePool:   tt.fields.UserIdlePool,
				Lookup:         tt.fields.Lookup,
				Cache:          tt.fields.Cache,
				BciNode:        tt.fields.BciNode,
				RefreshTrigger: tt.fields.RefreshTrigger,
				WaitGCPod:      tt.fields.WaitGCPod,
				WaitReleaseIP:  tt.fields.WaitReleaseIP,
				Nlink:          tt.fields.Nlink,
				Client:         tt.fields.Client,
			}
			got, err := ipam.getPodNamesOnCurrentNodeByUserID(tt.args.ctx, tt.args.userID)
			if (err != nil) != tt.wantErr {
				t.Errorf("IPAM.getPodNamesOnCurrentNodeByUserID() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("IPAM.getPodNamesOnCurrentNodeByUserID() = %v, want %v", got, tt.want)
			}
		})
	}
}
