/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package ipam

import (
	"context"

	"sigs.k8s.io/controller-runtime/pkg/cache"
	"sigs.k8s.io/controller-runtime/pkg/client"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/rpc"
)

//go:generate mockgen -copyright_file=${GOPATH}/src/icode.baidu.com/baidu/bci2/bci-cni-driver/hack/boilerplate.go.txt -destination=./mock/mock.go -package=mock icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/nodeagent/ipam Interface,RuntimeCache,RuntimeClient,RuntimeClientStatusWriter
type Interface interface {
	AllocateIP(ctx context.Context, podName string, podNameSpace string, podID string) (*rpc.ENIMultiIPReply, error)
	ReleaseIP(ctx context.Context, podName string, podNameSpace string, podID string) error
}

type RuntimeCache interface {
	cache.Cache
}

type RuntimeClient interface {
	client.Client
}
type RuntimeClientStatusWriter interface {
	client.StatusWriter
}
