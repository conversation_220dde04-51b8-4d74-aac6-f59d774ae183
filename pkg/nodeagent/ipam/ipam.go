package ipam

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"sync"
	"time"

	corev1 "k8s.io/api/core/v1"
	"sigs.k8s.io/controller-runtime/pkg/cache"
	"sigs.k8s.io/controller-runtime/pkg/client"

	networkingv1 "icode.baidu.com/baidu/bci2/bci-cni-driver/apis/networking/v1"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/logger"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/nodeagent/utils/enilink"
	utilenv "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/nodeagent/utils/env"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/nodeagent/utils/trigger"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/queue"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/rpc"
	netlinkwrapper "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/netlink"
)

const (
	BciNodeMetaFilePath = "/run/bci-cni/bcinode.meta"
	ADMINUSERNAME       = "admin"
	// pod 声明了多个eip，在多eip场景下，pod需要挂载多个private ip并绑定eip
	podMultiEIPs               = "bci.virtual-kubelet.io/bci-multi-eip-ips"
	podIPAnnotation            = "bci_internal_PodIP"
	podIPv6Annotation          = "bci_internal_PodIPv6"
	podAllocatedIPv6Annotation = "bci.baidu.com/allocated-ipv6Address"
	podEniIdAnnotation         = "bci_internal_eni_id"
	podMultiIPAnnotation       = "bci_internal_multi_podips"
	// Pod IPv6启用annotation
	podEnableIPv6Annotation = "bci.baidu.com/bci-enable-ipv6"
)

var (
	RequireUniqueRouteTable bool = false
)

type PayLoad struct {
	allocationIP networkingv1.AllocationIP
	address      string
	macAddress   string
}

type IPAM struct {
	lock sync.RWMutex
	// UserIdlePool[userID][eniID] - IPv4地址池
	UserIdlePool map[string]map[string]*queue.Queue
	// IPv6UserIdlePool[userID][eniID] - IPv6地址池
	IPv6UserIdlePool map[string]map[string]*queue.Queue
	Lookup           map[string]bool // IPv4查询索引
	IPv6Lookup       map[string]bool // IPv6查询索引
	Cache            cache.Cache
	BciNode          *networkingv1.BciNode
	RefreshTrigger   *trigger.Trigger
	WaitGCPod        map[string]time.Time
	// WaitReleaseIP[userIDEniID][ip]=do-not-release
	WaitReleaseIP map[string]map[string]networkingv1.IPReleaseStatus
	Nlink         netlinkwrapper.Interface
	client.Client
}

var _ Interface = &IPAM{}

func (ipam *IPAM) DeleteUserIdlePool(ctx context.Context, userAllocDel networkingv1.AllocationMap) {
	// TODO: delete bcinode status. status should already been deleted when pod deleted
	// other word: should not delete crd or eni, if there are pods using it.
	// for now: fixed eni and ips, this logic will not be used.
	for userID, userAlloc := range userAllocDel {
		for eniID := range userAlloc {
			if _, ok := ipam.UserIdlePool[userID]; ok {
				// 删除IPv4地址
				for ip := range userAlloc[eniID].PrivateIPAddresses {
					delete(ipam.Lookup, ip)
				}
				delete(ipam.UserIdlePool[userID], eniID)
				logger.Infof(ctx, "delete eni: %s", eniID)
			}
			// 删除IPv6地址
			if _, ok := ipam.IPv6UserIdlePool[userID]; ok {
				for ip := range userAlloc[eniID].PrivateIPv6Addresses {
					delete(ipam.IPv6Lookup, ip)
				}
				delete(ipam.IPv6UserIdlePool[userID], eniID)
			}
		}
		if len(ipam.UserIdlePool[userID]) == 0 {
			delete(ipam.UserIdlePool, userID)
			logger.Infof(ctx, "delete user: %s", userID)
		}
		if len(ipam.IPv6UserIdlePool[userID]) == 0 {
			delete(ipam.IPv6UserIdlePool, userID)
		}
	}
}

// crd controller update crd spec
func (ipam *IPAM) UpdateUserIdlePool(ctx context.Context, userAllocAdd networkingv1.AllocationMap, userAllocDel networkingv1.AllocationMap) {
	ipam.lock.Lock()
	defer ipam.lock.Unlock()

	ipam.DeleteUserIdlePool(ctx, userAllocDel)
	for userID, userAlloc := range userAllocAdd {
		for eniID, eniAlloc := range userAlloc {
			// 处理IPv4地址
			for ip, ipAlloc := range eniAlloc.PrivateIPAddresses {
				if _, ok := ipam.Lookup[ip]; ok {
					// already enqueue
					continue
				}
				payLoad := PayLoad{
					allocationIP: *ipAlloc.DeepCopy(),
					address:      ip,
					macAddress:   eniAlloc.MacAddress,
				}
				if _, ok := ipam.UserIdlePool[userID]; !ok {
					ipam.UserIdlePool[userID] = make(map[string]*queue.Queue)
				}
				if _, ok := ipam.UserIdlePool[userID][eniID]; !ok {
					ipam.UserIdlePool[userID][eniID] = queue.New()
				}
				ipam.UserIdlePool[userID][eniID].Append(payLoad)
				ipam.Lookup[ip] = true
				logger.Infof(ctx, "append IPv4 %+v", payLoad)
			}
			// 处理IPv6地址
			for ip, ipAlloc := range eniAlloc.PrivateIPv6Addresses {
				if _, ok := ipam.IPv6Lookup[ip]; ok {
					// already enqueue
					continue
				}
				payLoad := PayLoad{
					allocationIP: *ipAlloc.DeepCopy(),
					address:      ip,
					macAddress:   eniAlloc.MacAddress,
				}
				if _, ok := ipam.IPv6UserIdlePool[userID]; !ok {
					ipam.IPv6UserIdlePool[userID] = make(map[string]*queue.Queue)
				}
				if _, ok := ipam.IPv6UserIdlePool[userID][eniID]; !ok {
					ipam.IPv6UserIdlePool[userID][eniID] = queue.New()
				}
				ipam.IPv6UserIdlePool[userID][eniID].Append(payLoad)
				ipam.IPv6Lookup[ip] = true
				logger.Infof(ctx, "append IPv6 %+v", payLoad)
			}
		}
	}
}

func (ipam *IPAM) RebuildUserIdlePool(ctx context.Context, allocationPool networkingv1.AllocationMap) {
	ipam.lock.Lock()
	defer ipam.lock.Unlock()

	ipam.UserIdlePool = make(map[string]map[string]*queue.Queue)
	ipam.IPv6UserIdlePool = make(map[string]map[string]*queue.Queue)

	for userID, userAlloc := range allocationPool {
		for eniID, eniAlloc := range userAlloc {
			// 处理IPv4地址
			for ip, ipAlloc := range eniAlloc.PrivateIPAddresses {
				if ipam.isIPAllocated(userID, eniID, ip) {
					continue
				}

				payLoad := PayLoad{
					allocationIP: *ipAlloc.DeepCopy(),
					address:      ip,
					macAddress:   eniAlloc.MacAddress,
				}
				if _, ok := ipam.UserIdlePool[userID]; !ok {
					ipam.UserIdlePool[userID] = make(map[string]*queue.Queue)
				}
				if _, ok := ipam.UserIdlePool[userID][eniID]; !ok {
					ipam.UserIdlePool[userID][eniID] = queue.New()
				}
				ipam.UserIdlePool[userID][eniID].Append(payLoad)
				logger.Infof(ctx, "rebuild queue, append IPv4 %+v", payLoad)
			}
			// 处理IPv6地址
			for ip, ipAlloc := range eniAlloc.PrivateIPv6Addresses {
				if ipam.isIPv6Allocated(userID, eniID, ip) {
					continue
				}

				payLoad := PayLoad{
					allocationIP: *ipAlloc.DeepCopy(),
					address:      ip,
					macAddress:   eniAlloc.MacAddress,
				}
				if _, ok := ipam.IPv6UserIdlePool[userID]; !ok {
					ipam.IPv6UserIdlePool[userID] = make(map[string]*queue.Queue)
				}
				if _, ok := ipam.IPv6UserIdlePool[userID][eniID]; !ok {
					ipam.IPv6UserIdlePool[userID][eniID] = queue.New()
				}
				ipam.IPv6UserIdlePool[userID][eniID].Append(payLoad)
				logger.Infof(ctx, "rebuild queue, append IPv6 %+v", payLoad)
			}
		}
	}
}

func (ipam *IPAM) ipIsUsed(userID, eniID, ip string) bool {
	if _, ok := ipam.BciNode.Status.EniMultiIP.Used[userID]; !ok {
		return false
	}

	if _, ok := ipam.BciNode.Status.EniMultiIP.Used[userID][eniID]; !ok {
		return false
	}

	_, ok := ipam.BciNode.Status.EniMultiIP.Used[userID][eniID].PrivateIPAddresses[ip]

	return ok
}

// anntate node for eni creating.
func (ipam *IPAM) RequestEni(ctx context.Context, userID string, podName string, podNameSpace string) error {
	nodeName, err := utilenv.GetNodeName(ctx)
	if err != nil {
		return errors.New("unable to get node name in ipam")
	}

	original := &corev1.Node{}
	err = ipam.Client.Get(context.Background(), client.ObjectKey{
		Name: nodeName,
	}, original)
	if err != nil {
		return fmt.Errorf("get node spec failed in ipam: %s, %w", nodeName, err)
	}

	// network controller is responsible for eni dedup
	patch := client.MergeFrom(original.DeepCopy())
	key := fmt.Sprintf("bci-wait-eni-%s", userID)
	value := fmt.Sprintf("%s/%s", podNameSpace, podName)
	original.Annotations[key] = value
	// TODO: add retry logic
	err = ipam.Client.Patch(context.Background(), original, patch)
	if err != nil {
		return fmt.Errorf("patch node anntation failed in ipam: %s, %s, %s", userID, podNameSpace, podName)
	}

	return nil
}

func (ipam *IPAM) RequestPrivateIP(ctx context.Context, userID, eniID string) error {
	nodeName, err := utilenv.GetNodeName(ctx)
	if err != nil {
		return errors.New("unable to get node name in ipam")
	}

	node := &corev1.Node{}
	err = ipam.Client.Get(context.Background(), client.ObjectKey{
		Name: nodeName,
	}, node)
	if err != nil {
		return fmt.Errorf("get node spec failed in ipam: %s, %w", nodeName, err)
	}

	// network controller is responsible for iaas resources
	patch := client.MergeFrom(node.DeepCopy())
	key := fmt.Sprintf("bci-wait-private-ip-%s", eniID)
	node.Annotations[key] = userID
	// TODO: add retry logic
	err = ipam.Client.Patch(context.Background(), node, patch)
	if err != nil {
		return fmt.Errorf("patch node annotation failed in ipam: %s, %s, %s", userID, eniID, err)
	}

	return nil
}

func (ipam *IPAM) PatchBciInternalIP(ctx context.Context, namespace, name, ip string, multiIPs string, eniId string, undo bool) error {
	var (
		pod = &corev1.Pod{}
	)

	err := ipam.Client.Get(ctx, client.ObjectKey{
		Namespace: namespace,
		Name:      name,
	}, pod)
	if err != nil {
		return fmt.Errorf("get pod(%v/%v) failed in ipam: %w", namespace, name, err)
	}

	if pod.Annotations == nil {
		pod.Annotations = make(map[string]string)
	}

	patch := client.MergeFrom(pod.DeepCopy())

	if !undo {
		pod.Annotations[podIPAnnotation] = ip
		pod.Annotations[podMultiIPAnnotation] = multiIPs
		pod.Annotations[podEniIdAnnotation] = eniId
	} else {
		delete(pod.Annotations, podIPAnnotation)
		delete(pod.Annotations, podMultiIPAnnotation)
		delete(pod.Annotations, podEniIdAnnotation)
	}

	err = ipam.Client.Patch(ctx, pod, patch)
	if err != nil {
		return fmt.Errorf("patch pod(%v/%v) annotation failed in ipam: %w", namespace, name, err)
	}

	return nil
}

func (ipam *IPAM) PatchBciInternalIPWithIPv6(ctx context.Context, namespace, name, ip string, multiIPs string, ipv6 string, eniId string, undo bool) error {
	var (
		pod = &corev1.Pod{}
	)

	err := ipam.Client.Get(ctx, client.ObjectKey{
		Namespace: namespace,
		Name:      name,
	}, pod)
	if err != nil {
		return fmt.Errorf("get pod(%v/%v) failed in ipam: %w", namespace, name, err)
	}

	if pod.Annotations == nil {
		pod.Annotations = make(map[string]string)
	}

	patch := client.MergeFrom(pod.DeepCopy())

	if !undo {
		pod.Annotations[podIPAnnotation] = ip
		pod.Annotations[podMultiIPAnnotation] = multiIPs
		pod.Annotations[podEniIdAnnotation] = eniId
		if ipv6 != "" {
			pod.Annotations[podIPv6Annotation] = ipv6
			pod.Annotations[podAllocatedIPv6Annotation] = ipv6
		}
	} else {
		delete(pod.Annotations, podIPAnnotation)
		delete(pod.Annotations, podMultiIPAnnotation)
		delete(pod.Annotations, podEniIdAnnotation)
		delete(pod.Annotations, podIPv6Annotation)
		delete(pod.Annotations, podAllocatedIPv6Annotation)
	}

	err = ipam.Client.Patch(ctx, pod, patch)
	if err != nil {
		return fmt.Errorf("patch pod(%v/%v) annotation failed in ipam: %w", namespace, name, err)
	}

	return nil
}

// TODO: how to handle init failed eni
func (ipam *IPAM) MarkEniNotReady(ctx context.Context, enis []string) error {
	ipam.lock.Lock()
	defer ipam.lock.Unlock()
	logger.Infof(ctx, "update not ready enis: %v", enis)
	bciNode, err := ipam.getBciNode(ctx)
	if err != nil {
		logger.Errorf(ctx, "update crd in mark ip used error %v", err)
	}

	// we need to upload immed to tell network controller eni init failed
	bciNode.Status.EniMultiIP.NotReady = append(bciNode.Status.EniMultiIP.NotReady, enis...)
	ipam.BciNode.Status.EniMultiIP.NotReady = append(ipam.BciNode.Status.EniMultiIP.NotReady, enis...)
	err = ipam.Client.Status().Update(context.Background(), bciNode.DeepCopy())
	if err != nil {
		logger.Errorf(ctx, "mark eni not ready error")
		return err
	}
	return nil
}

// thread safe
func (ipam *IPAM) markIPUsed(ctx context.Context, userID string, eniID string, ips []string,
	podName string, podNameSpace string, podID string) ([]string, error) {
	markFailedIP := make([]string, 0)
	if ipam.BciNode == nil {
		return markFailedIP, errors.New("bci node is not initialized yet")
	}
	if _, ok := ipam.BciNode.Status.EniMultiIP.Used[userID]; !ok {
		if ipam.BciNode.Status.EniMultiIP.Used == nil {
			ipam.BciNode.Status.EniMultiIP.Used = networkingv1.AllocationMap{}
		}
		ipam.BciNode.Status.EniMultiIP.Used[userID] = networkingv1.UserAllocationEnis{}
	}
	allocEnis := ipam.BciNode.Status.EniMultiIP.Used[userID]
	if _, ok := allocEnis[eniID]; !ok {
		allocEnis[eniID] = &networkingv1.AllocationEni{}
	}

	allocEni := allocEnis[eniID]
	if allocEni.PrivateIPAddresses == nil {
		allocEni.PrivateIPAddresses = make(map[string]networkingv1.AllocationIP)
	}

	for _, ip := range ips {
		if _, ok := allocEni.PrivateIPAddresses[ip]; ok {
			markFailedIP = append(markFailedIP, ip)
			continue
		}
		allocEni.PrivateIPAddresses[ip] = networkingv1.AllocationIP{
			Owner:       fmt.Sprintf("%s/%s", podNameSpace, podName),
			UserID:      userID,
			EniID:       eniID,
			ContainerID: podID,
		}
	}

	if len(markFailedIP) != 0 {
		return markFailedIP, errors.New(fmt.Sprintf("ip %v has USED", markFailedIP))
	}
	err := ipam.SaveMetaToDisk(ctx)
	if err != nil {
		logger.Error(ctx, "save meta error")
	}

	return markFailedIP, nil
}

// thread safe
func (ipam *IPAM) markIPUsedWithIPv6(ctx context.Context, userID string, eniID string, ips []string, ipv6s []string,
	podName string, podNameSpace string, podID string) ([]string, []string, error) {
	markFailedIP := make([]string, 0)
	markFailedIPv6 := make([]string, 0)
	if ipam.BciNode == nil {
		return markFailedIP, markFailedIPv6, errors.New("bci node is not initialized yet")
	}
	if _, ok := ipam.BciNode.Status.EniMultiIP.Used[userID]; !ok {
		if ipam.BciNode.Status.EniMultiIP.Used == nil {
			ipam.BciNode.Status.EniMultiIP.Used = networkingv1.AllocationMap{}
		}
		ipam.BciNode.Status.EniMultiIP.Used[userID] = networkingv1.UserAllocationEnis{}
	}
	allocEnis := ipam.BciNode.Status.EniMultiIP.Used[userID]
	if _, ok := allocEnis[eniID]; !ok {
		allocEnis[eniID] = &networkingv1.AllocationEni{}
	}

	allocEni := allocEnis[eniID]
	if allocEni.PrivateIPAddresses == nil {
		allocEni.PrivateIPAddresses = make(map[string]networkingv1.AllocationIP)
	}
	if allocEni.PrivateIPv6Addresses == nil {
		allocEni.PrivateIPv6Addresses = make(map[string]networkingv1.AllocationIP)
	}

	for _, ip := range ips {
		if _, ok := allocEni.PrivateIPAddresses[ip]; ok {
			markFailedIP = append(markFailedIP, ip)
			continue
		}
		allocEni.PrivateIPAddresses[ip] = networkingv1.AllocationIP{
			Owner:       fmt.Sprintf("%s/%s", podNameSpace, podName),
			UserID:      userID,
			EniID:       eniID,
			ContainerID: podID,
		}
	}

	for _, ipv6 := range ipv6s {
		if _, ok := allocEni.PrivateIPv6Addresses[ipv6]; ok {
			markFailedIPv6 = append(markFailedIPv6, ipv6)
			continue
		}
		allocEni.PrivateIPv6Addresses[ipv6] = networkingv1.AllocationIP{
			Owner:       fmt.Sprintf("%s/%s", podNameSpace, podName),
			UserID:      userID,
			EniID:       eniID,
			ContainerID: podID,
		}
	}

	if len(markFailedIP) != 0 {
		return markFailedIP, markFailedIPv6, errors.New(fmt.Sprintf("ip %v has USED", markFailedIP))
	}
	err := ipam.SaveMetaToDisk(ctx)
	if err != nil {
		logger.Error(ctx, "save meta error")
	}

	return markFailedIP, markFailedIPv6, nil
}

// thread safe
func (ipam *IPAM) markIPUnused(ctx context.Context, userID string, podName string, podNamespace string) error {
	ipam.lock.Lock()
	defer ipam.lock.Unlock()

	releasedIPNums := 0
	if ipam.BciNode == nil {
		return nil
	}

	podNameSpaceName := fmt.Sprintf("%s/%s", podNamespace, podName)
	eniID := ipam.getEniIDByPodNameSpaceName(podNameSpaceName)
	if eniID == "" {
		logger.Infof(ctx, "container id %v not found in used list", podNameSpaceName)
		return nil
	}

	if _, ok := ipam.BciNode.Status.EniMultiIP.Used[userID]; !ok {
		// should never happen
		logger.Errorf(ctx, "Error! userID: %s not in used list!", userID)
		return nil
	}
	if _, ok := ipam.BciNode.Status.EniMultiIP.Used[userID][eniID]; !ok {
		// should never happen
		logger.Errorf(ctx, "Error! userID: %s, eniID: %s not in used list!", userID, eniID)
		return nil
	}

	eniAllocate := ipam.BciNode.Status.EniMultiIP.Used[userID][eniID]
	for ip, ipAllocate := range eniAllocate.PrivateIPAddresses {
		if ipAllocate.Owner == podNameSpaceName {
			logger.Infof(ctx, "pod %s is releasing ip: %s", podNameSpaceName, ip)
			if _, ok := ipam.UserIdlePool[userID]; !ok {
				ipam.UserIdlePool[userID] = make(map[string]*queue.Queue)
			}
			if _, ok := ipam.UserIdlePool[userID][eniID]; !ok {
				ipam.UserIdlePool[userID][eniID] = queue.New()
			}
			ipam.UserIdlePool[userID][eniID].Append(PayLoad{
				address: ip,
				allocationIP: networkingv1.AllocationIP{
					UserID: userID,
					EniID:  eniID,
				},
				macAddress: ipam.BciNode.Spec.EniMultiIP.Pool[userID][eniID].MacAddress,
			})
			delete(eniAllocate.PrivateIPAddresses, ip)
			releasedIPNums++
		}
	}
	err := ipam.SaveMetaToDisk(ctx)
	if err != nil {
		logger.Errorf(ctx, "pod %s save metadate to disk error: %s", podNameSpaceName, err.Error())
	}

	if releasedIPNums == 0 {
		// already released
		logger.Errorf(ctx, "pod %s has released IP, ip info not found in used meta", podNameSpaceName)
	} else {
		logger.Infof(ctx, "pod %s released IP success, pod hold IP nums is %v, ip address is released", podNameSpaceName, releasedIPNums)
	}
	return nil
}

// thread safe
func (ipam *IPAM) markIPUnusedWithIPv6(ctx context.Context, userID string, podName string, podNamespace string) error {
	ipam.lock.Lock()
	defer ipam.lock.Unlock()

	releasedIPNums := 0
	releasedIPv6Nums := 0
	if ipam.BciNode == nil {
		return nil
	}

	podNameSpaceName := fmt.Sprintf("%s/%s", podNamespace, podName)
	eniID := ipam.getEniIDByPodNameSpaceName(podNameSpaceName)
	if eniID == "" {
		logger.Infof(ctx, "container id %v not found in used list", podNameSpaceName)
		return nil
	}

	if _, ok := ipam.BciNode.Status.EniMultiIP.Used[userID]; !ok {
		// should never happen
		logger.Errorf(ctx, "Error! userID: %s not in used list!", userID)
		return nil
	}
	if _, ok := ipam.BciNode.Status.EniMultiIP.Used[userID][eniID]; !ok {
		// should never happen
		logger.Errorf(ctx, "Error! userID: %s, eniID: %s not in used list!", userID, eniID)
		return nil
	}

	eniAllocate := ipam.BciNode.Status.EniMultiIP.Used[userID][eniID]
	for ip, ipAllocate := range eniAllocate.PrivateIPAddresses {
		if ipAllocate.Owner == podNameSpaceName {
			logger.Infof(ctx, "pod %s is releasing ip: %s", podNameSpaceName, ip)
			if _, ok := ipam.UserIdlePool[userID]; !ok {
				ipam.UserIdlePool[userID] = make(map[string]*queue.Queue)
			}
			if _, ok := ipam.UserIdlePool[userID][eniID]; !ok {
				ipam.UserIdlePool[userID][eniID] = queue.New()
			}
			ipam.UserIdlePool[userID][eniID].Append(PayLoad{
				address: ip,
				allocationIP: networkingv1.AllocationIP{
					UserID: userID,
					EniID:  eniID,
				},
				macAddress: ipam.BciNode.Spec.EniMultiIP.Pool[userID][eniID].MacAddress,
			})
			delete(eniAllocate.PrivateIPAddresses, ip)
			releasedIPNums++
		}
	}
	for ip, ipAllocate := range eniAllocate.PrivateIPv6Addresses {
		if ipAllocate.Owner == podNameSpaceName {
			logger.Infof(ctx, "pod %s is releasing ipv6: %s", podNameSpaceName, ip)
			if _, ok := ipam.IPv6UserIdlePool[userID]; !ok {
				ipam.IPv6UserIdlePool[userID] = make(map[string]*queue.Queue)
			}
			if _, ok := ipam.IPv6UserIdlePool[userID][eniID]; !ok {
				ipam.IPv6UserIdlePool[userID][eniID] = queue.New()
			}
			ipam.IPv6UserIdlePool[userID][eniID].Append(PayLoad{
				address: ip,
				allocationIP: networkingv1.AllocationIP{
					UserID: userID,
					EniID:  eniID,
				},
				macAddress: ipam.BciNode.Spec.EniMultiIP.Pool[userID][eniID].MacAddress,
			})
			delete(eniAllocate.PrivateIPv6Addresses, ip)
			releasedIPv6Nums++
		}
	}
	err := ipam.SaveMetaToDisk(ctx)
	if err != nil {
		logger.Errorf(ctx, "pod %s save metadate to disk error: %s", podNameSpaceName, err.Error())
	}

	if releasedIPNums == 0 && releasedIPv6Nums == 0 {
		// already released
		logger.Errorf(ctx, "pod %s has released IP, ip/ipv6 info not found in used meta", podNameSpaceName)
	} else {
		logger.Infof(ctx, "pod %s released IP success, pod hold IP nums is %v IPv6 nums is %v, ip/ipv6 address is released",
			podNameSpaceName, releasedIPNums, releasedIPv6Nums)
	}
	return nil
}

func (ipam *IPAM) popIPs(ctx context.Context, userID string, eniID string, needIPNums int) ([]PayLoad, error) {
	payLoads := make([]PayLoad, 0, needIPNums)

	if _, ok := ipam.UserIdlePool[userID]; !ok {
		ipam.UserIdlePool[userID] = make(map[string]*queue.Queue)
	}

	if _, ok := ipam.UserIdlePool[userID][eniID]; !ok {
		ipam.UserIdlePool[userID][eniID] = queue.New()
	}

	ipQ := ipam.UserIdlePool[userID][eniID]
	ipQLen := ipQ.LenWithFilter(ipam.usableIPFilter(userID, eniID))
	logger.Infof(ctx, "user: %s, eni: %s ip queue available length: %v, all items: %v", userID, eniID, ipQLen, ipQ.String())
	if ipQLen < needIPNums {
		if err := ipam.RequestPrivateIP(ctx, userID, eniID); err != nil {
			logger.Errorf(ctx, "inform node to add private ip for %v %v failed: %v", userID, eniID, err)
		} else {
			logger.Infof(ctx, "inform node to add private ip for %v %v done", userID, eniID)
		}
		return payLoads, fmt.Errorf("user: %s, eni: %s ip used out", userID, eniID)
	}
	for i := 0; i < needIPNums; i++ {
		payLoad := ipam.UserIdlePool[userID][eniID].Pop(ipam.usableIPFilter(userID, eniID)).(PayLoad)
		payLoads = append(payLoads, payLoad)
	}

	return payLoads, nil
}

func (ipam *IPAM) popIPsWithIPv6(ctx context.Context, userID string, eniID string, needIPNums int, needIPv6Nums int) ([]PayLoad, error) {
	payLoads := make([]PayLoad, 0, needIPNums)
	if _, ok := ipam.UserIdlePool[userID]; !ok {
		ipam.UserIdlePool[userID] = make(map[string]*queue.Queue)
	}

	if _, ok := ipam.UserIdlePool[userID][eniID]; !ok {
		ipam.UserIdlePool[userID][eniID] = queue.New()
	}

	if _, ok := ipam.IPv6UserIdlePool[userID]; !ok {
		ipam.IPv6UserIdlePool[userID] = make(map[string]*queue.Queue)
	}
	if _, ok := ipam.IPv6UserIdlePool[userID][eniID]; !ok {
		ipam.IPv6UserIdlePool[userID][eniID] = queue.New()
	}

	ipQ := ipam.UserIdlePool[userID][eniID]
	ipQLen := ipQ.LenWithFilter(ipam.usableIPFilter(userID, eniID))

	ipv6Q := ipam.IPv6UserIdlePool[userID][eniID]
	ipv6QLen := ipv6Q.LenWithFilter(ipam.usableIPv6Filter(userID, eniID))
	logger.Infof(ctx, "user: %s, eni: %s ip queue available length: %v, all items: %v ipv6 queue available length: %v, all ipv6 items: %v",
		userID, eniID, ipQLen, ipQ.String(), ipv6QLen, ipv6Q.String())

	if ipQLen < needIPNums || ipv6QLen < needIPv6Nums {
		if err := ipam.RequestPrivateIP(ctx, userID, eniID); err != nil {
			logger.Errorf(ctx, "inform node to add private ip for %v %v failed: %v", userID, eniID, err)
		} else {
			logger.Infof(ctx, "inform node to add private ip for %v %v done", userID, eniID)
		}
		return payLoads, fmt.Errorf("user: %s, eni: %s ip used out", userID, eniID)
	}
	for i := 0; i < needIPNums; i++ {
		payLoad := ipam.UserIdlePool[userID][eniID].Pop(ipam.usableIPFilter(userID, eniID)).(PayLoad)
		payLoads = append(payLoads, payLoad)
	}

	for i := 0; i < needIPv6Nums; i++ {
		payLoad := ipam.IPv6UserIdlePool[userID][eniID].Pop(ipam.usableIPv6Filter(userID, eniID)).(PayLoad)
		payLoads = append(payLoads, payLoad)
	}
	return payLoads, nil
}

func (ipam *IPAM) usableIPFilter(userID string, eniID string) func(v interface{}) bool {
	return func(v interface{}) bool {
		if v == nil {
			return false
		}
		payload, ok := v.(PayLoad)
		if !ok {
			return false
		}

		ips, ok := ipam.WaitReleaseIP[fmt.Sprintf("%s:%s", userID, eniID)]
		if !ok {
			return true
		}

		status, ok := ips[payload.address]
		if !ok {
			return true
		}

		if status == networkingv1.IPAMDoNotRelease || status == networkingv1.IPAMReadyForRelease {
			return false
		}

		return true
	}
}

// TODO:
// 1.大幅改造 queue，ready-for-release/do-not-release 的 IP 不能参与分配
// 2. controller 每次更新 spec，需要重建 queue
func (ipam *IPAM) getIPAndMarkUsed(ctx context.Context, userID string, podName string, podNamespace string, podID string, eniID string) ([]PayLoad, error) {
	ipam.lock.Lock()
	defer ipam.lock.Unlock()

	pod, err := ipam.getPodSpec(ctx, podName, podNamespace)
	if err != nil {
		logger.Errorf(ctx, "can not find pod info %s/%s, error: %v", podNamespace, podName, err)
		return nil, err
	}
	needIPNums := 1
	for key, value := range pod.GetAnnotations() {
		if key == podMultiEIPs {
			eips := strings.Split(value, ",")
			needIPNums = len(eips)
			if needIPNums == 0 {
				needIPNums = 1
			}
			break
		}
	}

	payLoads := make([]PayLoad, 0)
	ok := false
	payLoads, ok = ipam.checkPodIPsHasAllocated(ctx, userID, eniID, podName, podNamespace, needIPNums)
	if ok {
		logger.Infof(ctx, "find exsits pod %s/%s has allocated ips, requred ip nums is %v", podNamespace, podName, needIPNums)
		return payLoads, nil
	}

	retryCnt := 5
	for i := 0; i < retryCnt; i++ {
		payLoads, err = ipam.popIPs(ctx, userID, eniID, needIPNums)
		if err != nil {
			return nil, err
		}

		multiIPList := make([]string, 0)
		for _, payLoad := range payLoads {
			multiIPList = append(multiIPList, payLoad.address)
		}
		markFailedIPs := make([]string, 0)
		markFailedIPs, err = ipam.markIPUsed(ctx, userID, eniID, multiIPList, podName, podNamespace, podID)
		if err == nil {
			break
		} else {
			logger.Errorf(ctx, "mark allocated ip to used status error for pod %s/%s, error %s,", podNamespace, podName, err.Error())
			// 如果分配的ip已经标记为已使用，不要再次放回到idle队列
			// if mark err, put ip back, let cni retry
			for _, payLoad := range payLoads {
				requeue := true
				for _, abnormalIP := range markFailedIPs {
					if payLoad.address == abnormalIP {
						requeue = false
						break
					}
				}
				if requeue {
					ipam.UserIdlePool[userID][eniID].Append(payLoad)
				}
			}
			logger.Warningf(ctx, "mark the allocated ip to used status failed, try to reAllocated again, allocated ip is: %+v", payLoads)
			if i == retryCnt-1 {
				return payLoads, errors.New("marked ip to used status failed")
			}
			time.Sleep(500 * time.Millisecond)
		}
	}
	return payLoads, nil
}

func (ipam *IPAM) getIPAndMarkUsedWithIPv6(ctx context.Context, userID string, podName string, podNamespace string, podID string, eniID string) ([]PayLoad, error) {
	ipam.lock.Lock()
	defer ipam.lock.Unlock()

	pod, err := ipam.getPodSpec(ctx, podName, podNamespace)
	if err != nil {
		logger.Errorf(ctx, "can not find pod info %s/%s, error: %v", podNamespace, podName, err)
		return nil, err
	}
	var needIPv6Nums int
	needIPNums := 1
	for key, value := range pod.GetAnnotations() {
		if key == podMultiEIPs {
			eips := strings.Split(value, ",")
			needIPNums = len(eips)
			if needIPNums == 0 {
				needIPNums = 1
			}
			break
		}
	}
	if pod.Annotations[podEnableIPv6Annotation] == "true" {
		needIPv6Nums = 1
	}

	payLoads := make([]PayLoad, 0)
	ok := false
	payLoads, ok = ipam.checkPodIPsHasAllocatedWithIPv6(ctx, userID, eniID, podName, podNamespace, needIPNums, needIPv6Nums)
	if ok {
		logger.Infof(ctx, "find exsits pod %s/%s has allocated ips, requred ip nums is %v ipv6 nums is %v", podNamespace, podName, needIPNums, needIPv6Nums)
		return payLoads, nil
	}

	retryCnt := 5
	for i := 0; i < retryCnt; i++ {
		payLoads, err = ipam.popIPsWithIPv6(ctx, userID, eniID, needIPNums, needIPv6Nums)
		if err != nil {
			return nil, err
		}

		multiIPList := make([]string, 0)
		for _, payLoad := range payLoads {
			multiIPList = append(multiIPList, payLoad.address)
		}
		multiIPv4List := make([]string, 0)
		multiIPv6List := make([]string, 0)
		if needIPv6Nums == 1 {
			multiIPv4List = multiIPList[:len(multiIPList)-1]
			multiIPv6List = multiIPList[len(multiIPList)-1:]
		}
		markFailedIPs := make([]string, 0)
		markFailedIPv6s := make([]string, 0)
		markFailedIPs, markFailedIPv6s, err = ipam.markIPUsedWithIPv6(ctx, userID, eniID, multiIPv4List, multiIPv6List, podName, podNamespace, podID)
		if err == nil {
			break
		} else {
			logger.Errorf(ctx, "mark allocated ip to used status error for pod %s/%s, error %s,", podNamespace, podName, err.Error())
			// 如果分配的ip已经标记为已使用，不要再次放回到idle队列
			// if mark err, put ip back, let cni retry
			for _, payLoad := range payLoads {
				ipv4Requeue := true
				ipv6Requeue := true
				for _, abnormalIP := range markFailedIPs {
					if payLoad.address == abnormalIP {
						ipv4Requeue = false
						break
					}
				}
				for _, abnormalIPv6 := range markFailedIPv6s {
					if payLoad.address == abnormalIPv6 {
						ipv6Requeue = false
						break
					}
				}
				if ipv4Requeue {
					ipam.UserIdlePool[userID][eniID].Append(payLoad)
				}
				if ipv6Requeue {
					ipam.IPv6UserIdlePool[userID][eniID].Append(payLoad)
				}
			}
			logger.Warningf(ctx, "mark the allocated ip to used status failed, try to reAllocated again, allocated ip is: %+v", payLoads)
			if i == retryCnt-1 {
				return payLoads, errors.New("marked ip to used status failed")
			}
			time.Sleep(500 * time.Millisecond)
		}
	}
	return payLoads, nil
}

func (ipam *IPAM) AllocateIP(ctx context.Context, podName string, podNameSpace string, podID string) (*rpc.ENIMultiIPReply, error) {
	ret := rpc.ENIMultiIPReply{}
	userID := podNameSpace

	isAdminPod, err := ipam.isAdminPod(ctx, podNameSpace, podName)
	if err != nil {
		logger.Errorf(ctx, err.Error())
		return &ret, err
	}
	isIPv6Pod, err := ipam.isIPv6Pod(ctx, podNameSpace, podName)
	if err != nil {
		logger.Errorf(ctx, err.Error())
		return &ret, err
	}
	if isAdminPod {
		userID = ADMINUSERNAME
	}

	logger.Infof(ctx, "ipam find user: %s by pod: %s, namespace: %s", userID, podName, podNameSpace)
	subnetID, secIDs, err := ipam.getPodSubnetIDAndSecIDs(ctx, userID, podName, podNameSpace)
	if err != nil {
		logger.Infof(ctx, "get pod subnet and sec error: %w", err)
		return &ret, err
	}

	// 检查node上是否存在符合pod要求的用户eni
	// if bcinode spec have, ippool should also have
	eniID, err := ipam.getEniIDBySubnetAndSecIDsFromSpec(ctx, userID, subnetID, secIDs)
	if err != nil {
		logger.Infof(ctx, "ENI not found, going to send create request, userID: %s, err: %v", userID, err)
		err := ipam.RequestEni(ctx, userID, podName, podNameSpace)
		if err != nil {
			return &ret, fmt.Errorf("eNI not found, annotate node failed, pod: %v, user: %v, err: %w", podName, userID, err)
		}
		// 尝试等待eni创建完毕，最长等待40s
		eniID, err = ipam.tryWaitEni(ctx, userID, subnetID, secIDs)
		if err != nil {
			logger.Errorf(ctx, "wait eni create failed %s/%s", userID, podName)
			return &ret, errors.New("user eni not found, wait create eni error")
		}
		logger.Infof(ctx, "wait eni create success")
	}
	// here eni may not ready on node. let cni retry if we can not get ip by eniid
	payLoads, err := ipam.getIPAndMarkUsedWithIPv6(ctx, userID, podName, podNameSpace, podID, eniID)
	if err != nil || len(payLoads) == 0 {
		return &ret, err
	}
	multiIPStr := ""
	ipv6Address := ""
	if len(payLoads) == 1 {
		payLoad := payLoads[0]
		ret.IP = payLoad.address
		ret.Mac = payLoad.macAddress
		ret.Gateway = "***************"
		multiIPStr = payLoad.address
		logger.Infof(ctx, "alloc ip for pod %s/%s success, ip is %s", podNameSpace, podName, payLoad.address)
		ret.EniNetns = fmt.Sprintf("/var/run/netns/enins-%s", payLoad.allocationIP.EniID)
	} else {
		payLoad := payLoads[0]
		ret.IP = payLoad.address
		ret.Mac = payLoad.macAddress
		ret.Gateway = "***************"
		ret.EniNetns = fmt.Sprintf("/var/run/netns/enins-%s", payLoad.allocationIP.EniID)
		multiIPs := make([]string, 0)
		ipv4Nums := len(payLoads)
		if isIPv6Pod {
			ipv4Nums = ipv4Nums - 1
			ipv6Address = payLoads[ipv4Nums].address
		}
		for i := 0; i < ipv4Nums; i++ {
			multiIPs = append(multiIPs, strings.TrimSpace(payLoads[i].address))
		}
		ret.MultiIPs = multiIPs
		ret.IPv6IP = ipv6Address
		multiIPStr = strings.Join(multiIPs, ",")
		logger.Infof(ctx, "alloc multi ips for pod %s/%s success, ips is %+v", podNameSpace, podName, multiIPs)
	}

	if isAdminPod || RequireUniqueRouteTable {
		ret.EniNetns = fmt.Sprintf("/proc/1/ns/net")
	}

	err = ipam.PatchBciInternalIPWithIPv6(ctx, podNameSpace, podName, payLoads[0].address, multiIPStr, ipv6Address, eniID, false)
	if err != nil {
		return &ret, err
	}

	// TODO: not sure how to handle netlink error
	if RequireUniqueRouteTable {
		ret.EniRequireUniqueRouteTable = true
		intf, err := enilink.FindEniLinkByHWAddress(ipam.Nlink, ret.Mac)
		if err != nil {
			logger.Errorf(ctx, "find allocated result eni link error: %v", err)
			return &ret, err
		}
		suffix, err := enilink.GetEniLinkNameSuffix(intf)
		if err != nil {
			logger.Errorf(ctx, "find allocated eni result link suffix error: %v", err)
			return &ret, err
		}

		ret.EniRouteTable = enilink.EniRouteTableBase + int32(suffix)
	}

	// Update custom resource to reflect the newly allocated IP.
	ipam.RefreshTrigger.TriggerWithReason(fmt.Sprintf("allocation IP of %s", podName))
	return &ret, nil
}

func (ipam *IPAM) ReleaseIP(ctx context.Context, podName string, podNameSpace string, podID string) error {
	podNameSpaceName := fmt.Sprintf("%s/%s", podNameSpace, podName)
	userID, released := ipam.isPodReleased(ctx, podNameSpaceName)
	if released {
		logger.Infof(ctx, "pod has been released %v", podNameSpaceName)
		return nil
	}

	err := ipam.markIPUnusedWithIPv6(ctx, userID, podName, podNameSpace)
	if err != nil {
		logger.Errorf(ctx, "Release IP error: %v", err)
		return nil
	}

	err = ipam.PatchBciInternalIPWithIPv6(ctx, podNameSpace, podName, "", "", "", "", true)
	if err != nil {
		logger.Warning(ctx, err)
	}
	// Update custom resource to reflect the newly released IP.
	ipam.RefreshTrigger.TriggerWithReason(fmt.Sprintf("release IP of %s", podName))
	return nil
}

// func (ipam *IPAM) CheckIP() error {

// }

func (ipam *IPAM) Lock() {
	ipam.lock.Lock()
}

func (ipam *IPAM) Unlock() {
	ipam.lock.Unlock()
}

// isIPv6Allocated checks if an IPv6 address is already allocated
func (ipam *IPAM) isIPv6Allocated(userID, eniID, ip string) bool {
	if ipam.BciNode == nil {
		return false
	}

	if _, ok := ipam.BciNode.Status.EniMultiIP.Used[userID]; !ok {
		return false
	}

	if _, ok := ipam.BciNode.Status.EniMultiIP.Used[userID][eniID]; !ok {
		return false
	}

	_, ok := ipam.BciNode.Status.EniMultiIP.Used[userID][eniID].PrivateIPv6Addresses[ip]
	return ok
}

// usableIPv6Filter returns a filter function for IPv6 addresses
func (ipam *IPAM) usableIPv6Filter(userID string, eniID string) func(v interface{}) bool {
	return func(v interface{}) bool {
		if v == nil {
			return false
		}
		payload, ok := v.(PayLoad)
		if !ok {
			return false
		}

		ips, ok := ipam.WaitReleaseIP[fmt.Sprintf("%s:%s", userID, eniID)]
		if !ok {
			return true
		}

		status, ok := ips[payload.address]
		if !ok {
			return true
		}

		if status == networkingv1.IPAMDoNotRelease || status == networkingv1.IPAMReadyForRelease {
			return false
		}

		return true
	}
}
