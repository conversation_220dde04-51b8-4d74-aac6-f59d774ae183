/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package options

import (
	"flag"
	"time"

	"github.com/spf13/pflag"
	cliflag "k8s.io/component-base/cli/flag"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/logger"
)

type Options struct {
	MetricsAddr              string
	ProbeAddr                string
	CustomResourceUpdateRate time.Duration
}

// TODO: validate options
func (o *Options) validate() error {
	return nil
}

func ParseArgs() *Options {
	var (
		o Options
	)

	pflag.CommandLine.SetNormalizeFunc(cliflag.WordSepNormalizeFunc)
	pflag.CommandLine.AddGoFlagSet(flag.CommandLine)

	flag.StringVar(&o.MetricsAddr, "metrics-bind-address", ":8080", "The address the metric endpoint binds to.")
	flag.StringVar(&o.ProbeAddr, "health-probe-bind-address", ":8081", "The address the probe endpoint binds to.")
	flag.DurationVar(&o.CustomResourceUpdateRate, "custom-resource-update-rate", time.Second*15, "The rate the bcinode cr is updated")

	logger.InitFlags(flag.CommandLine)

	flag.Parse()

	return &o
}
