"""
test
"""

import os
import subprocess
import random
import sys

def run(cmd):
    """
    execute command
    """
    p = subprocess.Popen(cmd,
            stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True)
    stdout = ""
    while p.poll() is None:
        stdout += p.stdout.read()
    rc = p.wait()
    return rc, stdout

podNamespace = sys.argv[1]
podNmae = sys.argv[2]

value = random.randint(0, 20)
while True:
    if value < 10:
        stdout = run("./test alloc %s %s" % (podNamespace, podNmae))
        print("alloc: %s", stdout)
    if value > 10:
        stdout = run("./test release %s %s" % (podNamespace, podNmae))
        print("release: %s", stdout)