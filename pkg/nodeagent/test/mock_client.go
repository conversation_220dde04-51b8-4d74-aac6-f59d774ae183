package main

import (
	"context"
	"fmt"
	"net"
	"os"
	"time"

	pb "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/rpc"

	"google.golang.org/grpc"
)

const (
	GrpcSocketFilePath = "/var/run/bci-cni/bci-cni.socket"
	protocol           = "unix"
)

func newClient() *grpc.ClientConn {
	dialer := func(addr string, t time.Duration) (net.Conn, error) {
		return net.Dial(protocol, addr)
	}
	conn, err := grpc.Dial(GrpcSocketFilePath, grpc.WithInsecure(), grpc.WithDialer(dialer))
	if err != nil {
		fmt.Printf("failed to connect: %v", err)
	}

	return conn
}

func main() {
	conn := newClient()
	defer conn.Close()
	client := pb.NewCniBackendClient(conn)

	options := os.Args

	action := options[1]
	podNamespace := options[2]
	podName := options[3]

	if action == "alloc" {
		ret, err := client.AllocateIP(context.Background(), &pb.Request{
			K8SPodName:             podName,
			K8SPodNamespace:        podNamespace,
			K8SPodInfraContainerID: "",
		})
		if err != nil {
			fmt.Printf("failed to get book list: %v", err)
		}
		fmt.Printf("book list: %v", ret)
	}

	if action == "release" {
		ret, err := client.ReleaseIP(context.Background(), &pb.Request{
			K8SPodName:             podName,
			K8SPodNamespace:        podNamespace,
			K8SPodInfraContainerID: "",
		})
		if err != nil {
			fmt.Printf("failed to get book list: %v", err)
		}
		fmt.Printf("book list: %v", ret)
	}
}
