/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package queue

import (
	"container/list"
	"log"
	"testing"
)

func ensureEmpty(t *testing.T, q *Queue) {
	if l := q.Len(); l != 0 {
		t.Errorf("q.Len() = %d, want %d", l, 0)
	}
	if e := q.Front(); e != nil {
		t.Errorf("q.Front() = %v, want %v", e, nil)
	}
	if e := q.Back(); e != nil {
		t.Errorf("q.Back() = %v, want %v", e, nil)
	}
	if e := q.PopFront(); e != nil {
		t.Errorf("q.PopFront() = %v, want %v", e, nil)
	}
	if e := q.PopBack(); e != nil {
		t.<PERSON>rf("q.PopBack() = %v, want %v", e, nil)
	}
}

func TestNew(t *testing.T) {
	q := New()
	ensureEmpty(t, q)
}

func ensureSingleton(t *testing.T, q *Queue) {
	if l := q.Len(); l != 1 {
		t.Errorf("q.Len() = %d, want %d", l, 1)
	}
	if e := q.Front(); e != 42 {
		t.Errorf("q.Front() = %v, want %v", e, 42)
	}
	if e := q.Back(); e != 42 {
		t.Errorf("q.Back() = %v, want %v", e, 42)
	}
}

func TestSingleton(t *testing.T) {
	q := New()
	ensureEmpty(t, q)
	q.PushFront(42)
	ensureSingleton(t, q)
	q.PopFront()
	ensureEmpty(t, q)
	q.PushBack(42)
	ensureSingleton(t, q)
	q.PopBack()
	ensureEmpty(t, q)
	q.PushFront(42)
	ensureSingleton(t, q)
	q.PopBack()
	ensureEmpty(t, q)
	q.PushBack(42)
	ensureSingleton(t, q)
	q.PopFront()
	ensureEmpty(t, q)
}

func TestDuos(t *testing.T) {
	q := New()
	ensureEmpty(t, q)
	q.PushFront(42)
	ensureSingleton(t, q)
	q.PushBack(43)
	if l := q.Len(); l != 2 {
		t.Errorf("q.Len() = %d, want %d", l, 2)
	}
	if e := q.Front(); e != 42 {
		t.Errorf("q.Front() = %v, want %v", e, 42)
	}
	if e := q.Back(); e != 43 {
		t.Errorf("q.Back() = %v, want %v", e, 43)
	}
}

func ensureLength(t *testing.T, q *Queue, len int) {
	if l := q.Len(); l != len {
		t.Errorf("q.Len() = %d, want %d", l, len)
	}
}

func TestZeroValue(t *testing.T) {
	var q Queue = *New()
	q.PushFront(1)
	ensureLength(t, &q, 1)
	q.PushFront(2)
	ensureLength(t, &q, 2)
	q.PushFront(3)
	ensureLength(t, &q, 3)
	q.PushFront(4)
	ensureLength(t, &q, 4)
	q.PushFront(5)
	ensureLength(t, &q, 5)
	q.PushBack(6)
	ensureLength(t, &q, 6)
	q.PushBack(7)
	ensureLength(t, &q, 7)
	q.PushBack(8)
	ensureLength(t, &q, 8)
	q.PushBack(9)
	ensureLength(t, &q, 9)
	const want = "[5 4 3 2 1 6 7 8 9]"
	if s := q.String(); s != want {
		t.Errorf("q.String() = %s, want %s", s, want)
	}
}

func TestGrowShrink1(t *testing.T) {
	var q Queue = *New()
	for i := 0; i < size; i++ {
		q.PushBack(i)
		ensureLength(t, &q, i+1)
	}
	for i := 0; q.Len() > 0; i++ {
		x := q.PopFront().(int)
		if x != i {
			t.Errorf("q.PopFront() = %d, want %d", x, i)
		}
		ensureLength(t, &q, size-i-1)
	}
}
func TestGrowShrink2(t *testing.T) {
	var q Queue = *New()
	for i := 0; i < size; i++ {
		q.PushFront(i)
		ensureLength(t, &q, i+1)
	}
	for i := 0; q.Len() > 0; i++ {
		x := q.PopBack().(int)
		if x != i {
			t.Errorf("q.PopBack() = %d, want %d", x, i)
		}
		ensureLength(t, &q, size-i-1)
	}
}

const size = 1024

func BenchmarkPushFrontQueue(b *testing.B) {
	for i := 0; i < b.N; i++ {
		var q Queue
		for n := 0; n < size; n++ {
			q.PushFront(n)
		}
	}
}
func BenchmarkPushFrontList(b *testing.B) {
	for i := 0; i < b.N; i++ {
		var q list.List
		for n := 0; n < size; n++ {
			q.PushFront(n)
		}
	}
}

func BenchmarkPushBackQueue(b *testing.B) {
	for i := 0; i < b.N; i++ {
		var q Queue
		for n := 0; n < size; n++ {
			q.PushBack(n)
		}
	}
}

func TestLenWithFilter(t *testing.T) {
	q := New()
	ensureEmpty(t, q)
	q.PushFront(42)
	q.PushFront(42)
	q.PushFront(42)

	len := q.LenWithFilter(func(v interface{}) bool {
		return v.(int) == 42
	})

	if len != 3 {
		t.Error("TestLenWithFilter")
	}

	q.PushFront(42)
	q.PushFront(42)
	q.PushFront(43)

	len = q.LenWithFilter(func(v interface{}) bool {
		return v.(int) != 42
	})
	if len != 1 {
		t.Error("TestLenWithFilter")
	}
}

func TestPopWithFilter1(t *testing.T) {
	q := New()
	ensureEmpty(t, q)
	q.PushBack(41)
	q.PushBack(42)
	q.PushBack(43)

	log.Println(q)

	v := q.Pop(func(v interface{}) bool {
		return v.(int) == 42
	})

	log.Println(q)

	if v != 42 {
		t.Error("TestPopWithFilter1")
	}
}

func TestPopWithFilter2(t *testing.T) {
	q := New()
	ensureEmpty(t, q)
	q.PushBack(41)
	q.PushBack(42)
	q.PushBack(43)

	log.Println(q)

	v := q.Pop(func(v interface{}) bool {
		return v.(int) > 40
	})

	log.Println(q)

	if v != 41 {
		t.Error("TestPopWithFilter2")
	}
}

func TestPopWithFilter3(t *testing.T) {
	q := New()
	ensureEmpty(t, q)
	q.Append(41)
	q.Append(42)
	q.Append(43)

	log.Println(q)

	v := q.Pop(func(v interface{}) bool {
		return v.(int) > 50
	})

	log.Println(q)

	if v != nil {
		t.Error("TestPopWithFilter3")
	}
}

func TestPopWithFilter4(t *testing.T) {
	q := New()
	ensureEmpty(t, q)

	log.Println(q)

	v := q.Pop(func(v interface{}) bool {
		return true
	})

	log.Println(q)

	if v != nil {
		t.Error("TestPopWithFilter4")
	}
}
