/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
// Code generated by client-gen. DO NOT EDIT.

package v1

import (
	"context"
	"time"

	v1 "icode.baidu.com/baidu/bci2/bci-cni-driver/apis/networking/v1"
	scheme "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/generated/clientset/versioned/scheme"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	rest "k8s.io/client-go/rest"
)

// BciNodesGetter has a method to return a BciNodeInterface.
// A group's client should implement this interface.
type BciNodesGetter interface {
	BciNodes() BciNodeInterface
}

// BciNodeInterface has methods to work with BciNode resources.
type BciNodeInterface interface {
	Create(ctx context.Context, bciNode *v1.BciNode, opts metav1.CreateOptions) (*v1.BciNode, error)
	Update(ctx context.Context, bciNode *v1.BciNode, opts metav1.UpdateOptions) (*v1.BciNode, error)
	UpdateStatus(ctx context.Context, bciNode *v1.BciNode, opts metav1.UpdateOptions) (*v1.BciNode, error)
	Delete(ctx context.Context, name string, opts metav1.DeleteOptions) error
	DeleteCollection(ctx context.Context, opts metav1.DeleteOptions, listOpts metav1.ListOptions) error
	Get(ctx context.Context, name string, opts metav1.GetOptions) (*v1.BciNode, error)
	List(ctx context.Context, opts metav1.ListOptions) (*v1.BciNodeList, error)
	Watch(ctx context.Context, opts metav1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts metav1.PatchOptions, subresources ...string) (result *v1.BciNode, err error)
	BciNodeExpansion
}

// bciNodes implements BciNodeInterface
type bciNodes struct {
	client rest.Interface
}

// newBciNodes returns a BciNodes
func newBciNodes(c *NetworkingV1Client) *bciNodes {
	return &bciNodes{
		client: c.RESTClient(),
	}
}

// Get takes name of the bciNode, and returns the corresponding bciNode object, and an error if there is any.
func (c *bciNodes) Get(ctx context.Context, name string, options metav1.GetOptions) (result *v1.BciNode, err error) {
	result = &v1.BciNode{}
	err = c.client.Get().
		Resource("bcinodes").
		Name(name).
		VersionedParams(&options, scheme.ParameterCodec).
		Do(ctx).
		Into(result)
	return
}

// List takes label and field selectors, and returns the list of BciNodes that match those selectors.
func (c *bciNodes) List(ctx context.Context, opts metav1.ListOptions) (result *v1.BciNodeList, err error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	result = &v1.BciNodeList{}
	err = c.client.Get().
		Resource("bcinodes").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Do(ctx).
		Into(result)
	return
}

// Watch returns a watch.Interface that watches the requested bciNodes.
func (c *bciNodes) Watch(ctx context.Context, opts metav1.ListOptions) (watch.Interface, error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	opts.Watch = true
	return c.client.Get().
		Resource("bcinodes").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Watch(ctx)
}

// Create takes the representation of a bciNode and creates it.  Returns the server's representation of the bciNode, and an error, if there is any.
func (c *bciNodes) Create(ctx context.Context, bciNode *v1.BciNode, opts metav1.CreateOptions) (result *v1.BciNode, err error) {
	result = &v1.BciNode{}
	err = c.client.Post().
		Resource("bcinodes").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(bciNode).
		Do(ctx).
		Into(result)
	return
}

// Update takes the representation of a bciNode and updates it. Returns the server's representation of the bciNode, and an error, if there is any.
func (c *bciNodes) Update(ctx context.Context, bciNode *v1.BciNode, opts metav1.UpdateOptions) (result *v1.BciNode, err error) {
	result = &v1.BciNode{}
	err = c.client.Put().
		Resource("bcinodes").
		Name(bciNode.Name).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(bciNode).
		Do(ctx).
		Into(result)
	return
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *bciNodes) UpdateStatus(ctx context.Context, bciNode *v1.BciNode, opts metav1.UpdateOptions) (result *v1.BciNode, err error) {
	result = &v1.BciNode{}
	err = c.client.Put().
		Resource("bcinodes").
		Name(bciNode.Name).
		SubResource("status").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(bciNode).
		Do(ctx).
		Into(result)
	return
}

// Delete takes name of the bciNode and deletes it. Returns an error if one occurs.
func (c *bciNodes) Delete(ctx context.Context, name string, opts metav1.DeleteOptions) error {
	return c.client.Delete().
		Resource("bcinodes").
		Name(name).
		Body(&opts).
		Do(ctx).
		Error()
}

// DeleteCollection deletes a collection of objects.
func (c *bciNodes) DeleteCollection(ctx context.Context, opts metav1.DeleteOptions, listOpts metav1.ListOptions) error {
	var timeout time.Duration
	if listOpts.TimeoutSeconds != nil {
		timeout = time.Duration(*listOpts.TimeoutSeconds) * time.Second
	}
	return c.client.Delete().
		Resource("bcinodes").
		VersionedParams(&listOpts, scheme.ParameterCodec).
		Timeout(timeout).
		Body(&opts).
		Do(ctx).
		Error()
}

// Patch applies the patch and returns the patched bciNode.
func (c *bciNodes) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts metav1.PatchOptions, subresources ...string) (result *v1.BciNode, err error) {
	result = &v1.BciNode{}
	err = c.client.Patch(pt).
		Resource("bcinodes").
		Name(name).
		SubResource(subresources...).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(data).
		Do(ctx).
		Into(result)
	return
}
