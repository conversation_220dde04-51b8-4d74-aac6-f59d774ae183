/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"

	networkingv1 "icode.baidu.com/baidu/bci2/bci-cni-driver/apis/networking/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeBciNodes implements BciNodeInterface
type FakeBciNodes struct {
	Fake *FakeNetworkingV1
}

var bcinodesResource = schema.GroupVersionResource{Group: "networking.bci.cloud.baidu.com", Version: "v1", Resource: "bcinodes"}

var bcinodesKind = schema.GroupVersionKind{Group: "networking.bci.cloud.baidu.com", Version: "v1", Kind: "BciNode"}

// Get takes name of the bciNode, and returns the corresponding bciNode object, and an error if there is any.
func (c *FakeBciNodes) Get(ctx context.Context, name string, options v1.GetOptions) (result *networkingv1.BciNode, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootGetAction(bcinodesResource, name), &networkingv1.BciNode{})
	if obj == nil {
		return nil, err
	}
	return obj.(*networkingv1.BciNode), err
}

// List takes label and field selectors, and returns the list of BciNodes that match those selectors.
func (c *FakeBciNodes) List(ctx context.Context, opts v1.ListOptions) (result *networkingv1.BciNodeList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootListAction(bcinodesResource, bcinodesKind, opts), &networkingv1.BciNodeList{})
	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &networkingv1.BciNodeList{ListMeta: obj.(*networkingv1.BciNodeList).ListMeta}
	for _, item := range obj.(*networkingv1.BciNodeList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested bciNodes.
func (c *FakeBciNodes) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewRootWatchAction(bcinodesResource, opts))
}

// Create takes the representation of a bciNode and creates it.  Returns the server's representation of the bciNode, and an error, if there is any.
func (c *FakeBciNodes) Create(ctx context.Context, bciNode *networkingv1.BciNode, opts v1.CreateOptions) (result *networkingv1.BciNode, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootCreateAction(bcinodesResource, bciNode), &networkingv1.BciNode{})
	if obj == nil {
		return nil, err
	}
	return obj.(*networkingv1.BciNode), err
}

// Update takes the representation of a bciNode and updates it. Returns the server's representation of the bciNode, and an error, if there is any.
func (c *FakeBciNodes) Update(ctx context.Context, bciNode *networkingv1.BciNode, opts v1.UpdateOptions) (result *networkingv1.BciNode, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateAction(bcinodesResource, bciNode), &networkingv1.BciNode{})
	if obj == nil {
		return nil, err
	}
	return obj.(*networkingv1.BciNode), err
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *FakeBciNodes) UpdateStatus(ctx context.Context, bciNode *networkingv1.BciNode, opts v1.UpdateOptions) (*networkingv1.BciNode, error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootUpdateSubresourceAction(bcinodesResource, "status", bciNode), &networkingv1.BciNode{})
	if obj == nil {
		return nil, err
	}
	return obj.(*networkingv1.BciNode), err
}

// Delete takes name of the bciNode and deletes it. Returns an error if one occurs.
func (c *FakeBciNodes) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewRootDeleteAction(bcinodesResource, name), &networkingv1.BciNode{})
	return err
}

// DeleteCollection deletes a collection of objects.
func (c *FakeBciNodes) DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error {
	action := testing.NewRootDeleteCollectionAction(bcinodesResource, listOpts)

	_, err := c.Fake.Invokes(action, &networkingv1.BciNodeList{})
	return err
}

// Patch applies the patch and returns the patched bciNode.
func (c *FakeBciNodes) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *networkingv1.BciNode, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewRootPatchSubresourceAction(bcinodesResource, name, pt, data, subresources...), &networkingv1.BciNode{})
	if obj == nil {
		return nil, err
	}
	return obj.(*networkingv1.BciNode), err
}
