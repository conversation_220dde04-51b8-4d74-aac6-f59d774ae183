/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
// Code generated by lister-gen. DO NOT EDIT.

package v1

import (
	v1 "icode.baidu.com/baidu/bci2/bci-cni-driver/apis/networking/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/tools/cache"
)

// BciNodeLister helps list BciNodes.
// All objects returned here must be treated as read-only.
type BciNodeLister interface {
	// List lists all BciNodes in the indexer.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*v1.BciNode, err error)
	// Get retrieves the BciNode from the index for a given name.
	// Objects returned here must be treated as read-only.
	Get(name string) (*v1.BciNode, error)
	BciNodeListerExpansion
}

// bciNodeLister implements the BciNodeLister interface.
type bciNodeLister struct {
	indexer cache.Indexer
}

// NewBciNodeLister returns a new BciNodeLister.
func NewBciNodeLister(indexer cache.Indexer) BciNodeLister {
	return &bciNodeLister{indexer: indexer}
}

// List lists all BciNodes in the indexer.
func (s *bciNodeLister) List(selector labels.Selector) (ret []*v1.BciNode, err error) {
	err = cache.ListAll(s.indexer, selector, func(m interface{}) {
		ret = append(ret, m.(*v1.BciNode))
	})
	return ret, err
}

// Get retrieves the BciNode from the index for a given name.
func (s *bciNodeLister) Get(name string) (*v1.BciNode, error) {
	obj, exists, err := s.indexer.GetByKey(name)
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, errors.NewNotFound(v1.Resource("bcinode"), name)
	}
	return obj.(*v1.BciNode), nil
}
