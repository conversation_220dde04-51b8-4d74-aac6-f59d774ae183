/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
// Code generated by informer-gen. DO NOT EDIT.

package v1

import (
	"context"
	time "time"

	networkingv1 "icode.baidu.com/baidu/bci2/bci-cni-driver/apis/networking/v1"
	versioned "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/generated/clientset/versioned"
	internalinterfaces "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/generated/informers/externalversions/internalinterfaces"
	v1 "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/generated/listers/networking/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	runtime "k8s.io/apimachinery/pkg/runtime"
	watch "k8s.io/apimachinery/pkg/watch"
	cache "k8s.io/client-go/tools/cache"
)

// BciNodeInformer provides access to a shared informer and lister for
// BciNodes.
type BciNodeInformer interface {
	Informer() cache.SharedIndexInformer
	Lister() v1.BciNodeLister
}

type bciNodeInformer struct {
	factory          internalinterfaces.SharedInformerFactory
	tweakListOptions internalinterfaces.TweakListOptionsFunc
}

// NewBciNodeInformer constructs a new informer for BciNode type.
// Always prefer using an informer factory to get a shared informer instead of getting an independent
// one. This reduces memory footprint and number of connections to the server.
func NewBciNodeInformer(client versioned.Interface, resyncPeriod time.Duration, indexers cache.Indexers) cache.SharedIndexInformer {
	return NewFilteredBciNodeInformer(client, resyncPeriod, indexers, nil)
}

// NewFilteredBciNodeInformer constructs a new informer for BciNode type.
// Always prefer using an informer factory to get a shared informer instead of getting an independent
// one. This reduces memory footprint and number of connections to the server.
func NewFilteredBciNodeInformer(client versioned.Interface, resyncPeriod time.Duration, indexers cache.Indexers, tweakListOptions internalinterfaces.TweakListOptionsFunc) cache.SharedIndexInformer {
	return cache.NewSharedIndexInformer(
		&cache.ListWatch{
			ListFunc: func(options metav1.ListOptions) (runtime.Object, error) {
				if tweakListOptions != nil {
					tweakListOptions(&options)
				}
				return client.NetworkingV1().BciNodes().List(context.TODO(), options)
			},
			WatchFunc: func(options metav1.ListOptions) (watch.Interface, error) {
				if tweakListOptions != nil {
					tweakListOptions(&options)
				}
				return client.NetworkingV1().BciNodes().Watch(context.TODO(), options)
			},
		},
		&networkingv1.BciNode{},
		resyncPeriod,
		indexers,
	)
}

func (f *bciNodeInformer) defaultInformer(client versioned.Interface, resyncPeriod time.Duration) cache.SharedIndexInformer {
	return NewFilteredBciNodeInformer(client, resyncPeriod, cache.Indexers{cache.NamespaceIndex: cache.MetaNamespaceIndexFunc}, f.tweakListOptions)
}

func (f *bciNodeInformer) Informer() cache.SharedIndexInformer {
	return f.factory.InformerFor(&networkingv1.BciNode{}, f.defaultInformer)
}

func (f *bciNodeInformer) Lister() v1.BciNodeLister {
	return v1.NewBciNodeLister(f.Informer().GetIndexer())
}
