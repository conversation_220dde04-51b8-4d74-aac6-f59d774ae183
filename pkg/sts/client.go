// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2018/12/25 19:30:00, by <EMAIL>, create
*/
/*
DESCRIPTION
定义 STS NewSignOption 实现
*/

package sts

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/baidubce/bce-sdk-go/services/bcc/api"
	"github.com/karlseguin/ccache"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
	bcests "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/sts"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/logger"
)

var (
	_ Interface = &Client{}

	// cache 缓存 &bcests.Credential, 参考：https://github.com/karlseguin/ccache
	cache = ccache.New(ccache.Configure().MaxSize(5000).ItemsToPrune(500).GetsPerPromote(10))
)

var Endpoints = map[string]string{
	"bj":      "sts.bj.iam.sdns.baidu.com:8586/v1",
	"gz":      "sts.gz.iam.sdns.baidu.com:8586/v1",
	"su":      "sts.su.iam.sdns.baidu.com:8586/v1",
	"nj":      "sts.nj.bce.baidu-int.com/v1",
	"hkg":     "sts.hkg.bce.baidu-int.com:8586/v1",
	"fwh":     "sts.fwh.bce.baidu-int.com:8586/v1",
	"bd":      "sts.bdbl.bce.baidu-int.com:8586/v1",
	"sandbox": "sts.bj.internal-qasandbox.baidu-int.com:8586/v1",
}

// Client 是 sts.Interface 实现
type Client struct {
	stsclient bcests.Interface
}

// NewClient 初始化 sts.Client
func NewClient(ctx context.Context, stsConfig *bce.Config, iamConfig *bce.Config, roleName, serviceName, servicePassword string) *Client {
	stsclient := bcests.NewClient(ctx, stsConfig, iamConfig, roleName, serviceName, servicePassword)

	return &Client{
		stsclient: stsclient,
	}
}

// SetDebug - 是否开启调试
func (c *Client) SetDebug(debug bool) {
	c.stsclient.SetDebug(debug)
}

// NewSignOption 代签名
func (c *Client) NewSignOption(ctx context.Context, accountID string) *bce.SignOption {
	// 获取 Credential
	cred, err := c.GetCredential(ctx, accountID)
	if err != nil {
		logger.Errorf(ctx, "GetCredential failed: %v", err)
		return nil
	}

	if cred == nil {
		logger.Errorf(ctx, "GetCredential failed: cred is nil")
		return nil
	}

	// 构建 SignOption
	option := &bce.SignOption{}

	option.AddHeader("X-Bce-Request-Id", logger.GetUUID())
	option.AddHeader("X-Auth-Token", cred.Token.ID)
	option.AddHeader("X-Bce-Security-Token", cred.SessionToken)
	option.Credentials = bce.NewCredentials(cred.AccessKeyID, cred.SecretAccessKey)

	option.AddHeadersToSign("host")

	return option
}

// 资源账号计费方式, 每个服务的方案不同，这个方法是给BLB使用
// BLB: https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/KzHUM_sAtc/8YYDzrTwHn/MlmvryIiNJq4Hu
func (c *Client) NewSignOptionWithResourceHeader(ctx context.Context, userID, hexKey, resourceAccountID, resourceSource string) *bce.SignOption {
	// 获取 Credential
	cred, err := c.GetCredential(ctx, userID)
	if err != nil {
		logger.Errorf(ctx, "GetCredential failed: %v", err)
		return nil
	}

	if cred == nil {
		logger.Errorf(ctx, "GetCredential failed: cred is nil")
		return nil
	}

	// 构建 SignOption
	option := &bce.SignOption{}

	option.AddHeader("X-Bce-Request-Id", logger.GetUUID())
	option.AddHeader("X-Auth-Token", cred.Token.ID)
	option.AddHeader("X-Bce-Security-Token", cred.SessionToken)
	option.AddHeader("resource-accountId", encryptAccountId(hexKey, resourceAccountID))
	option.AddHeader("resource-source", resourceSource)
	option.Credentials = bce.NewCredentials(cred.AccessKeyID, cred.SecretAccessKey)

	option.AddHeadersToSign("host")

	return option
}

// EIP: https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/Pk9qRIMo58/miS95cpYcDcRUa
func (c *Client) NewSignOptionWithResourceAK(ctx context.Context, userID, resourceAK, resourceSK, resourceAccountID, resourceSource string) *bce.SignOption {
	cred, err := c.GetCredential(ctx, userID)
	if err != nil {
		logger.Errorf(ctx, "GetCredential failed: %v", err)
		return nil
	}

	if cred == nil {
		logger.Errorf(ctx, "GetCredential failed: cred is nil")
		return nil
	}

	// 构建 SignOption
	option := &bce.SignOption{}

	if resourceSK == "" {
		logger.Errorf(ctx, "resourceSK is empty")
		return nil
	}
	option.AddHeader("X-Bce-Request-Id", logger.GetUUID())
	option.AddHeader("X-Auth-Token", cred.Token.ID)
	option.AddHeader("X-Bce-Security-Token", cred.SessionToken)

	option.AddHeader("resource-accountId", encryptAccountId(resourceSK, resourceAccountID))
	option.AddHeader("resource-source", resourceSource)
	option.AddHeader("resource-ak", resourceAK)
	option.Credentials = bce.NewCredentials(cred.AccessKeyID, cred.SecretAccessKey)

	option.AddHeadersToSign("host")

	return option
}

// GetEndpoint 获取服务 endpoint
func (c *Client) GetEndpoint(ctx context.Context, accountID, region, serviceType string) (string, error) {
	// 获取 Credential
	cred, err := c.GetCredential(ctx, accountID)
	if err != nil {
		logger.Errorf(ctx, "GetCredential failed: %v", err)
		return "", err
	}

	if cred == nil {
		return "", errors.New("GetEndpoint failed: cred is nil")
	}

	for _, service := range cred.Token.Catalog {
		if service.Type == serviceType {
			// 没有 endpoint
			if service.Endpoints == nil || len(service.Endpoints) < 0 {
				return "", errors.New("getEndpoint failed: len(service.Endpoints) < 0")
			}

			// 寻找对应 region 的 endpoint
			for _, endpoint := range service.Endpoints {
				if endpoint.Region == region {
					return endpoint.URL, nil
				}
			}

			// 找不到对应的Region，返回第一个
			return service.Endpoints[0].URL, nil
		}
	}

	return "", errors.New("no endpoint found")
}

// GetCredential 获取 Credential
func (c *Client) GetCredential(ctx context.Context, accountID string) (*bcests.Credential, error) {
	var cred *bcests.Credential
	var err error

	// 通过缓存查询 accountID 对应 Credential
	cred, err = c.GetCredentialFromCache(ctx, accountID)
	if err != nil {
		logger.Errorf(ctx, "GetCredentialFromCache failed: %v", err)
		return nil, err
	}

	// 查询 Credential 失败, 则重新申请
	if cred == nil {
		cred, err = c.stsclient.AssumeRoleWithToken(ctx, accountID)
		if err != nil {
			logger.Errorf(ctx, "AssumeRoleWithToken accountID=%s failed: %v", accountID, err)
			return nil, err
		}

		if cred == nil {
			msg := "AssumeRoleWithToken failed: sts is nil"
			logger.Errorf(ctx, msg)
			return nil, fmt.Errorf(msg)
		}

		// 更新 Credential 缓存
		if err := c.UpdateCredentialInCache(ctx, accountID, cred); err != nil {
			logger.Errorf(ctx, "UpdateCredentialInCache failed: %v", err)
		}
	}

	return cred, nil
}

// GetCredentialFromCache 从缓存中获取 sts.Credential
func (c *Client) GetCredentialFromCache(ctx context.Context, accountID string) (*bcests.Credential, error) {
	item := cache.Get(accountID)
	if item == nil || item.Expired() {
		return nil, nil
	}

	if cred, ok := item.Value().(*bcests.Credential); ok {
		return cred, nil
	}

	return nil, fmt.Errorf("type assertion error, item=%+v", item.Value())
}

// UpdateCredentialInCache 更新缓存中 sts.Credential
func (c *Client) UpdateCredentialInCache(ctx context.Context, accountID string, cred *bcests.Credential) error {
	if cred == nil {
		return errors.New("cred is nil")
	}

	if cred.Expiration.IsZero() {
		return errors.New("cred's expire time not set")
	}

	// 计算 cred 有效时长
	now := time.Now()
	expireTime := cred.Expiration

	//  提前过期时间
	expireMarginDuration := 5 * time.Second
	ttl := time.Duration(expireTime.UnixNano()-now.UnixNano()) - expireMarginDuration

	if ttl <= 0 {
		return errors.New("cred is expired")
	}

	cache.Set(accountID, cred, ttl)

	return nil
}

func encryptAccountId(hexKey string, accountId string) string {
	tmStr := time.Now().UTC().Format("2006-01-02T15:04:05Z")
	content := accountId + "/" + tmStr
	src, _ := api.Aes128EncryptUseSecreteKey(hexKey, content)
	return src
}
