// /*
// Copyright 2022 Baidu, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
// */

// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/sts (interfaces: Interface)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	bce "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
	sts "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/sts"
)

// MockInterface is a mock of Interface interface.
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface.
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance.
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// GetCredential mocks base method.
func (m *MockInterface) GetCredential(arg0 context.Context, arg1 string) (*sts.Credential, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCredential", arg0, arg1)
	ret0, _ := ret[0].(*sts.Credential)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCredential indicates an expected call of GetCredential.
func (mr *MockInterfaceMockRecorder) GetCredential(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCredential", reflect.TypeOf((*MockInterface)(nil).GetCredential), arg0, arg1)
}

// GetCredentialFromCache mocks base method.
func (m *MockInterface) GetCredentialFromCache(arg0 context.Context, arg1 string) (*sts.Credential, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCredentialFromCache", arg0, arg1)
	ret0, _ := ret[0].(*sts.Credential)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCredentialFromCache indicates an expected call of GetCredentialFromCache.
func (mr *MockInterfaceMockRecorder) GetCredentialFromCache(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCredentialFromCache", reflect.TypeOf((*MockInterface)(nil).GetCredentialFromCache), arg0, arg1)
}

// GetEndpoint mocks base method.
func (m *MockInterface) GetEndpoint(arg0 context.Context, arg1, arg2, arg3 string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEndpoint", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEndpoint indicates an expected call of GetEndpoint.
func (mr *MockInterfaceMockRecorder) GetEndpoint(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEndpoint", reflect.TypeOf((*MockInterface)(nil).GetEndpoint), arg0, arg1, arg2, arg3)
}

// NewSignOption mocks base method.
func (m *MockInterface) NewSignOption(arg0 context.Context, arg1 string) *bce.SignOption {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewSignOption", arg0, arg1)
	ret0, _ := ret[0].(*bce.SignOption)
	return ret0
}

// NewSignOption indicates an expected call of NewSignOption.
func (mr *MockInterfaceMockRecorder) NewSignOption(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewSignOption", reflect.TypeOf((*MockInterface)(nil).NewSignOption), arg0, arg1)
}

// NewSignOptionWithResourceAK mocks base method.
func (m *MockInterface) NewSignOptionWithResourceAK(arg0 context.Context, arg1, arg2, arg3, arg4, arg5 string) *bce.SignOption {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewSignOptionWithResourceAK", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(*bce.SignOption)
	return ret0
}

// NewSignOptionWithResourceAK indicates an expected call of NewSignOptionWithResourceAK.
func (mr *MockInterfaceMockRecorder) NewSignOptionWithResourceAK(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewSignOptionWithResourceAK", reflect.TypeOf((*MockInterface)(nil).NewSignOptionWithResourceAK), arg0, arg1, arg2, arg3, arg4, arg5)
}

// NewSignOptionWithResourceHeader mocks base method.
func (m *MockInterface) NewSignOptionWithResourceHeader(arg0 context.Context, arg1, arg2, arg3, arg4 string) *bce.SignOption {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewSignOptionWithResourceHeader", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*bce.SignOption)
	return ret0
}

// NewSignOptionWithResourceHeader indicates an expected call of NewSignOptionWithResourceHeader.
func (mr *MockInterfaceMockRecorder) NewSignOptionWithResourceHeader(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewSignOptionWithResourceHeader", reflect.TypeOf((*MockInterface)(nil).NewSignOptionWithResourceHeader), arg0, arg1, arg2, arg3, arg4)
}

// SetDebug mocks base method.
func (m *MockInterface) SetDebug(arg0 bool) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetDebug", arg0)
}

// SetDebug indicates an expected call of SetDebug.
func (mr *MockInterfaceMockRecorder) SetDebug(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDebug", reflect.TypeOf((*MockInterface)(nil).SetDebug), arg0)
}

// UpdateCredentialInCache mocks base method.
func (m *MockInterface) UpdateCredentialInCache(arg0 context.Context, arg1 string, arg2 *sts.Credential) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateCredentialInCache", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateCredentialInCache indicates an expected call of UpdateCredentialInCache.
func (mr *MockInterfaceMockRecorder) UpdateCredentialInCache(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCredentialInCache", reflect.TypeOf((*MockInterface)(nil).UpdateCredentialInCache), arg0, arg1, arg2)
}
