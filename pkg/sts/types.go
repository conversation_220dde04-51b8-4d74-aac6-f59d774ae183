// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2018/12/25 19:30:00, by <EMAIL>, create
*/
/*
DESCRIPTION
封装 bce-sdk-go/sts.Interface 中 NewSignOption, 增加一层缓存
*/

package sts

import (
	"context"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
	bcests "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/sts"
)

// go:generate mockgen -copyright_file=${GOPATH}/src/icode.baidu.com/baidu/bci2/bci-cni-driver/hack/boilerplate.go.txt -destination=./mock/mock_client.go -package=mock icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/sts Interface

// Interface 定义 sts 相关接口
type Interface interface {
	SetDebug(debug bool)

	// 获取请求头和签名
	NewSignOption(ctx context.Context, accountID string) *bce.SignOption

	// 资源账号计费方式2
	NewSignOptionWithResourceHeader(ctx context.Context, userID, hexKey, resourceAccountID, resourceSource string) *bce.SignOption

	// 资源账号计费方式1-传递ak，使用sk加密
	NewSignOptionWithResourceAK(ctx context.Context, userID, resourceAK, resourceSK, resourceAccountID, resourceSource string) *bce.SignOption

	// 获取部分服务的 Endpoint
	GetEndpoint(ctx context.Context, accountID, region, serviceType string) (string, error)

	// 获取 Credential，先读缓存，没有则创建并更新
	GetCredential(ctx context.Context, accountID string) (*bcests.Credential, error)

	// Credential 缓存相关操作, 可通过内存, redis, etcd 等实现
	GetCredentialFromCache(ctx context.Context, accountID string) (*bcests.Credential, error)
	UpdateCredentialInCache(ctx context.Context, accountID string, cred *bcests.Credential) error
}
