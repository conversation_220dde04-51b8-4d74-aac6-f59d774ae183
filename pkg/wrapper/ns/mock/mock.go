// /*
// Copyright 2022 Baidu, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
// */

// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/ns (interfaces: Interface)

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	ns "github.com/containernetworking/plugins/pkg/ns"
	gomock "github.com/golang/mock/gomock"
)

// MockInterface is a mock of Interface interface.
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface.
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance.
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// GetCurrentNS mocks base method.
func (m *MockInterface) GetCurrentNS() (ns.NetNS, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCurrentNS")
	ret0, _ := ret[0].(ns.NetNS)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCurrentNS indicates an expected call of GetCurrentNS.
func (mr *MockInterfaceMockRecorder) GetCurrentNS() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCurrentNS", reflect.TypeOf((*MockInterface)(nil).GetCurrentNS))
}

// GetNS mocks base method.
func (m *MockInterface) GetNS(arg0 string) (ns.NetNS, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNS", arg0)
	ret0, _ := ret[0].(ns.NetNS)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNS indicates an expected call of GetNS.
func (mr *MockInterfaceMockRecorder) GetNS(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNS", reflect.TypeOf((*MockInterface)(nil).GetNS), arg0)
}

// WithNetNSPath mocks base method.
func (m *MockInterface) WithNetNSPath(arg0 string, arg1 func(ns.NetNS) error) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithNetNSPath", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// WithNetNSPath indicates an expected call of WithNetNSPath.
func (mr *MockInterfaceMockRecorder) WithNetNSPath(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithNetNSPath", reflect.TypeOf((*MockInterface)(nil).WithNetNSPath), arg0, arg1)
}
