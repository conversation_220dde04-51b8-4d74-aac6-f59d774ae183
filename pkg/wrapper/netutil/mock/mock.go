// /*
// Copyright 2022 Baidu, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
// */

// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/netutil (interfaces: Interface)

// Package mock is a generated GoMock package.
package mock

import (
	net "net"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	netlink "github.com/vishvananda/netlink"
)

// MockInterface is a mock of Interface interface.
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface.
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance.
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// DetectDefaultRouteInterfaceName mocks base method.
func (m *MockInterface) DetectDefaultRouteInterfaceName() (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DetectDefaultRouteInterfaceName")
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DetectDefaultRouteInterfaceName indicates an expected call of DetectDefaultRouteInterfaceName.
func (mr *MockInterfaceMockRecorder) DetectDefaultRouteInterfaceName() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DetectDefaultRouteInterfaceName", reflect.TypeOf((*MockInterface)(nil).DetectDefaultRouteInterfaceName))
}

// DetectInterfaceMTU mocks base method.
func (m *MockInterface) DetectInterfaceMTU(arg0 string) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DetectInterfaceMTU", arg0)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DetectInterfaceMTU indicates an expected call of DetectInterfaceMTU.
func (mr *MockInterfaceMockRecorder) DetectInterfaceMTU(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DetectInterfaceMTU", reflect.TypeOf((*MockInterface)(nil).DetectInterfaceMTU), arg0)
}

// GetIPFromPodNetNS mocks base method.
func (m *MockInterface) GetIPFromPodNetNS(arg0, arg1 string, arg2 int) (net.IP, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIPFromPodNetNS", arg0, arg1, arg2)
	ret0, _ := ret[0].(net.IP)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIPFromPodNetNS indicates an expected call of GetIPFromPodNetNS.
func (mr *MockInterfaceMockRecorder) GetIPFromPodNetNS(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIPFromPodNetNS", reflect.TypeOf((*MockInterface)(nil).GetIPFromPodNetNS), arg0, arg1, arg2)
}

// GetLinkByMacAddress mocks base method.
func (m *MockInterface) GetLinkByMacAddress(arg0 string) (netlink.Link, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLinkByMacAddress", arg0)
	ret0, _ := ret[0].(netlink.Link)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLinkByMacAddress indicates an expected call of GetLinkByMacAddress.
func (mr *MockInterfaceMockRecorder) GetLinkByMacAddress(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLinkByMacAddress", reflect.TypeOf((*MockInterface)(nil).GetLinkByMacAddress), arg0)
}

// GratuitousArpOverIface mocks base method.
func (m *MockInterface) GratuitousArpOverIface(arg0 net.IP, arg1 net.Interface) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GratuitousArpOverIface", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// GratuitousArpOverIface indicates an expected call of GratuitousArpOverIface.
func (mr *MockInterfaceMockRecorder) GratuitousArpOverIface(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GratuitousArpOverIface", reflect.TypeOf((*MockInterface)(nil).GratuitousArpOverIface), arg0, arg1)
}

// InterfaceByName mocks base method.
func (m *MockInterface) InterfaceByName(arg0 string) (*net.Interface, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InterfaceByName", arg0)
	ret0, _ := ret[0].(*net.Interface)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InterfaceByName indicates an expected call of InterfaceByName.
func (mr *MockInterfaceMockRecorder) InterfaceByName(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InterfaceByName", reflect.TypeOf((*MockInterface)(nil).InterfaceByName), arg0)
}
