/*
 * Copyright (c) 2021 Baidu, Inc. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file
 * except in compliance with the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the
 * License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
 * either express or implied. See the License for the specific language governing permissions
 * and limitations under the License.
 *
 */

package network

import (
	"net"

	"github.com/vishvananda/netlink"

	netlinkwrapper "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/netlink"
	nswrapper "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/ns"
)

// go:generate mockgen -copyright_file=${GOPATH}/src/icode.baidu.com/baidu/bci2/bci-cni-driver/hack/boilerplate.go.txt -destination=./mock/mock.go -package=mock icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/netutil Interface
type Interface interface {
	DetectInterfaceMTU(device string) (int, error)
	DetectDefaultRouteInterfaceName() (string, error)
	GetIPFromPodNetNS(podNSPath string, ifName string, ipFamily int) (net.IP, error)
	GetLinkByMacAddress(macAddress string) (netlink.Link, error)
	InterfaceByName(name string) (*net.Interface, error)
	GratuitousArpOverIface(srcIP net.IP, iface net.Interface) error
}

type linuxNetwork struct {
	nlink netlinkwrapper.Interface
	ns    nswrapper.Interface
}

func New() Interface {
	return &linuxNetwork{
		nlink: netlinkwrapper.New(),
		ns:    nswrapper.New(),
	}
}
