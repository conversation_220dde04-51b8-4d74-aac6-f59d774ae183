// /*
// Copyright 2022 Baidu, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
// */

// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/ipam (interfaces: Interface)

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	types "github.com/containernetworking/cni/pkg/types"
	types100 "github.com/containernetworking/cni/pkg/types/100"
	gomock "github.com/golang/mock/gomock"
)

// MockInterface is a mock of Interface interface.
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface.
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance.
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// ConfigureIface mocks base method.
func (m *MockInterface) ConfigureIface(arg0 string, arg1 *types100.Result) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConfigureIface", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ConfigureIface indicates an expected call of ConfigureIface.
func (mr *MockInterfaceMockRecorder) ConfigureIface(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfigureIface", reflect.TypeOf((*MockInterface)(nil).ConfigureIface), arg0, arg1)
}

// ExecAdd mocks base method.
func (m *MockInterface) ExecAdd(arg0 string, arg1 []byte) (types.Result, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExecAdd", arg0, arg1)
	ret0, _ := ret[0].(types.Result)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExecAdd indicates an expected call of ExecAdd.
func (mr *MockInterfaceMockRecorder) ExecAdd(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExecAdd", reflect.TypeOf((*MockInterface)(nil).ExecAdd), arg0, arg1)
}

// ExecDel mocks base method.
func (m *MockInterface) ExecDel(arg0 string, arg1 []byte) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExecDel", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ExecDel indicates an expected call of ExecDel.
func (mr *MockInterfaceMockRecorder) ExecDel(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExecDel", reflect.TypeOf((*MockInterface)(nil).ExecDel), arg0, arg1)
}
