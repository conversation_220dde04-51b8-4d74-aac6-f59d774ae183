// /*
// Copyright 2022 Baidu, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
// */

// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/ip (interfaces: Interface)

// Package mock is a generated GoMock package.
package mock

import (
	net "net"
	reflect "reflect"

	types "github.com/containernetworking/cni/pkg/types"
	types100 "github.com/containernetworking/cni/pkg/types/100"
	ns "github.com/containernetworking/plugins/pkg/ns"
	gomock "github.com/golang/mock/gomock"
	netlink "github.com/vishvananda/netlink"
)

// MockInterface is a mock of Interface interface.
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface.
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance.
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// AddDefaultRoute mocks base method.
func (m *MockInterface) AddDefaultRoute(arg0 net.IP, arg1 netlink.Link) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddDefaultRoute", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddDefaultRoute indicates an expected call of AddDefaultRoute.
func (mr *MockInterfaceMockRecorder) AddDefaultRoute(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddDefaultRoute", reflect.TypeOf((*MockInterface)(nil).AddDefaultRoute), arg0, arg1)
}

// AddHostRoute mocks base method.
func (m *MockInterface) AddHostRoute(arg0 *net.IPNet, arg1 net.IP, arg2 netlink.Link) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddHostRoute", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddHostRoute indicates an expected call of AddHostRoute.
func (mr *MockInterfaceMockRecorder) AddHostRoute(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddHostRoute", reflect.TypeOf((*MockInterface)(nil).AddHostRoute), arg0, arg1, arg2)
}

// AddRoute mocks base method.
func (m *MockInterface) AddRoute(arg0 *net.IPNet, arg1 net.IP, arg2 netlink.Link) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddRoute", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddRoute indicates an expected call of AddRoute.
func (mr *MockInterfaceMockRecorder) AddRoute(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddRoute", reflect.TypeOf((*MockInterface)(nil).AddRoute), arg0, arg1, arg2)
}

// DelLinkByName mocks base method.
func (m *MockInterface) DelLinkByName(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelLinkByName", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelLinkByName indicates an expected call of DelLinkByName.
func (mr *MockInterfaceMockRecorder) DelLinkByName(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelLinkByName", reflect.TypeOf((*MockInterface)(nil).DelLinkByName), arg0)
}

// DelLinkByNameAddr mocks base method.
func (m *MockInterface) DelLinkByNameAddr(arg0 string) ([]*net.IPNet, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelLinkByNameAddr", arg0)
	ret0, _ := ret[0].([]*net.IPNet)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelLinkByNameAddr indicates an expected call of DelLinkByNameAddr.
func (mr *MockInterfaceMockRecorder) DelLinkByNameAddr(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelLinkByNameAddr", reflect.TypeOf((*MockInterface)(nil).DelLinkByNameAddr), arg0)
}

// EnableForward mocks base method.
func (m *MockInterface) EnableForward(arg0 []*types100.IPConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EnableForward", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// EnableForward indicates an expected call of EnableForward.
func (mr *MockInterfaceMockRecorder) EnableForward(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EnableForward", reflect.TypeOf((*MockInterface)(nil).EnableForward), arg0)
}

// EnableIP4Forward mocks base method.
func (m *MockInterface) EnableIP4Forward() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EnableIP4Forward")
	ret0, _ := ret[0].(error)
	return ret0
}

// EnableIP4Forward indicates an expected call of EnableIP4Forward.
func (mr *MockInterfaceMockRecorder) EnableIP4Forward() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EnableIP4Forward", reflect.TypeOf((*MockInterface)(nil).EnableIP4Forward))
}

// EnableIP6Forward mocks base method.
func (m *MockInterface) EnableIP6Forward() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EnableIP6Forward")
	ret0, _ := ret[0].(error)
	return ret0
}

// EnableIP6Forward indicates an expected call of EnableIP6Forward.
func (mr *MockInterfaceMockRecorder) EnableIP6Forward() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EnableIP6Forward", reflect.TypeOf((*MockInterface)(nil).EnableIP6Forward))
}

// GetVethPeerIfindex mocks base method.
func (m *MockInterface) GetVethPeerIfindex(arg0 string) (netlink.Link, int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVethPeerIfindex", arg0)
	ret0, _ := ret[0].(netlink.Link)
	ret1, _ := ret[1].(int)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetVethPeerIfindex indicates an expected call of GetVethPeerIfindex.
func (mr *MockInterfaceMockRecorder) GetVethPeerIfindex(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVethPeerIfindex", reflect.TypeOf((*MockInterface)(nil).GetVethPeerIfindex), arg0)
}

// RandomVethName mocks base method.
func (m *MockInterface) RandomVethName() (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RandomVethName")
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RandomVethName indicates an expected call of RandomVethName.
func (mr *MockInterfaceMockRecorder) RandomVethName() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RandomVethName", reflect.TypeOf((*MockInterface)(nil).RandomVethName))
}

// RenameLink mocks base method.
func (m *MockInterface) RenameLink(arg0, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RenameLink", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// RenameLink indicates an expected call of RenameLink.
func (mr *MockInterfaceMockRecorder) RenameLink(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RenameLink", reflect.TypeOf((*MockInterface)(nil).RenameLink), arg0, arg1)
}

// SetupIPMasq mocks base method.
func (m *MockInterface) SetupIPMasq(arg0 *net.IPNet, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetupIPMasq", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetupIPMasq indicates an expected call of SetupIPMasq.
func (mr *MockInterfaceMockRecorder) SetupIPMasq(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetupIPMasq", reflect.TypeOf((*MockInterface)(nil).SetupIPMasq), arg0, arg1, arg2)
}

// SetupVeth mocks base method.
func (m *MockInterface) SetupVeth(arg0 string, arg1 int, arg2 string, arg3 ns.NetNS) (net.Interface, net.Interface, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetupVeth", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(net.Interface)
	ret1, _ := ret[1].(net.Interface)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// SetupVeth indicates an expected call of SetupVeth.
func (mr *MockInterfaceMockRecorder) SetupVeth(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetupVeth", reflect.TypeOf((*MockInterface)(nil).SetupVeth), arg0, arg1, arg2, arg3)
}

// SetupVethWithName mocks base method.
func (m *MockInterface) SetupVethWithName(arg0, arg1 string, arg2 int, arg3 string, arg4 ns.NetNS) (net.Interface, net.Interface, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetupVethWithName", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(net.Interface)
	ret1, _ := ret[1].(net.Interface)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// SetupVethWithName indicates an expected call of SetupVethWithName.
func (mr *MockInterfaceMockRecorder) SetupVethWithName(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetupVethWithName", reflect.TypeOf((*MockInterface)(nil).SetupVethWithName), arg0, arg1, arg2, arg3, arg4)
}

// TeardownIPMasq mocks base method.
func (m *MockInterface) TeardownIPMasq(arg0 *net.IPNet, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TeardownIPMasq", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// TeardownIPMasq indicates an expected call of TeardownIPMasq.
func (mr *MockInterfaceMockRecorder) TeardownIPMasq(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TeardownIPMasq", reflect.TypeOf((*MockInterface)(nil).TeardownIPMasq), arg0, arg1, arg2)
}

// ValidateExpectedInterfaceIPs mocks base method.
func (m *MockInterface) ValidateExpectedInterfaceIPs(arg0 string, arg1 []*types100.IPConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateExpectedInterfaceIPs", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ValidateExpectedInterfaceIPs indicates an expected call of ValidateExpectedInterfaceIPs.
func (mr *MockInterfaceMockRecorder) ValidateExpectedInterfaceIPs(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateExpectedInterfaceIPs", reflect.TypeOf((*MockInterface)(nil).ValidateExpectedInterfaceIPs), arg0, arg1)
}

// ValidateExpectedRoute mocks base method.
func (m *MockInterface) ValidateExpectedRoute(arg0 []*types.Route) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateExpectedRoute", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// ValidateExpectedRoute indicates an expected call of ValidateExpectedRoute.
func (mr *MockInterfaceMockRecorder) ValidateExpectedRoute(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateExpectedRoute", reflect.TypeOf((*MockInterface)(nil).ValidateExpectedRoute), arg0)
}
