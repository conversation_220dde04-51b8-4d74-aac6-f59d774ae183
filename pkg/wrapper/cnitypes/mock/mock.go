// /*
// Copyright 2022 Baidu, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
// */

// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/cnitypes (interfaces: Interface)

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	types "github.com/containernetworking/cni/pkg/types"
	gomock "github.com/golang/mock/gomock"
)

// MockInterface is a mock of Interface interface.
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface.
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance.
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// LoadArgs mocks base method.
func (m *MockInterface) LoadArgs(arg0 string, arg1 interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LoadArgs", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// LoadArgs indicates an expected call of LoadArgs.
func (mr *MockInterfaceMockRecorder) LoadArgs(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LoadArgs", reflect.TypeOf((*MockInterface)(nil).LoadArgs), arg0, arg1)
}

// PrintResult mocks base method.
func (m *MockInterface) PrintResult(arg0 types.Result, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PrintResult", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// PrintResult indicates an expected call of PrintResult.
func (mr *MockInterfaceMockRecorder) PrintResult(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PrintResult", reflect.TypeOf((*MockInterface)(nil).PrintResult), arg0, arg1)
}
