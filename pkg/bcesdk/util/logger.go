package util

import (
	"context"
	"fmt"
	"log"
	"os"
)

const (
	RequestID                      string = "RequestID"
	DEFAULT_LOGGER_FUNC_CALL_DEPTH        = 2
)

var DefaultLogger LoggerItf

type LoggerItf interface {
	// SetDebug set debug to on/off
	SetDebug(bool)
	// Trace log, only prints when debug is on
	Trace(context.Context, string, ...interface{})
	// Debug log, only prints when debug is on
	Debug(context.Context, string, ...interface{})
	// Info log
	Info(context.Context, string, ...interface{})
	// Warn log
	Warn(context.Context, string, ...interface{})
	// Error log
	Error(context.Context, string, ...interface{})
	// Fatal log, print followed by a call to os.Exit(1)
	Fatal(context.Context, string, ...interface{})
}

type Logger struct {
	log.Logger
	debug               bool
	loggerFuncCallDepth int // depth to trace log caller when computing the file name and line number
}

func init() {
	DefaultLogger = NewLogger("bce-sdk-go", true, DEFAULT_LOGGER_FUNC_CALL_DEPTH)
}

func NewLogger(prefix string, debug bool, loggerFuncCallDepth int) *Logger {
	logger := log.New(os.Stdout, "["+prefix+"] -- ", log.Ldate|log.Ltime|log.Lmicroseconds|log.Lshortfile)
	return &Logger{*logger, debug, loggerFuncCallDepth}
}

func (logger *Logger) SetDebug(debug bool) {
	logger.debug = debug
}

func (logger *Logger) Trace(ctx context.Context, format string, v ...interface{}) {
	if !logger.debug {
		return
	}
	if ctx != nil {
		if requestID := ctx.Value(RequestID); requestID != nil {
			logger.Output(logger.loggerFuncCallDepth,
				fmt.Sprintf("["+requestID.(string)+"] TRACE -- "+format+"\n", v...))
			return
		}
	}
	logger.Output(logger.loggerFuncCallDepth, fmt.Sprintf("TRACE -- "+format+"\n", v...))
}

func (logger *Logger) Debug(ctx context.Context, format string, v ...interface{}) {
	if !logger.debug {
		return
	}
	if ctx != nil {
		if requestID := ctx.Value(RequestID); requestID != nil {
			logger.Output(logger.loggerFuncCallDepth,
				fmt.Sprintf("["+requestID.(string)+"] DEBUG -- "+format+"\n", v...))
			return
		}
	}
	logger.Output(logger.loggerFuncCallDepth, fmt.Sprintf("DEBUG -- "+format+"\n", v...))
}

func (logger *Logger) Info(ctx context.Context, format string, v ...interface{}) {
	if ctx != nil {
		if requestID := ctx.Value(RequestID); requestID != nil {
			logger.Output(logger.loggerFuncCallDepth,
				fmt.Sprintf("["+ctx.Value(RequestID).(string)+"] INFO -- "+format+"\n", v...))
			return
		}
	}
	logger.Output(logger.loggerFuncCallDepth, fmt.Sprintf("INFO -- "+format+"\n", v...))
}

func (logger *Logger) Warn(ctx context.Context, format string, v ...interface{}) {
	if ctx != nil {
		if requestID := ctx.Value(RequestID); requestID != nil {
			logger.Output(logger.loggerFuncCallDepth,
				fmt.Sprintf("["+ctx.Value(RequestID).(string)+"] WARN -- "+format+"\n", v...))
			return
		}
	}
	logger.Output(logger.loggerFuncCallDepth, fmt.Sprintf("WARN -- "+format+"\n", v...))
}

func (logger *Logger) Error(ctx context.Context, format string, v ...interface{}) {
	if ctx != nil {
		if requestID := ctx.Value(RequestID); requestID != nil {
			logger.Output(logger.loggerFuncCallDepth,
				fmt.Sprintf("["+ctx.Value(RequestID).(string)+"] ERROR -- "+format+"\n", v...))
			return
		}
	}
	logger.Output(logger.loggerFuncCallDepth, fmt.Sprintf("ERROR -- "+format+"\n", v...))
}

func (logger *Logger) Fatal(ctx context.Context, format string, v ...interface{}) {
	if ctx != nil {
		if requestID := ctx.Value(RequestID); requestID != nil {
			logger.Output(logger.loggerFuncCallDepth,
				fmt.Sprintf("["+ctx.Value(RequestID).(string)+"] FATAL -- "+format+"\n", v...))
			return
		}
	} else {
		logger.Output(logger.loggerFuncCallDepth, fmt.Sprintf("FATAL -- "+format+"\n", v...))
	}
	os.Exit(1)
}
