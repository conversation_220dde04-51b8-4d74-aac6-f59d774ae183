package util

import (
	"net"

	gocidr "github.com/apparentlymart/go-cidr/cidr"
	netutil "k8s.io/utils/net"
)

var (
	_, PrivateIPv4Net1, _ = net.ParseCIDR("10.0.0.0/8")
	_, PrivateIPv4Net2, _ = net.ParseCIDR("**********/12")
	_, PrivateIPv4Net3, _ = net.ParseCIDR("***********/16")
	_, PrivateIPv4Net4, _ = net.ParseCIDR("*******/8")
	_, PrivateIPv4Net5, _ = net.ParseCIDR("*******/8")
	_, PrivateIPv6Net, _  = net.ParseCIDR("fc00::/7")

	PrivateIPv4Nets       = []*net.IPNet{PrivateIPv4Net1, PrivateIPv4Net2, PrivateIPv4Net3, PrivateIPv4Net4, PrivateIPv4Net5}
	PrivateIPv4NetsString = []string{"10.0.0.0/8", "**********/12", "***********/16", "*******/8", "*******/8"}

	PrivateIPv6Nets       = []*net.IPNet{PrivateIPv6Net}
	PrivateIPv6NetsString = []string{"fc00::/7"}

	_, IPv4ZeroCIDR, _ = net.ParseCIDR("0.0.0.0/0")
	_, IPv6ZeroCIDR, _ = net.ParseCIDR("::/0")

	_, LinkLocalCIDR, _ = net.ParseCIDR("***********/16")
)

func IsValidCIDR(cidr string) bool {
	ip, ipnet, err := net.ParseCIDR(cidr)
	if err != nil || !ip.Equal(ipnet.IP) {
		return false
	}
	return true
}

// IsConflictCIDR 检查两个网段是否冲突
func IsConflictCIDR(cidr1, cidr2 *net.IPNet) bool {
	var err error
	subnets := []*net.IPNet{cidr1, cidr2}

	// different IP family not conflict
	if netutil.IsIPv6CIDR(cidr1) != netutil.IsIPv6CIDR(cidr2) {
		return false
	}

	if netutil.IsIPv6CIDR(cidr1) {
		err = gocidr.VerifyNoOverlap(subnets, IPv6ZeroCIDR)
	} else {
		err = gocidr.VerifyNoOverlap(subnets, IPv4ZeroCIDR)
	}

	if err == nil {
		return false
	}
	return true
}

// IsConflictCIDRString 检查两个网段是否冲突
func IsConflictCIDRString(cidr1, cidr2 string) bool {
	_, c1, _ := net.ParseCIDR(cidr1)
	_, c2, _ := net.ParseCIDR(cidr2)
	return IsConflictCIDR(c1, c2)
}

// IsPrivateNetCIDR 检查网段是否属于私有网段
func IsPrivateNetCIDR(cidr *net.IPNet) bool {
	first, last := gocidr.AddressRange(cidr)
	if netutil.IsIPv6CIDR(cidr) {
		return PrivateIPv6Net.Contains(first) && PrivateIPv6Net.Contains(last)
	}

	for _, net := range PrivateIPv4Nets {
		if net.Contains(first) && net.Contains(last) {
			return true
		}
	}

	return false
}

// IsPrivateNetCIDRString 检查网段是否属于私有网段
func IsPrivateNetCIDRString(cidr string) bool {
	_, c, _ := net.ParseCIDR(cidr)
	return IsPrivateNetCIDR(c)
}

// IsPrivateNetIP 检查 ip (ipv4) 是否属于私有网段
func IsPrivateNetIP(ip string) bool {
	ipAddress := net.ParseIP(ip)

	for _, n := range PrivateIPv4Nets {
		if n.Contains(ipAddress) {
			return true
		}
	}

	return false
}
