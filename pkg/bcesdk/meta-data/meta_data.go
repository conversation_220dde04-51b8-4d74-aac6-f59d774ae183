package meta_data

import (
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"strings"
)

const (
	basePath = "/1.0/meta-data/"
)

var (
	ErrorNotImplemented = errors.New("meta-data API not implemented")
)

// go:generate mockgen -copyright_file=${GOPATH}/src/icode.baidu.com/baidu/bci2/bci-cni-driver/hack/boilerplate.go.txt -destination=./mock/mock.go -package=mock icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/meta-data Interface
type Interface interface {
	GetInstanceID() (string, error)
	GetInstanceName() (string, error)
	GetLocalIPv4() (string, error)
	GetAvailabilityZone() (string, error)
	GetRegion() (string, error)
	GetVPCID() (string, error)
	GetSubnetID() (string, error)
	GetLinkGateway(string, string) (string, error)
	GetLinkMask(string, string) (string, error)
}

var _ Interface = &Client{}

type Client struct {
	host   string
	scheme string
}

func NewClient(host, scheme string) *Client {
	c := &Client{
		host:   host,
		scheme: scheme,
	}
	if c.scheme == "" {
		c.scheme = "http"
	}
	if c.host == "" {
		c.host = "***************"
	}
	return c
}

func (c *Client) sendRequest(path string) ([]byte, error) {
	url := url.URL{
		Scheme: c.scheme,
		Host:   c.host,
		Path:   path,
	}
	resp, err := http.Get(url.String())
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// Get body content
	bodyContent, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode >= http.StatusBadRequest {
		return bodyContent, fmt.Errorf("Error Message: \"%s\", Status Code: %d", string(bodyContent), resp.StatusCode)
	}
	// Empty body means openstack meta-data API not implemented yet
	if strings.TrimSpace(string(bodyContent)) == "" {
		return bodyContent, ErrorNotImplemented
	}

	return bodyContent, err
}

func (c *Client) GetInstanceID() (string, error) {
	body, err := c.sendRequest(basePath + "instance-shortid")
	if err != nil {
		return "", err
	}
	instanceID := strings.TrimSpace(string(body))
	return instanceID, nil
}

func (c *Client) GetInstanceName() (string, error) {
	body, err := c.sendRequest(basePath + "instance-name")
	if err != nil {
		return "", err
	}
	instanceID := strings.TrimSpace(string(body))
	return instanceID, nil
}

func (c *Client) GetLocalIPv4() (string, error) {
	body, err := c.sendRequest(basePath + "local-ipv4")
	if err != nil {
		return "", err
	}
	addr := strings.TrimSpace(string(body))
	return addr, nil
}

func (c *Client) GetAvailabilityZone() (string, error) {
	body, err := c.sendRequest(basePath + "azone")
	if err != nil {
		return "", err
	}
	azone := strings.TrimSpace(string(body))
	return azone, nil
}

func (c *Client) GetRegion() (string, error) {
	body, err := c.sendRequest(basePath + "region")
	if err != nil {
		return "", err
	}
	region := strings.TrimSpace(string(body))
	return region, nil
}

func (c *Client) GetVPCID() (string, error) {
	body, err := c.sendRequest(basePath + "vpc-id")
	if err != nil {
		return "", err
	}
	VPCID := strings.TrimSpace(string(body))
	return VPCID, nil
}

func (c *Client) GetSubnetID() (string, error) {
	body, err := c.sendRequest(basePath + "subnet-id")
	if err != nil {
		return "", err
	}
	subnetID := strings.TrimSpace(string(body))
	return subnetID, nil
}

func (c *Client) GetLinkGateway(macAddress, ipAddress string) (string, error) {
	// eg. /1.0/meta-data/network/interfaces/macs/fa:26:00:01:6f:37/fixed_ips/**********/gateway
	path := fmt.Sprintf(basePath+"network/interfaces/macs/%s/fixed_ips/%s/gateway", macAddress, ipAddress)
	body, err := c.sendRequest(path)
	if err != nil {
		return "", err
	}
	gateway := strings.TrimSpace(string(body))
	return gateway, nil
}

func (c *Client) GetLinkMask(macAddress, ipAddress string) (string, error) {
	// eg. /1.0/meta-data/network/interfaces/macs/fa:26:00:01:6f:37/fixed_ips/**********/mask
	path := fmt.Sprintf(basePath+"network/interfaces/macs/%s/fixed_ips/%s/mask", macAddress, ipAddress)
	body, err := c.sendRequest(path)
	if err != nil {
		return "", err
	}
	mask := strings.TrimSpace(string(body))
	return mask, nil
}
