package meta_data

import (
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"

	"github.com/gorilla/mux"
)

var (
	testHTTPServer *httptest.Server
)

func setupTestEnv(handler http.Handler) {
	testHTTPServer = httptest.NewServer(handler)
}

func tearDownTestEnv() {
	testHTTPServer.Close()
}

func newHandler(handler func(w http.ResponseWriter, r *http.Request)) http.Handler {
	r := mux.NewRouter()

	r.<PERSON>(basePath+"instance-shortid", handler).Methods("GET")
	r.HandleFunc(basePath+"instance-name", handler).Methods("GET")
	r.<PERSON>(basePath+"local-ipv4", handler).Methods("GET")
	r.<PERSON>le<PERSON>(basePath+"azone", handler).Methods("GET")
	r.<PERSON>le<PERSON>unc(basePath+"region", handler).Methods("GET")
	r.<PERSON>(basePath+"vpc-id", handler).Methods("GET")
	r.<PERSON>(basePath+"subnet-id", handler).Methods("GET")

	return r
}

func handleMetaDataOK(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "text/html")

	w.WriteHeader(http.StatusOK)
	w.Write([]byte("xxx"))
}

func handleMetaDataNotImplemented(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "text/html")

	w.WriteHeader(http.StatusOK)
	w.Write([]byte(""))
}

func handleMetaDataError(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "text/html")

	w.WriteHeader(http.StatusInternalServerError)
	w.Write([]byte("internal server error"))
}

func TestMetaDataOK(t *testing.T) {
	setupTestEnv(newHandler(handleMetaDataOK))
	defer tearDownTestEnv()

	url, _ := url.Parse(testHTTPServer.URL)
	c := NewClient(url.Host, url.Scheme)

	var result string
	var err error

	result, err = c.GetInstanceID()
	if err != nil {
		t.Errorf("TestMetaData got error: %v", err)
	}

	result, err = c.GetInstanceName()
	if err != nil {
		t.Errorf("TestMetaData got error: %v", err)
	}

	result, err = c.GetLocalIPv4()
	if err != nil {
		t.Errorf("TestMetaData got error: %v", err)
	}

	result, err = c.GetAvailabilityZone()
	if err != nil {
		t.Errorf("TestMetaData got error: %v", err)
	}

	result, err = c.GetRegion()
	if err != nil {
		t.Errorf("TestMetaData got error: %v", err)
	}

	result, err = c.GetVPCID()
	if err != nil {
		t.Errorf("TestMetaData got error: %v", err)
	}

	result, err = c.GetSubnetID()
	if err != nil {
		t.Errorf("TestMetaData got error: %v", err)
	}

	if result != "xxx" {
		t.Errorf("TestMetaData want: %v , got : %v", "xxx", result)
	}
}

func TestMetaDataNotImplemented(t *testing.T) {
	setupTestEnv(newHandler(handleMetaDataNotImplemented))
	defer tearDownTestEnv()

	url, _ := url.Parse(testHTTPServer.URL)
	c := NewClient(url.Host, url.Scheme)

	var err error

	_, err = c.GetInstanceID()
	if err != ErrorNotImplemented {
		t.Errorf("TestMetaData got error: %v", err)
	}

	_, err = c.GetInstanceName()
	if err != ErrorNotImplemented {
		t.Errorf("TestMetaData got error: %v", err)
	}

	_, err = c.GetLocalIPv4()
	if err != ErrorNotImplemented {
		t.Errorf("TestMetaData got error: %v", err)
	}

	_, err = c.GetAvailabilityZone()
	if err != ErrorNotImplemented {
		t.Errorf("TestMetaData got error: %v", err)
	}

	_, err = c.GetRegion()
	if err != ErrorNotImplemented {
		t.Errorf("TestMetaData got error: %v", err)
	}

	_, err = c.GetVPCID()
	if err != ErrorNotImplemented {
		t.Errorf("TestMetaData got error: %v", err)
	}

	_, err = c.GetSubnetID()
	if err != ErrorNotImplemented {
		t.Errorf("TestMetaData got error: %v", err)
	}
}

func TestMetaDataError(t *testing.T) {
	setupTestEnv(newHandler(handleMetaDataError))
	defer tearDownTestEnv()

	url, _ := url.Parse(testHTTPServer.URL)
	c := NewClient(url.Host, url.Scheme)

	var err error

	_, err = c.GetInstanceID()
	if err == nil {
		t.Errorf("TestMetaData wants error")
	}

	_, err = c.GetInstanceName()
	if err == nil {
		t.Errorf("TestMetaData wants error")
	}
	_, err = c.GetLocalIPv4()
	if err == nil {
		t.Errorf("TestMetaData wants error")
	}

	_, err = c.GetAvailabilityZone()
	if err == nil {
		t.Errorf("TestMetaData wants error")
	}

	_, err = c.GetRegion()
	if err == nil {
		t.Errorf("TestMetaData wants error")
	}

	_, err = c.GetVPCID()
	if err == nil {
		t.Errorf("TestMetaData wants error")
	}

	_, err = c.GetSubnetID()
	if err == nil {
		t.Errorf("TestMetaData wants error")
	}
}
