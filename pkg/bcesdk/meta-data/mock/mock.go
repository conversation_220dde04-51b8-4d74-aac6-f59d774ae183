// /*
// Copyright 2022 Baidu, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
// */

// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/meta-data (interfaces: Interface)

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockInterface is a mock of Interface interface.
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface.
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance.
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// GetAvailabilityZone mocks base method.
func (m *MockInterface) GetAvailabilityZone() (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAvailabilityZone")
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAvailabilityZone indicates an expected call of GetAvailabilityZone.
func (mr *MockInterfaceMockRecorder) GetAvailabilityZone() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAvailabilityZone", reflect.TypeOf((*MockInterface)(nil).GetAvailabilityZone))
}

// GetInstanceID mocks base method.
func (m *MockInterface) GetInstanceID() (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstanceID")
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstanceID indicates an expected call of GetInstanceID.
func (mr *MockInterfaceMockRecorder) GetInstanceID() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstanceID", reflect.TypeOf((*MockInterface)(nil).GetInstanceID))
}

// GetInstanceName mocks base method.
func (m *MockInterface) GetInstanceName() (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstanceName")
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstanceName indicates an expected call of GetInstanceName.
func (mr *MockInterfaceMockRecorder) GetInstanceName() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstanceName", reflect.TypeOf((*MockInterface)(nil).GetInstanceName))
}

// GetLinkGateway mocks base method.
func (m *MockInterface) GetLinkGateway(arg0, arg1 string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLinkGateway", arg0, arg1)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLinkGateway indicates an expected call of GetLinkGateway.
func (mr *MockInterfaceMockRecorder) GetLinkGateway(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLinkGateway", reflect.TypeOf((*MockInterface)(nil).GetLinkGateway), arg0, arg1)
}

// GetLinkMask mocks base method.
func (m *MockInterface) GetLinkMask(arg0, arg1 string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLinkMask", arg0, arg1)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLinkMask indicates an expected call of GetLinkMask.
func (mr *MockInterfaceMockRecorder) GetLinkMask(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLinkMask", reflect.TypeOf((*MockInterface)(nil).GetLinkMask), arg0, arg1)
}

// GetLocalIPv4 mocks base method.
func (m *MockInterface) GetLocalIPv4() (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLocalIPv4")
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLocalIPv4 indicates an expected call of GetLocalIPv4.
func (mr *MockInterfaceMockRecorder) GetLocalIPv4() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLocalIPv4", reflect.TypeOf((*MockInterface)(nil).GetLocalIPv4))
}

// GetRegion mocks base method.
func (m *MockInterface) GetRegion() (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRegion")
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRegion indicates an expected call of GetRegion.
func (mr *MockInterfaceMockRecorder) GetRegion() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRegion", reflect.TypeOf((*MockInterface)(nil).GetRegion))
}

// GetSubnetID mocks base method.
func (m *MockInterface) GetSubnetID() (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSubnetID")
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSubnetID indicates an expected call of GetSubnetID.
func (mr *MockInterfaceMockRecorder) GetSubnetID() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSubnetID", reflect.TypeOf((*MockInterface)(nil).GetSubnetID))
}

// GetVPCID mocks base method.
func (m *MockInterface) GetVPCID() (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVPCID")
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVPCID indicates an expected call of GetVPCID.
func (mr *MockInterfaceMockRecorder) GetVPCID() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVPCID", reflect.TypeOf((*MockInterface)(nil).GetVPCID))
}
