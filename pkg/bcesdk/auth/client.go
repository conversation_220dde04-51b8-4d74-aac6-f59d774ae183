/*
 * @Author: ye<PERSON><PERSON><PERSON>@baidu.com
 * @Date: 2018-12-27 14:44:24
 *
 *
 * modification history
 * --------------------
 * 2018/12/27 20:32:05, by ye<PERSON><PERSON><PERSON>@baidu.com, switch to sts token authorization generation
 * 2019/03/14 21:00:00, by yezi<PERSON><PERSON>@baidu.com, mod RequestAuthCheck to meet common usage
 * 2019/10/23 17:26:00, by <EMAIL>, modify to meet common usage
 */

package auth

import (
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
)

// Endpoints defines the IAM endpoints
var Endpoints = map[string]string{
	"bj": "iam.bj.baidubce.com",
}

var _ Interface = &Client{}

type Client struct {
	*bce.Client
}

func NewClient(config *bce.Config) *Client {
	bceClient := bce.NewClient(config)
	return &Client{
		Client: bceClient,
	}
}

func (c *Client) GetURL(objectKey string, params map[string]string) string {
	host := c.Endpoint
	if host == "" {
		host = Endpoints[c.GetRegion()]
	}
	uriPath := objectKey
	return c.Client.GetURL(host, uriPath, params)
}
