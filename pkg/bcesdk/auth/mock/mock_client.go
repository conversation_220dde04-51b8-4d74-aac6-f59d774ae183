// /*
// Copyright 2022 Baidu, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
// */

// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/auth (interfaces: Interface)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	auth "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/auth"
	bce "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
)

// MockInterface is a mock of Interface interface.
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface.
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance.
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// Authentication mocks base method.
func (m *MockInterface) Authentication(arg0 context.Context, arg1 *auth.RequestInfoForm, arg2 *bce.SignOption) (*auth.AuthResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Authentication", arg0, arg1, arg2)
	ret0, _ := ret[0].(*auth.AuthResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Authentication indicates an expected call of Authentication.
func (mr *MockInterfaceMockRecorder) Authentication(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Authentication", reflect.TypeOf((*MockInterface)(nil).Authentication), arg0, arg1, arg2)
}

// Authorization mocks base method.
func (m *MockInterface) Authorization(arg0 context.Context, arg1 *auth.RequestInfoForm, arg2 *bce.SignOption) (*auth.AuthResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Authorization", arg0, arg1, arg2)
	ret0, _ := ret[0].(*auth.AuthResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Authorization indicates an expected call of Authorization.
func (mr *MockInterfaceMockRecorder) Authorization(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Authorization", reflect.TypeOf((*MockInterface)(nil).Authorization), arg0, arg1, arg2)
}

// GenAuthorization mocks base method.
func (m *MockInterface) GenAuthorization(arg0 context.Context, arg1 *auth.RequestSignForm, arg2 *bce.SignOption) (*auth.SignatureResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenAuthorization", arg0, arg1, arg2)
	ret0, _ := ret[0].(*auth.SignatureResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenAuthorization indicates an expected call of GenAuthorization.
func (mr *MockInterfaceMockRecorder) GenAuthorization(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenAuthorization", reflect.TypeOf((*MockInterface)(nil).GenAuthorization), arg0, arg1, arg2)
}

// SetDebug mocks base method.
func (m *MockInterface) SetDebug(arg0 bool) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetDebug", arg0)
}

// SetDebug indicates an expected call of SetDebug.
func (mr *MockInterfaceMockRecorder) SetDebug(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDebug", reflect.TypeOf((*MockInterface)(nil).SetDebug), arg0)
}
