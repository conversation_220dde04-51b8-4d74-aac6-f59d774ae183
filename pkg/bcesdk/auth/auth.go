// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2019/11/04 20:30:00, by <EMAIL>, create
*/
/*
DESCRIPTION
This file contains the implemention of Interface
*/

package auth

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/logger"
)

// getProtoRequestHeader 获取原生协议头信息
func getProtoRequestHeader(ctx context.Context, req *http.Request) (map[string]string, error) {
	buf := new(bytes.Buffer)
	h := make(map[string]string)
	if err := req.Header.Write(buf); err != nil {
		return h, err
	}
	lines := strings.Split(buf.String(), "\n")
	for _, l := range lines {
		sep := ": "
		if strings.Contains(l, sep) {
			l = strings.Replace(l, "\r", "", -1)
			kv := strings.Split(l, sep)
			if len(kv) > 2 {
				logger.Warningf(ctx, "This header maybe be parsed inappropriately, %v", l)
			}
			h[strings.ToLower(kv[0])] = kv[1]
		}
	}
	return h, nil
}

func NewAuthenticationArgs(ctx context.Context, req *http.Request) (*RequestInfoForm, error) {
	h, err := getProtoRequestHeader(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("fail to getProtoRequestHeader: %w", err)
	}
	h["host"] = req.Host
	return &RequestInfoForm{
		Authorization:    req.Header.Get("authorization"),
		Method:           req.Method,
		URI:              req.URL.Path,
		SignatureHeaders: h,
		Parameters:       req.URL.Query(),
	}, nil
}

// Authentication 认证
func (c *Client) Authentication(ctx context.Context, args *RequestInfoForm, options *bce.SignOption) (*AuthResponse, error) {
	params := map[string]string{}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	url := fmt.Sprintf("v1/iam/auth")
	req, err := bce.NewRequest("POST", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		logger.Errorf(ctx, "bce.NewRequest failed: %s", err)
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, options)
	if err != nil {
		logger.Errorf(ctx, "SendRequest failed: %s", err)
		return nil, err
	}

	var authResp AuthResponse
	bodyContent, err := resp.GetBodyContent()
	err = json.Unmarshal(bodyContent, &authResp)
	if err != nil {
		logger.Errorf(ctx, "Unmarshal failed: %s", err)
		return nil, err
	}

	return &authResp, err
}

func NewAuthorizationArgs(ctx context.Context, req *http.Request, region, resourceID, ownerID string, permissions []string) (*RequestInfoForm, error) {
	h, err := getProtoRequestHeader(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("fail to getProtoRequestHeader: %w", err)
	}
	h["host"] = req.Host
	return &RequestInfoForm{
		Authorization:    req.Header.Get("authorization"),
		Method:           req.Method,
		URI:              req.URL.Path,
		SignatureHeaders: h,
		Parameters:       req.URL.Query(),
		Permissions:      permissions,
		ResourceID:       resourceID,
		RegionID:         region,
		OwnerID:          ownerID,
	}, nil
}

// Authorization 认证 + 鉴权
func (c *Client) Authorization(ctx context.Context, args *RequestInfoForm, options *bce.SignOption) (*AuthResponse, error) {
	params := map[string]string{}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	url := fmt.Sprintf("v1/iam/permission")
	req, err := bce.NewRequest("POST", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, options)
	if err != nil {
		return nil, err
	}

	var authResp AuthResponse
	bodyContent, err := resp.GetBodyContent()
	err = json.Unmarshal(bodyContent, &authResp)
	if err != nil {
		return nil, err
	}

	return &authResp, err
}

func NewGenAuthorizationArgs(ctx context.Context, req *http.Request, accountID string) (*RequestSignForm, error) {
	h, err := getProtoRequestHeader(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("fail to getProtoRequestHeader: %w", err)
	}
	h["host"] = req.Host
	return &RequestSignForm{
		AccountID:        accountID,
		Method:           req.Method,
		URI:              req.URL.Path,
		Parameters:       req.URL.Query(),
		SignatureHeaders: h,
	}, nil
}

// GenAuthorization 获取签名字符串，分为service和user两种类型，就是永久 和 临时 ，sts改造之后，服务号已经没有权限获取永久ak sk 来生成
func (c *Client) GenAuthorization(ctx context.Context, args *RequestSignForm, options *bce.SignOption) (*SignatureResponse, error) {
	params := map[string]string{}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	url := fmt.Sprintf("v1/iam/authorization")
	req, err := bce.NewRequest("POST", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, options)
	if err != nil {
		return nil, err
	}

	var signature SignatureResponse
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(bodyContent, &signature)
	return &signature, err
}
