package eni

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/logger"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
)

// AddPrivateIPArgs defines args to add a private IP
type AddPrivateIPArgs struct {
	ENIID     string `json:"-"`
	PrivateIP string `json:"privateIpAddress"`
}

// AddPrivateIPResponse defines response to adds a private IP
type AddPrivateIPResponse struct {
	PrivateIP string `json:"privateIpAddress"`
}

func (args *AddPrivateIPArgs) validate() error {
	if args == nil {
		return fmt.Errorf("AddPrivateIP validate failed: args cannot be nil")
	}

	if args.ENIID == "" {
		return fmt.Errorf("AddPrivateIP validate failed: ENIID cannot be empty")
	}

	// check ip format
	if args.PrivateIP != "" {
		err := isIPLegal(args.PrivateIP)
		if err != nil {
			return err
		}
	}
	return nil
}

// AddPrivateIP adds a private IP to an eni
func (c *Client) AddPrivateIP(ctx context.Context, args *AddPrivateIPArgs, signOpt *bce.SignOption) (string, error) {
	ctx = logger.EnsureTraceIDInCtx(ctx)

	err := args.validate()
	if err != nil {
		return "", err
	}

	bodyContent, err := json.Marshal(args)
	if err != nil {
		return "", err
	}

	path := "v1/eni/" + args.ENIID + "/privateIp"
	req, err := bce.NewRequest("POST", c.GetURL(path, nil), bytes.NewBuffer(bodyContent))
	if err != nil {
		return "", err
	}

	resp, err := c.SendRequest(ctx, req, signOpt)
	if err != nil {
		return "", err

	}
	bodyContent, err = resp.GetBodyContent()
	if err != nil {
		return "", err
	}

	addPrivateIPResponse := &AddPrivateIPResponse{}
	err = json.Unmarshal(bodyContent, addPrivateIPResponse)
	if err != nil {
		return "", err
	}
	// the server responds a "null" ip address if eni already has the same ip as param `PrivateIP`
	// e.g. {"privateIpAddress":"null"}
	if addPrivateIPResponse.PrivateIP == "null" {
		return args.PrivateIP, nil
	}

	return addPrivateIPResponse.PrivateIP, nil
}
