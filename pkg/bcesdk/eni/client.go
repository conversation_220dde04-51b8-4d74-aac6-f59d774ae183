package eni

import (
	"fmt"
	"net"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
)

// Endpoint contains all endpoints of Baidu Cloud BCC.
var Endpoint = map[string]string{
	"bj":  "bcc.bj.baidubce.com",
	"gz":  "bcc.gz.baidubce.com",
	"su":  "bcc.su.baidubce.com",
	"hkg": "bcc.hkg.baidubce.com",
	"fwh": "bcc.fwh.baidubce.com",
	"bd":  "bcc.bd.baidubce.com",
}

// Config contains all options for bos.Client.
type Config struct {
	*bce.Config
}

// NewConfig for eni config
func NewConfig(config *bce.Config) *Config {
	return &Config{config}
}

// Client is the bos client implemention for Baidu Cloud BOS API.
type Client struct {
	*bce.Client
}

// NewClient for ENI Client
func NewClient(config *Config) *Client {
	bceClient := bce.NewClient(config.Config)
	return &Client{bceClient}
}

// GetURL generates the full URL of http request for Baidu Cloud BOS API.
func (c *Client) GetURL(objectKey string, params map[string]string) string {
	host := c.Endpoint

	if host == "" {
		host = Endpoint[c.GetRegion()]
	}

	uriPath := objectKey
	return c.Client.GetURL(host, uriPath, params)
}

func isIPLegal(ip string) error {
	if net.ParseIP(ip) == nil {
		return fmt.Errorf("%q is not a legal IP address", ip)
	}
	return nil
}
