package eni

import (
	"context"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/gorilla/mux"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/util"
)

func handleDeleteENI(w http.ResponseWriter, r *http.Request) {
	w.<PERSON>er().Set("Content-Type", "application/json")

	vars := mux.Vars(r)
	eniID, ok := vars["eniId"]
	if !ok {
		w.Write([]byte(fmt.Sprintf(`{"Code":"BadParameters","Message":"eniID is invalid,"RequestID":%s}`, util.GetRequestID())))
		return
	}

	if eniID == "" || eniID == "invalid-eni" {
		w.WriteHeader(http.StatusBadRequest)
		w.Write([]byte(fmt.Sprintf(`{"Code":"BadParameters","Message":"eniID is invalid,"RequestID":%s}`, util.GetRequestID())))
		return
	}

	w.<PERSON><PERSON>ead<PERSON>(http.StatusOK)
}

func TestDeleteENI(t *testing.T) {
	setupTestEnv(newENIHandler())
	defer tearDownTestEnv()

	type args struct {
		eniID   string
		signOpt *bce.SignOption
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name:    "normal case",
			args:    args{eniID: "eni-xxxxxx"},
			wantErr: false,
		},
		{
			name:    "empty eni id case",
			args:    args{eniID: ""},
			wantErr: true,
		},
		{
			name:    "invalid eni id case",
			args:    args{eniID: "invalid-eni"},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
			defer cancel()

			if err := eniClient.DeleteENI(ctx, tt.args.eniID, tt.args.signOpt); (err != nil) != tt.wantErr {
				t.Errorf("DeleteENI() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
