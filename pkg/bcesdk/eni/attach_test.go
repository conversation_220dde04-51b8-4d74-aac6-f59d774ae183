package eni

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"testing"
	"time"

	"github.com/gorilla/mux"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/util"
)

func handleAttachAndDetachENI(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	vars := mux.Vars(r)
	eniID, ok := vars["eniId"]
	if !ok {
		w.Write([]byte(fmt.Sprintf(`{"Code":"BadParameters","Message":"eniID is invalid,"RequestID":%s}`, util.GetRequestID())))
		return
	}

	if eniID == "" || eniID == "invalid-eni" {
		w.Write([]byte(fmt.Sprintf(`{"Code":"BadParameters","Message":"eniID is invalid,"RequestID":%s}`, util.GetRequestID())))
		return
	}

	bodyContent, _ := ioutil.ReadAll(r.Body)
	defer r.Body.Close()

	attachENiArgs := &AttachENIArgs{}
	err := json.Unmarshal(bodyContent, attachENiArgs)
	if err != nil || attachENiArgs.InstanceID == "" {
		w.Write([]byte(fmt.Sprintf(`{"Code":"BadParameters","Message":"AttachENIArgs is invalid,"RequestID":%s}`, util.GetRequestID())))
		return
	}

	w.WriteHeader(http.StatusOK)
}

func TestAttachENI(t *testing.T) {
	setupTestEnv(newENIHandler())
	defer tearDownTestEnv()

	type args struct {
		args    *AttachENIArgs
		signOpt *bce.SignOption
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "normal case",
			args: args{
				args: &AttachENIArgs{
					InstanceID: "i-abcdedf",
					ENIID:      "eni-123456",
				},
			},
			wantErr: false,
		},
		{
			name: "empty instance id case",
			args: args{
				args: &AttachENIArgs{
					InstanceID: "",
					ENIID:      "eni-123456",
				},
			},
			wantErr: true,
		},

		{
			name: "empty eni id case",
			args: args{
				args: &AttachENIArgs{
					InstanceID: "i-abcdedf",
					ENIID:      "",
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
			defer cancel()

			if err := eniClient.AttachENI(ctx, tt.args.args, tt.args.signOpt); (err != nil) != tt.wantErr {
				t.Errorf("AttachENI() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
