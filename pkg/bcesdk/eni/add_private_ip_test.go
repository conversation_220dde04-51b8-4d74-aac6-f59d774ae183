package eni

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"testing"
	"time"

	"github.com/gorilla/mux"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/util"
)

const addPrivateIPTemplate = `
{
	"privateIpAddress":"%s"
}`

const defaultIP = "************"
const existedIP = "***********"

func handleAddPrivateIP(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	vars := mux.Vars(r)
	eniID, ok := vars["eniId"]
	if !ok {
		w.Write([]byte(fmt.Sprintf(`{"Code":"BadParameters","Message":"eniID is invalid,"RequestID":%s}`, util.GetRequestID())))
		return
	}

	if eniID == "" || eniID == "invalid-eni" {
		w.WriteHeader(http.StatusBadRequest)
		w.Write([]byte(fmt.Sprintf(`{"Code":"BadParameters","Message":"eniID is invalid,"RequestID":%s}`, util.GetRequestID())))
		return
	}

	bodyContent, _ := ioutil.ReadAll(r.Body)
	defer r.Body.Close()

	addPrivateIPArgs := &AddPrivateIPArgs{}
	err := json.Unmarshal(bodyContent, addPrivateIPArgs)
	if err != nil {
		w.Write([]byte(fmt.Sprintf(`{"Code":"BadParameters","Message":"AddPrivateIPArgs is invalid,"RequestID":%s}`, util.GetRequestID())))
		return
	}

	addedIP := addPrivateIPArgs.PrivateIP
	if addedIP == "" {
		addedIP = defaultIP
	} else if addedIP == existedIP {
		addedIP = "null"
	}

	w.WriteHeader(http.StatusOK)
	w.Write([]byte(fmt.Sprintf(addPrivateIPTemplate, addedIP)))
}

func TestAddPrivateIP(t *testing.T) {
	setupTestEnv(newENIHandler())
	defer tearDownTestEnv()

	type args struct {
		args    *AddPrivateIPArgs
		signOpt *bce.SignOption
	}
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		{
			name: "normal case 1",
			args: args{
				args: &AddPrivateIPArgs{
					ENIID:     "eni-xxxxx",
					PrivateIP: "***********",
				},
			},
			wantErr: false,
			want:    "***********",
		},
		{
			name: "normal case 2",
			args: args{
				args: &AddPrivateIPArgs{
					ENIID:     "eni-xxxxx",
					PrivateIP: "",
				},
			},
			wantErr: false,
			want:    defaultIP,
		},
		{
			name: "empty eni id case",
			args: args{
				args: &AddPrivateIPArgs{
					ENIID:     "",
					PrivateIP: "",
				},
			},
			wantErr: true,
			want:    "",
		},
		{
			name: "existed ip case",
			args: args{
				args: &AddPrivateIPArgs{
					ENIID:     "eni-xxxxx",
					PrivateIP: existedIP,
				},
			},
			wantErr: false,
			want:    existedIP,
		},
		{
			name: "illegal ip case",
			args: args{
				args: &AddPrivateIPArgs{
					ENIID:     "eni-xxxxx",
					PrivateIP: "192.168.1.300",
				},
			},
			wantErr: true,
			want:    "",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
			defer cancel()

			got, err := eniClient.AddPrivateIP(ctx, tt.args.args, tt.args.signOpt)
			if (err != nil) != tt.wantErr {
				t.Errorf("AddPrivateIP() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("AddPrivateIP() got = %v, want %v", got, tt.want)
			}
		})
	}
}
