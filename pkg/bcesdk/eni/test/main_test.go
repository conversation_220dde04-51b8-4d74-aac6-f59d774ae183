package main

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/eni"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/sts"
)

const (
	resourceAccountID = "eca97e148cb74e9683d7b7240829d1ff"
	userID            = "2e1be1eb99e946c3a543ec5a4eaa7d39" // QA account
	eniHexkey         = "xxxxx"
	resourceType      = "bci"

	stsEndpoint     = "sts.bdbl.bce.baidu-int.com:8586/v1"
	iamEndpoint     = "iam.bdbl.bce.baidu-int.com/v3"
	serviceRoleName = "BceServiceRole_bci"
	servicePassword = "xxxxx"
	serviceName     = "bci"

	bccEndpoint = "bcc.bd.baidubce.com"

	userVPCID           = ""
	userSubnetID        = "sbn-tvqum5zsrgjz" // wmx-subnet
	userSecurityGroupID = "g-gu3w2z5vj835"

	eniID      = "eni-njk66dys7i5y"
	instanceID = "i-PJ24XjN3"
)

func mainCreate() {
	ctx := context.TODO()

	// 初始化 sts client
	stsclient := sts.NewClient(ctx, &bce.Config{
		Endpoint: stsEndpoint,
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   "bd",
	}, &bce.Config{
		Endpoint: iamEndpoint,
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   "bd",
	}, serviceRoleName, serviceName, servicePassword)

	client := eni.NewClient(eni.NewConfig(&bce.Config{
		Checksum: true,
		Timeout:  30 * time.Second,
		Endpoint: bccEndpoint,
	}))

	stsclient.SetDebug(true)
	client.SetDebug(true)

	args := eni.CreateENIArgs{
		Name:        "chenyaqi-bci3",
		Description: "created by chenyaqi-bci3",
		SubnetID:    userSubnetID,
		PrivateIPSet: []*eni.PrivateIP{
			{
				Primary: true,
			},
		},
		SecurityGroupIDs: []string{
			userSecurityGroupID,
		},
	}

	resp, err := client.CreateENI(ctx, &args, stsclient.NewSignOptionWithResourceHeader(ctx, userID, eniHexkey, resourceAccountID, resourceType))
	if err != nil {
		fmt.Printf("CreateENI failed: %v", err)
	}

	if respByte, err := json.Marshal(resp); err == nil {
		fmt.Printf("CreateENI succeeded: %s", string(respByte))
	}
}

func mainAttachENI() {
	ctx := context.TODO()

	// 初始化 sts client
	stsclient := sts.NewClient(ctx, &bce.Config{
		Endpoint: stsEndpoint,
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   "bd",
	}, &bce.Config{
		Endpoint: iamEndpoint,
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   "bd",
	}, serviceRoleName, serviceName, servicePassword)

	client := eni.NewClient(eni.NewConfig(&bce.Config{
		Checksum: true,
		Timeout:  30 * time.Second,
		Endpoint: bccEndpoint,
	}))

	stsclient.SetDebug(true)
	client.SetDebug(true)

	args := eni.AttachENIArgs{
		ENIID:      eniID,
		InstanceID: instanceID,
	}

	err := client.AttachENI(ctx, &args, stsclient.NewSignOptionWithResourceHeader(ctx, userID, eniHexkey, resourceAccountID, resourceType))
	if err != nil {
		fmt.Printf("AttachENI failed: %v", err)
	}
}

func mainDetachENI() {
	ctx := context.TODO()

	// 初始化 sts client
	stsclient := sts.NewClient(ctx, &bce.Config{
		Endpoint: stsEndpoint,
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   "bd",
	}, &bce.Config{
		Endpoint: iamEndpoint,
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   "bd",
	}, serviceRoleName, serviceName, servicePassword)

	client := eni.NewClient(eni.NewConfig(&bce.Config{
		Checksum: true,
		Timeout:  30 * time.Second,
		Endpoint: bccEndpoint,
	}))

	client.SetDebug(true)

	args := eni.DetachENIArgs{
		ENIID:      eniID,
		InstanceID: instanceID,
	}

	err := client.DetachENI(ctx, &args, stsclient.NewSignOptionWithResourceHeader(ctx, userID, eniHexkey, resourceAccountID, resourceType))
	if err != nil {
		fmt.Printf("DetachENI failed: %v", err)
	}
}

func mainList() {
	ctx := context.TODO()

	// 初始化 sts client
	stsclient := sts.NewClient(ctx, &bce.Config{
		Endpoint: stsEndpoint,
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   "bd",
	}, &bce.Config{
		Endpoint: iamEndpoint,
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   "bd",
	}, serviceRoleName, serviceName, servicePassword)

	client := eni.NewClient(eni.NewConfig(&bce.Config{
		Checksum: true,
		Timeout:  30 * time.Second,
		Endpoint: bccEndpoint,
	}))

	client.SetDebug(true)

	args := eni.ListENIsArgs{
		Name:  "chenyaqi-bci3",
		VPCID: "vpc-nzp6kwv06ec5",
	}

	resp, err := client.ListENIs(ctx, &args, stsclient.NewSignOptionWithResourceHeader(ctx, userID, eniHexkey, resourceAccountID, resourceType))
	if err != nil {
		fmt.Errorf("ListENIs failed: %v", err)
	}

	if respByte, err := json.Marshal(resp); err == nil {
		fmt.Printf("ListENIs succeeded: %s", string(respByte))
	}
}

func mainStat() {
	ctx := context.TODO()

	// 初始化 sts client
	stsclient := sts.NewClient(ctx, &bce.Config{
		Endpoint: stsEndpoint,
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   "bd",
	}, &bce.Config{
		Endpoint: iamEndpoint,
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   "bd",
	}, serviceRoleName, serviceName, servicePassword)

	client := eni.NewClient(eni.NewConfig(&bce.Config{
		Checksum: true,
		Timeout:  30 * time.Second,
		Endpoint: bccEndpoint,
	}))
	client.SetDebug(true)

	resp, err := client.StatENI(ctx, "eni-yqarmdncu4pj", stsclient.NewSignOptionWithResourceHeader(ctx, userID, eniHexkey, resourceAccountID, resourceType))
	if err != nil {
		fmt.Errorf("StatENI failed: %v", err)
	}

	if respByte, err := json.Marshal(resp); err == nil {
		fmt.Printf("StatENI succeeded: %s", string(respByte))
	}
}

func mainDeleteENI() {
	ctx := context.TODO()

	// 初始化 sts client
	stsclient := sts.NewClient(ctx, &bce.Config{
		Endpoint: stsEndpoint,
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   "bd",
	}, &bce.Config{
		Endpoint: iamEndpoint,
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   "bd",
	}, serviceRoleName, serviceName, servicePassword)

	client := eni.NewClient(eni.NewConfig(&bce.Config{
		Checksum: true,
		Timeout:  30 * time.Second,
		Endpoint: bccEndpoint,
	}))

	client.SetDebug(true)

	err := client.DeleteENI(ctx, eniID, stsclient.NewSignOptionWithResourceHeader(ctx, userID, eniHexkey, resourceAccountID, resourceType))
	if err != nil {
		fmt.Errorf("DeleteENI failed: %v", err)
	}
}

func main() {
	// mainCreate()
	// mainAttachENI()
	// mainDetachENI()
	//mainDeleteENI()
}
