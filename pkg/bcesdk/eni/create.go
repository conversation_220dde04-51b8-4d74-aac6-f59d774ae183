package eni

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/logger"
)

// CreateENIArgs defines CreateENI args
type CreateENIArgs struct {
	Name                       string       `json:"name"`
	SubnetID                   string       `json:"subnetId"`
	SecurityGroupIDs           []string     `json:"securityGroupIds"`
	EnterpriseSecurityGroupIds []string     `json:"enterpriseSecurityGroupIds,omitempty"`
	PrivateIPSet               []*PrivateIP `json:"privateIpSet"`      // IPv4辅助IP
	IPv6PrivateIPSet           []*PrivateIP `json:"ipv6PrivateIpSet"`  // IPv6辅助IP
	Description                string       `json:"description,omitempty"`
}

// CreateENIResponse defines CreateENI response
type CreateENIResponse struct {
	ENIID string `json:"eniId"`
}

func (args *CreateENIArgs) validate() error {
	if args == nil {
		return fmt.Errorf("CreateENI validate failed: args cannot be nil")
	}

	if args.Name == "" {
		return fmt.Errorf("CreateENI validate failed: ENI name cannot be empty")
	}
	if args.SubnetID == "" {
		return fmt.Errorf("CreateENI validate failed: ENI subnetId cannot be empty")
	}

	//securityGroupIds 与 enterpriseSecurityGroupIds 不能同时为空或者同时都有值，需要选取其中一个并对其赋值
	normalNil := false
	enterpriseNil := false

	if args.SecurityGroupIDs == nil || len(args.SecurityGroupIDs) == 0 {
		normalNil = true
	}

	if args.EnterpriseSecurityGroupIds == nil || len(args.EnterpriseSecurityGroupIds) == 0 {
		enterpriseNil = true
	}

	if normalNil && enterpriseNil {
		return fmt.Errorf("CreateENI validate failed: ENI securityGroupIds or enterpriseSecurityGroupId cannot be both empty")
	}

	if !normalNil && !enterpriseNil {
		return fmt.Errorf("CreateENI validate failed: ENI securityGroupIds cannot be both have value")
	}

	if args.PrivateIPSet == nil || len(args.PrivateIPSet) == 0 {
		return fmt.Errorf("CreateENI validate failed: ENI privateIpSet cannot be empty")
	}

	err := args.validatePrivateIPSet()
	if err != nil {
		return err
	}

	return nil
}

func (args *CreateENIArgs) validatePrivateIPSet() error {
	// 主IP只能有一个
	primaryIPCount := 0
	for _, ip := range args.PrivateIPSet {
		if ip.Primary {
			primaryIPCount++
		}
	}
	if primaryIPCount != 1 {
		return fmt.Errorf("CreateENI validate failed: ENI should have one primary IP")
	}
	return nil
}

// CreateENI creates an ENI
func (c *Client) CreateENI(ctx context.Context, args *CreateENIArgs, signOpt *bce.SignOption) (string, error) {
	ctx = logger.EnsureTraceIDInCtx(ctx)

	err := args.validate()
	if err != nil {
		return "", err
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return "", err
	}
	req, err := bce.NewRequest("POST", c.GetURL("v1/eni", nil), bytes.NewBuffer(postContent))
	if err != nil {
		return "", err
	}

	resp, err := c.SendRequest(ctx, req, signOpt)
	if err != nil {
		return "", err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return "", err
	}

	createENIResponse := &CreateENIResponse{}
	err = json.Unmarshal(bodyContent, createENIResponse)
	if err != nil {
		return "", err
	}

	return createENIResponse.ENIID, nil
}
