/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package eni

import (
	"bytes"
	"context"
	"encoding/json"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/logger"
)

// BatchDeletePrivateIP - batch delete private ip
//
// PARAMS:
//	- args: the arguments to batch delete private ipa
// RETURNS:
//	- error: nil if success otherwise the specific error
func (c *Client) BatchDeletePrivateIP(ctx context.Context, args *BatchPrivateIPArgs, signOpt *bce.SignOption) error {
	ctx = logger.EnsureTraceIDInCtx(ctx)

	bodyContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	path := "v1/eni/" + args.EniID + "/privateIp/batchDel"
	req, err := bce.NewRequest("POST", c.GetURL(path, nil), bytes.NewBuffer(bodyContent))
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, signOpt)
	if err != nil {
		return err

	}
	_, err = resp.GetBodyContent()
	if err != nil {
		return err
	}

	return nil
}
