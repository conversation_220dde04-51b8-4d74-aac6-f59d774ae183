package eni

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"reflect"
	"testing"
	"time"

	"github.com/gorilla/mux"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/util"
)

const statENITemplate = `
{
    "zoneName":"zoneA",
    "createdTime":"2019-04-12 18:53:07.0",
    "description":"",
    "instanceId":"i-Z2iJfB90",
    "eniId":"%s",
    "privateIpSet":[
        {
            "publicIpAddress":"**************",
            "primary":true,
            "privateIpAddress":"*************"
        }
    ],
    "macAddress":"fa:16:3e:c0:e4:3d",
    "name":"test",
    "securityGroupIds":[
        "g-SY5smEG9"
    ],
    "status":"available",
    "subnetId":"sbn-053m53r01z3h",
    "vpcId":"vpc-7hueyu2089wf"
}
`

func handleStatENI(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	vars := mux.Vars(r)
	eniID, ok := vars["eniId"]
	if !ok {
		w.Write([]byte(fmt.Sprintf(`{"Code":"BadParameters","Message":"eniID is invalid,"RequestID":%s}`, util.GetRequestID())))
	}

	if eniID == "" || eniID == "invalid-eni" {
		w.WriteHeader(http.StatusBadRequest)
		w.Write([]byte(`{"privateIpSet":[],"securityGroupIds":[]}`))
		return
	}

	w.WriteHeader(http.StatusOK)
	w.Write([]byte(fmt.Sprintf(statENITemplate, eniID)))
}

func TestStatENI(t *testing.T) {
	setupTestEnv(newENIHandler())
	defer tearDownTestEnv()

	type args struct {
		eniID   string
		signOpt *bce.SignOption
	}
	tests := []struct {
		name    string
		args    args
		want    *StatENIResponse
		wantErr bool
	}{
		{
			name:    "normal case",
			args:    args{eniID: "eni-12345"},
			wantErr: false,
		},
		{
			name:    "empty eniId case",
			args:    args{eniID: ""},
			wantErr: true,
		},
		{
			name:    "invalid eniId case",
			args:    args{eniID: "invalid-eni"},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
			defer cancel()

			if tt.wantErr == false {
				if err := json.Unmarshal([]byte(fmt.Sprintf(statENITemplate, tt.args.eniID)), &tt.want); err != nil {
					t.Errorf("StatENI() error = %v", err)
					return
				}
			}

			got, err := eniClient.StatENI(ctx, tt.args.eniID, tt.args.signOpt)
			if (err != nil) != tt.wantErr {
				t.Errorf("StatENI() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("StatENI() got = %v, want %v", got, tt.want)
			}
		})
	}
}
