package eni

import (
	"fmt"
	"net/http"
	"net/http/httptest"

	"github.com/gorilla/mux"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/util"
)

var (
	eniClient      *Client
	testHTTPServer *httptest.Server
)

func setupTestEnv(handler http.Handler) {
	credentials := &bce.Credentials{
		AccessKeyID:     "b275875821484bc7a3ee0bdae74c3ef1",
		SecretAccessKey: "xxx",
	}

	var bceConfig = &bce.Config{
		Credentials: credentials,
		Checksum:    true,
	}
	var eniConfig = NewConfig(bceConfig)
	eniClient = NewClient(eniConfig)
	eniClient.SetDebug(true)

	testHTTPServer = httptest.NewServer(handler)
	eniClient.Endpoint = testHTTPServer.URL
}

func tearDownTestEnv() {
	testHTTPServer.Close()
}

func newENIHandler() http.Handler {
	r := mux.NewRouter()

	r.HandleFunc("/v1/eni", handleListENIs).Methods("GET")
	r.HandleFunc("/v1/eni/{eniId}", handleStatENI).Methods("GET")
	r.HandleFunc("/v1/eni", handleCreateENI).Methods("POST")
	r.HandleFunc("/v1/eni/{eniId}", handleDeleteENI).Methods("DELETE")
	r.HandleFunc("/v1/eni/{eniId}", handleENIPutAction).Methods("PUT")
	r.HandleFunc("/v1/eni/{eniId}/privateIp", handleAddPrivateIP).Methods("POST")
	r.HandleFunc("/v1/eni/{eniId}/privateIp/{privateIpAddress}", handleDeletePrivateIP).Methods("DELETE")

	return r
}

func handleENIPutAction(w http.ResponseWriter, r *http.Request) {
	query := r.URL.Query()

	if _, ok := query["attach"]; ok {
		handleAttachAndDetachENI(w, r)
		return
	}

	if _, ok := query["detach"]; ok {
		handleAttachAndDetachENI(w, r)
		return
	}

	if _, ok := query["bindSg"]; ok {
		handleUpdateSecurityGroup(w, r)
		return
	}

	w.WriteHeader(http.StatusBadRequest)
	w.Write([]byte(fmt.Sprintf(`{"Code":"BadParameters","Message":"handler is invalid,"RequestID":%s}`, util.GetRequestID())))
}
