// /*
// Copyright 2022 Baidu, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
// */

// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/eni (interfaces: Interface)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	bce "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
	eni "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/eni"
)

// MockInterface is a mock of Interface interface.
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface.
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance.
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// AddPrivateIP mocks base method.
func (m *MockInterface) AddPrivateIP(arg0 context.Context, arg1 *eni.AddPrivateIPArgs, arg2 *bce.SignOption) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddPrivateIP", arg0, arg1, arg2)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddPrivateIP indicates an expected call of AddPrivateIP.
func (mr *MockInterfaceMockRecorder) AddPrivateIP(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddPrivateIP", reflect.TypeOf((*MockInterface)(nil).AddPrivateIP), arg0, arg1, arg2)
}

// AttachENI mocks base method.
func (m *MockInterface) AttachENI(arg0 context.Context, arg1 *eni.AttachENIArgs, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AttachENI", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AttachENI indicates an expected call of AttachENI.
func (mr *MockInterfaceMockRecorder) AttachENI(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AttachENI", reflect.TypeOf((*MockInterface)(nil).AttachENI), arg0, arg1, arg2)
}

// BatchAddPrivateIP mocks base method.
func (m *MockInterface) BatchAddPrivateIP(arg0 context.Context, arg1 *eni.BatchPrivateIPArgs, arg2 *bce.SignOption) (*eni.BatchAddPrivateIPResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchAddPrivateIP", arg0, arg1, arg2)
	ret0, _ := ret[0].(*eni.BatchAddPrivateIPResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchAddPrivateIP indicates an expected call of BatchAddPrivateIP.
func (mr *MockInterfaceMockRecorder) BatchAddPrivateIP(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchAddPrivateIP", reflect.TypeOf((*MockInterface)(nil).BatchAddPrivateIP), arg0, arg1, arg2)
}

// BatchDeletePrivateIP mocks base method.
func (m *MockInterface) BatchDeletePrivateIP(arg0 context.Context, arg1 *eni.BatchPrivateIPArgs, arg2 *bce.SignOption) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "BatchDeletePrivateIP", arg0, arg1, arg2)
}

// BatchDeletePrivateIP indicates an expected call of BatchDeletePrivateIP.
func (mr *MockInterfaceMockRecorder) BatchDeletePrivateIP(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDeletePrivateIP", reflect.TypeOf((*MockInterface)(nil).BatchDeletePrivateIP), arg0, arg1, arg2)
}

// CreateENI mocks base method.
func (m *MockInterface) CreateENI(arg0 context.Context, arg1 *eni.CreateENIArgs, arg2 *bce.SignOption) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateENI", arg0, arg1, arg2)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateENI indicates an expected call of CreateENI.
func (mr *MockInterfaceMockRecorder) CreateENI(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateENI", reflect.TypeOf((*MockInterface)(nil).CreateENI), arg0, arg1, arg2)
}

// DeleteENI mocks base method.
func (m *MockInterface) DeleteENI(arg0 context.Context, arg1 string, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteENI", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteENI indicates an expected call of DeleteENI.
func (mr *MockInterfaceMockRecorder) DeleteENI(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteENI", reflect.TypeOf((*MockInterface)(nil).DeleteENI), arg0, arg1, arg2)
}

// DeletePrivateIP mocks base method.
func (m *MockInterface) DeletePrivateIP(arg0 context.Context, arg1 *eni.DeletePrivateIPArgs, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeletePrivateIP", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeletePrivateIP indicates an expected call of DeletePrivateIP.
func (mr *MockInterfaceMockRecorder) DeletePrivateIP(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeletePrivateIP", reflect.TypeOf((*MockInterface)(nil).DeletePrivateIP), arg0, arg1, arg2)
}

// DetachENI mocks base method.
func (m *MockInterface) DetachENI(arg0 context.Context, arg1 *eni.DetachENIArgs, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DetachENI", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DetachENI indicates an expected call of DetachENI.
func (mr *MockInterfaceMockRecorder) DetachENI(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DetachENI", reflect.TypeOf((*MockInterface)(nil).DetachENI), arg0, arg1, arg2)
}

// ListENIs mocks base method.
func (m *MockInterface) ListENIs(arg0 context.Context, arg1 *eni.ListENIsArgs, arg2 *bce.SignOption) (*eni.ListENIsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListENIs", arg0, arg1, arg2)
	ret0, _ := ret[0].(*eni.ListENIsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListENIs indicates an expected call of ListENIs.
func (mr *MockInterfaceMockRecorder) ListENIs(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListENIs", reflect.TypeOf((*MockInterface)(nil).ListENIs), arg0, arg1, arg2)
}

// StatENI mocks base method.
func (m *MockInterface) StatENI(arg0 context.Context, arg1 string, arg2 *bce.SignOption) (*eni.StatENIResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StatENI", arg0, arg1, arg2)
	ret0, _ := ret[0].(*eni.StatENIResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StatENI indicates an expected call of StatENI.
func (mr *MockInterfaceMockRecorder) StatENI(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StatENI", reflect.TypeOf((*MockInterface)(nil).StatENI), arg0, arg1, arg2)
}

// UpdateSecurityGroup mocks base method.
func (m *MockInterface) UpdateSecurityGroup(arg0 context.Context, arg1 *eni.UpdateSecurityGroupArgs, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSecurityGroup", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateSecurityGroup indicates an expected call of UpdateSecurityGroup.
func (mr *MockInterfaceMockRecorder) UpdateSecurityGroup(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSecurityGroup", reflect.TypeOf((*MockInterface)(nil).UpdateSecurityGroup), arg0, arg1, arg2)
}
