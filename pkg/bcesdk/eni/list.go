package eni

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/logger"
)

// DefaultMaxKeys 是默认每页包含的最大数量，最大数量不超过1000
const DefaultMaxKeys = 1000

// ListENIsArgs defines ListENIs Args
type ListENIsArgs struct {
	VPCID      string
	InstanceID string
	Name       string
	Marker     string
	MaxKeys    int64
}

// ListENIsResponse defines ListENIs response
type ListENIsResponse struct {
	ENIs        []*ENI `json:"enis"`
	Marker      string `json:"marker"`
	IsTruncated bool   `json:"isTruncated"`
	NextMarker  string `json:"nextMarker"`
	MaxKeys     int64  `json:"maxKeys"`
}

func (args *ListENIsArgs) validate() error {
	if args == nil {
		return fmt.Errorf("ListENIsArgs validate failed: args cannot be nil")
	}

	if args.VPCID == "" {
		return fmt.Errorf("ListENIsArgs validate failed: VPCID cannot be empty")
	}

	if args.MaxKeys < 0 {
		return fmt.Errorf("ListENIsArgs validate failed: MaxKeys must be greater than 0")
	} else if args.MaxKeys == 0 {
		args.MaxKeys = DefaultMaxKeys
	}

	return nil
}

// ListENIs returns the list of ENIs via vpc id
func (c *Client) ListENIs(ctx context.Context, args *ListENIsArgs, signOpt *bce.SignOption) (*ListENIsResponse, error) {
	ctx = logger.EnsureTraceIDInCtx(ctx)

	err := args.validate()
	if err != nil {
		return nil, err
	}

	// url query params
	params := map[string]string{
		"vpcId":   args.VPCID,
		"maxKeys": strconv.FormatInt(args.MaxKeys, 10),
	}
	if args.Marker != "" {
		params["marker"] = args.Marker
	}
	if args.InstanceID != "" {
		params["instanceId"] = args.InstanceID
	}
	if args.Name != "" {
		params["name"] = args.Name
	}

	req, err := bce.NewRequest("GET", c.GetURL("v1/eni", params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, signOpt)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	listENIResponse := &ListENIsResponse{}
	err = json.Unmarshal(bodyContent, listENIResponse)
	if err != nil {
		return nil, err
	}

	return listENIResponse, nil
}
