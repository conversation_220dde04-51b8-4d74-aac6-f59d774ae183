package eni

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/logger"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
)

// UpdateSecurityGroupArgs defines args to update security group(s) of an eni
type UpdateSecurityGroupArgs struct {
	ENIID            string   `json:"-"`
	SecurityGroupIDs []string `json:"securityGroupIds"`
}

func (args *UpdateSecurityGroupArgs) validate() error {
	if args == nil {
		return fmt.Errorf("UpdateSecurityGroup validate failed: args cannot be nil")
	}

	if args.ENIID == "" {
		return fmt.Errorf("UpdateSecurityGroup validate failed: ENIID cannot be empty")
	}

	if args.SecurityGroupIDs == nil || len(args.SecurityGroupIDs) == 0 {
		return fmt.Errorf("UpdateSecurityGroup validate failed: SecurityGroupIDs cannot be empty")
	}

	return nil
}

// UpdateSecurityGroup updates security group(s) of an eni
func (c *Client) UpdateSecurityGroup(ctx context.Context, args *UpdateSecurityGroupArgs, signOpt *bce.SignOption) error {
	ctx = logger.EnsureTraceIDInCtx(ctx)

	err := args.validate()
	if err != nil {
		return err
	}

	params := map[string]string{
		"bindSg": "",
	}

	bodyContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	req, err := bce.NewRequest("PUT", c.GetURL("v1/eni/"+args.ENIID, params), bytes.NewBuffer(bodyContent))
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, signOpt)
	if err != nil {
		return err
	}

	_, err = resp.GetBodyContent()
	if err != nil {
		return err
	}

	return nil
}
