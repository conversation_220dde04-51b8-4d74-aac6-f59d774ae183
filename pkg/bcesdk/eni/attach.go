package eni

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/logger"
)

// AttachENIArgs defines AttachENI args
type AttachENIArgs struct {
	InstanceID string `json:"instanceId"`
	ENIID      string `json:"-"`
}

func (args *AttachENIArgs) validate() error {
	if args == nil {
		return errors.New("AttachENI validate failed: args cannot be nil")
	}

	if len(args.InstanceID) == 0 {
		return errors.New("AttachENI validate failed: InstanceID cannot be empty")
	}

	if len(args.ENIID) == 0 {
		return errors.New("AttachENI validate failed: ENIID cannot be empty")
	}

	return nil
}

// AttachENI attaches an ENI to a bcc instance
func (c *Client) AttachENI(ctx context.Context, args *AttachENIArgs, signOpt *bce.SignOption) error {
	ctx = logger.EnsureTraceIDInCtx(ctx)

	err := args.validate()
	if err != nil {
		return err
	}

	params := map[string]string{
		"attach": "",
	}

	bodyContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	req, err := bce.NewRequest("PUT", c.GetURL("v1/eni/"+args.ENIID, params), bytes.NewBuffer(bodyContent))
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, signOpt)
	if err != nil {
		return err
	}
	_, err = resp.GetBodyContent()
	if err != nil {
		return err
	}

	return nil
}

// AttachEIPArgs defines AttachEIP args
type AttachEIPArgs struct {
	PrivateIPAddress string `json:"privateIpAddress"`
	PublicIPAddress  string `json:"publicIpAddress"`
	ENIID            string `json:"-"`
}

func (args *AttachEIPArgs) validate() error {
	if args == nil {
		return errors.New("AttachENI validate failed: args cannot be nil")
	}

	if len(args.PrivateIPAddress) == 0 {
		return errors.New("AttachENI validate failed: InstanceID cannot be empty")
	}

	if len(args.PublicIPAddress) == 0 {
		return errors.New("AttachENI validate failed: ENIID cannot be empty")
	}

	if len(args.ENIID) == 0 {
		return errors.New("AttachENI validate failed: ENIID cannot be empty")
	}

	return nil
}

// AttachEIP attaches an EIP to a ENI instance
func (c *Client) AttachEIP(ctx context.Context, args *AttachEIPArgs, signOpt *bce.SignOption) error {
	ctx = logger.EnsureTraceIDInCtx(ctx)

	err := args.validate()
	if err != nil {
		return err
	}

	params := map[string]string{
		"bind": "",
	}

	bodyContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	req, err := bce.NewRequest("PUT", c.GetURL("v1/eni/"+args.ENIID, params), bytes.NewBuffer(bodyContent))
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, signOpt)
	if err != nil {
		return err
	}
	_, err = resp.GetBodyContent()
	if err != nil {
		return err
	}

	return nil
}
