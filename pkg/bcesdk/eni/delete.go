package eni

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/logger"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
)

// DeleteENI delete eni via eniID
func (c *Client) DeleteENI(ctx context.Context, eniID string, signOpt *bce.SignOption) error {
	ctx = logger.EnsureTraceIDInCtx(ctx)

	if eniID == "" {
		return fmt.Errorf("DeleteENI failed: eniID cannot be empty")
	}

	req, err := bce.NewRequest("DELETE", c.GetURL("v1/eni/"+eniID, nil), nil)
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, signOpt)
	if err != nil {
		return err
	}
	_, err = resp.GetBodyContent()
	if err != nil {
		return err
	}

	return nil
}
