/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package eni

import (
	"context"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"os"
	"reflect"
	"strings"
	"testing"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
)

type RoundTripFunc func(req *http.Request) *http.Response

func (f RoundTripFunc) RoundTrip(req *http.Request) (*http.Response, error) {
	return f(req), nil
}

func TestClient_BatchAddPrivateIp(t *testing.T) {
	type fields struct {
		Client *bce.Client
	}
	type args struct {
		ctx     context.Context
		args    *BatchPrivateIPArgs
		signOpt *bce.SignOption
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *BatchAddPrivateIPResult
		wantErr bool
	}{
		{
			name: "正常流程",
			fields: fields{
				Client: &bce.Client{
					Config: &bce.Config{
						Credentials:    &bce.Credentials{},
						Region:         "",
						Endpoint:       "",
						APIVersion:     "",
						Protocol:       "",
						UserAgent:      "",
						ProxyHost:      "",
						ProxyPort:      0,
						MaxConnections: 0,
						Timeout:        0,
						RetryPolicy:    nil,
						Checksum:       false,
					},
					HTTPClient: &http.Client{
						Transport: RoundTripFunc(func(req *http.Request) *http.Response {
							return &http.Response{
								StatusCode: http.StatusOK,
								Body: ioutil.NopCloser(strings.NewReader(`{
									"privateIpAddresses": [
										"***********",
										"***********",
										"***********"
									]
								}`)),
							}
						}),
						Timeout: 0,
					},
				},
			},
			args: args{
				ctx: context.TODO(),
				args: &BatchPrivateIPArgs{
					EniID:                 "",
					PrivateIPAddresses:    []string{},
					PrivateIPAddressCount: 0,
				},
				signOpt: &bce.SignOption{
					Timestamp:                 "",
					ExpirationPeriodInSeconds: 0,
					Headers: map[string]string{
						"": "",
					},
					HeadersToSign: []string{},
					Credentials: &bce.Credentials{
						AccessKeyID:     "",
						SecretAccessKey: "",
					},
					CustomSignFunc:  func(context.Context, *bce.Request) { fmt.Println("not implemented") },
					CustomUserAgent: "",
					PostForm:        false,
				},
			},
			want: &BatchAddPrivateIPResult{
				PrivateIPAddresses: []string{
					"***********",
					"***********",
					"***********"},
			},
			wantErr: false,
		},
		{
			name: "失败流程",
			fields: fields{
				Client: &bce.Client{
					Config: &bce.Config{
						Credentials:    &bce.Credentials{},
						Region:         "",
						Endpoint:       "",
						APIVersion:     "",
						Protocol:       "",
						UserAgent:      "",
						ProxyHost:      "",
						ProxyPort:      0,
						MaxConnections: 0,
						Timeout:        0,
						RetryPolicy:    nil,
						Checksum:       false,
					},
					HTTPClient: &http.Client{
						Transport: RoundTripFunc(func(req *http.Request) *http.Response {
							return &http.Response{
								StatusCode: http.StatusBadRequest,
								Body:       ioutil.NopCloser(strings.NewReader(``)),
							}
						}),
						Timeout: 0,
					},
				},
			},
			args: args{
				ctx: context.TODO(),
				args: &BatchPrivateIPArgs{
					EniID:                 "",
					PrivateIPAddresses:    []string{},
					PrivateIPAddressCount: 0,
				},
				signOpt: &bce.SignOption{
					Timestamp:                 "",
					ExpirationPeriodInSeconds: 0,
					Headers: map[string]string{
						"": "",
					},
					HeadersToSign: []string{},
					Credentials: &bce.Credentials{
						AccessKeyID:     "",
						SecretAccessKey: "",
					},
					CustomSignFunc:  func(context.Context, *bce.Request) { fmt.Println("not implemented") },
					CustomUserAgent: "",
					PostForm:        false,
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "失败流程",
			fields: fields{
				Client: &bce.Client{
					Config: &bce.Config{
						Credentials:    &bce.Credentials{},
						Region:         "",
						Endpoint:       "",
						APIVersion:     "",
						Protocol:       "",
						UserAgent:      "",
						ProxyHost:      "",
						ProxyPort:      0,
						MaxConnections: 0,
						Timeout:        0,
						RetryPolicy:    nil,
						Checksum:       false,
					},
					HTTPClient: &http.Client{
						Transport: RoundTripFunc(func(req *http.Request) *http.Response {
							return &http.Response{
								StatusCode: http.StatusOK,
								Body:       &dataErrReader{},
							}
						}),
						Timeout: 0,
					},
				},
			},
			args: args{
				ctx: context.TODO(),
				args: &BatchPrivateIPArgs{
					EniID:                 "",
					PrivateIPAddresses:    []string{},
					PrivateIPAddressCount: 0,
				},
				signOpt: &bce.SignOption{
					Timestamp:                 "",
					ExpirationPeriodInSeconds: 0,
					Headers: map[string]string{
						"": "",
					},
					HeadersToSign: []string{},
					Credentials: &bce.Credentials{
						AccessKeyID:     "",
						SecretAccessKey: "",
					},
					CustomSignFunc:  func(context.Context, *bce.Request) { fmt.Println("not implemented") },
					CustomUserAgent: "",
					PostForm:        false,
				},
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Client{
				Client: tt.fields.Client,
			}
			got, err := c.BatchAddPrivateIP(tt.args.ctx, tt.args.args, tt.args.signOpt)
			if (err != nil) != tt.wantErr {
				t.Errorf("Client.BatchAddPrivateIp() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Client.BatchAddPrivateIp() = %v, want %v", got, tt.want)
			}
		})
	}
}

func testEniBatchAddAndDelPrivateIPOnline(t *testing.T) {
	var (
		eniConfig = bce.NewConfig(bce.NewCredentials("ak", "sk"))
		ctx       = context.TODO()
	)

	eniConfig.Endpoint = "bcc.fwh.baidubce.com"

	client := NewClient(
		&Config{eniConfig},
	)

	resp, err := client.BatchAddPrivateIP(ctx, &BatchPrivateIPArgs{
		EniID:              "eni-nz37imp3ma7s",
		PrivateIPAddresses: []string{"************", "*************"},
	}, nil)
	if err != nil {
		t.Error("BatchAddPrivateIP", err)
	}

	log.Println("BatchAddPrivateIP resp:", resp.PrivateIPAddresses)

	fmt.Printf("-> Press Return key to continue.")
	_, _ = ioutil.ReadAll(os.Stdin)

	err = client.BatchDeletePrivateIP(ctx, &BatchPrivateIPArgs{
		EniID:              "eni-nz37imp3ma7s",
		PrivateIPAddresses: []string{"************", "*************"},
	}, nil)
	if err != nil {
		t.Error("BatchDeletePrivateIP", err)
	}
}
