package eni

import (
	"context"
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/logger"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
)

// StatENIResponse defines StatENI response
type StatENIResponse struct {
	ENIID                      string       `json:"eniId"`
	Name                       string       `json:"name"`
	Description                string       `json:"description"`
	VPCID                      string       `json:"vpcId"`
	SubnetID                   string       `json:"subnetId"`
	MacAddress                 string       `json:"macAddress"`
	Status                     string       `json:"status"`
	ZoneName                   string       `json:"zoneName"`
	InstanceID                 string       `json:"instanceId"`
	PrivateIPSet               []*PrivateIP `json:"privateIpSet"`     // IPv4地址列表
	IPv6PrivateIPSet           []*PrivateIP `json:"ipv6PrivateIpSet"` // IPv6地址列表
	SecurityGroupIDs           []string     `json:"SecurityGroupIds"`
	EnterpriseSecurityGroupIds []string     `json:"enterpriseSecurityGroupIds"`
	CreatedTime                string       `json:"createdTime"`
}

// StatENI checks ENI status via eniID
func (c *Client) StatENI(ctx context.Context, eniID string, signOpt *bce.SignOption) (*StatENIResponse, error) {
	ctx = logger.EnsureTraceIDInCtx(ctx)

	if eniID == "" {
		return nil, fmt.Errorf("StatENI failed: eniID cannot be empty")
	}

	req, err := bce.NewRequest("GET", c.GetURL("v1/eni/"+eniID, nil), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, signOpt)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	statENIResponse := &StatENIResponse{}
	err = json.Unmarshal(bodyContent, statENIResponse)
	if err != nil {
		return nil, err
	}
	// the server responds empty body if param `eniID` is invalid.
	// e.g. {"privateIpSet":[],"securityGroupIds":[]}
	if len(statENIResponse.PrivateIPSet) == 0 || statENIResponse.ENIID == "" {
		return nil, fmt.Errorf("StatENI failed: eniID is invalid")
	}

	return statENIResponse, nil
}
