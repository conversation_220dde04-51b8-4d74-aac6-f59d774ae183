/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package eni

import (
	"context"
	"io/ioutil"
	"net"
	"net/http"
	"strings"
	"testing"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/retrypolicy"
)

type dataErrReader struct{}

func (r *dataErrReader) Read(p []byte) (n int, err error) {
	return 0, net.ErrClosed
}

func (r *dataErrReader) Close() error {
	return nil
}

func TestClient_BatchDeletePrivateIp(t *testing.T) {
	type fields struct {
		Client *bce.Client
	}
	type args struct {
		ctx     context.Context
		args    *BatchPrivateIPArgs
		signOpt *bce.SignOption
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "正常流程",
			fields: fields{
				Client: &bce.Client{
					Config: &bce.Config{
						Credentials:    &bce.Credentials{},
						Region:         "",
						Endpoint:       "",
						APIVersion:     "",
						Protocol:       "",
						UserAgent:      "",
						ProxyHost:      "",
						ProxyPort:      0,
						MaxConnections: 0,
						Timeout:        0,
						RetryPolicy:    nil,
						Checksum:       false,
					},
					HTTPClient: &http.Client{
						Transport: RoundTripFunc(func(req *http.Request) *http.Response {
							return &http.Response{
								StatusCode: http.StatusOK,
								Body:       ioutil.NopCloser(strings.NewReader("")),
							}
						}),
						Timeout: 0,
					},
				},
			},
			args: args{
				ctx: context.TODO(),
				args: &BatchPrivateIPArgs{
					EniID:                 "",
					PrivateIPAddresses:    []string{},
					PrivateIPAddressCount: 0,
				},
				signOpt: &bce.SignOption{
					Timestamp:                 "",
					ExpirationPeriodInSeconds: 0,
					Headers: map[string]string{
						"": "",
					},
					HeadersToSign: []string{},
					Credentials: &bce.Credentials{
						AccessKeyID:     "",
						SecretAccessKey: "",
					},
					CustomUserAgent: "",
					PostForm:        false,
				},
			},
			wantErr: false,
		},
		{
			name: "失败流程",
			fields: fields{
				Client: &bce.Client{
					Config: &bce.Config{
						Credentials:    &bce.Credentials{},
						Region:         "",
						Endpoint:       "",
						APIVersion:     "",
						Protocol:       "",
						UserAgent:      "",
						ProxyHost:      "",
						ProxyPort:      0,
						MaxConnections: 0,
						Timeout:        0,
						RetryPolicy:    nil,
						Checksum:       false,
					},
					HTTPClient: &http.Client{
						Transport: RoundTripFunc(func(req *http.Request) *http.Response {
							return &http.Response{
								StatusCode: http.StatusBadGateway,
								Body:       ioutil.NopCloser(strings.NewReader("")),
							}
						}),
						Timeout: 0,
					},
				},
			},
			args: args{
				ctx: context.TODO(),
				args: &BatchPrivateIPArgs{
					EniID:                 "",
					PrivateIPAddresses:    []string{},
					PrivateIPAddressCount: 0,
				},
				signOpt: &bce.SignOption{
					Timestamp:                 "",
					ExpirationPeriodInSeconds: 0,
					Headers: map[string]string{
						"": "",
					},
					HeadersToSign: []string{},
					Credentials: &bce.Credentials{
						AccessKeyID:     "",
						SecretAccessKey: "",
					},
					CustomUserAgent: "",
					PostForm:        false,
				},
			},
			wantErr: true,
		},
		{
			name: "失败流程",
			fields: fields{
				Client: &bce.Client{
					Config: &bce.Config{
						Credentials:    &bce.Credentials{},
						Region:         "",
						Endpoint:       "",
						APIVersion:     "",
						Protocol:       "",
						UserAgent:      "",
						ProxyHost:      "",
						ProxyPort:      0,
						MaxConnections: 0,
						Timeout:        0,
						RetryPolicy:    retrypolicy.NewDefaultRetryPolicy(1, 1),
						Checksum:       false,
					},
					HTTPClient: &http.Client{
						Transport: RoundTripFunc(func(req *http.Request) *http.Response {
							return &http.Response{
								StatusCode: http.StatusOK,
								Body:       &dataErrReader{},
							}
						}),
						Timeout: 0,
					},
				},
			},
			args: args{
				ctx: context.TODO(),
				args: &BatchPrivateIPArgs{
					EniID:                 "",
					PrivateIPAddresses:    []string{},
					PrivateIPAddressCount: 0,
				},
				signOpt: &bce.SignOption{
					Timestamp:                 "",
					ExpirationPeriodInSeconds: 0,
					Headers: map[string]string{
						"": "",
					},
					HeadersToSign: []string{},
					Credentials: &bce.Credentials{
						AccessKeyID:     "",
						SecretAccessKey: "",
					},
					CustomUserAgent: "",
					PostForm:        false,
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Client{
				Client: tt.fields.Client,
			}
			if err := c.BatchDeletePrivateIP(tt.args.ctx, tt.args.args, tt.args.signOpt); (err != nil) != tt.wantErr {
				t.Errorf("Client.BatchDeletePrivateIp() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
