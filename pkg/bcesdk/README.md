# bce-sdk-go

bcc openapi sdk golang version

## 快速开始

操作挂载 CDS 磁盘为例，使用SDK

```go
package main

import (
    "log"
    "time"

    "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bcc"
    "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
)

func main() {
    ak := "xxxxx"
    sk := "xxxxx"
    region := "bj"
    cdsId := "v-M6jshva8"
    instanceId := "i-aWV32BU1"

    c := newBccClient(ak, sk, region)

    volume, err := c.DescribeVolume(cdsId)
    if err != nil {
        log.Fatalf("fail to describe volume %s: %v", cdsId, err)
    }
    if volume.Status == bcc.VOLUMESTATUS_AVALIABLE {
        args := &bcc.AttachCDSVolumeArgs{
            VolumeId:   cdsId,
            InstanceId: instanceId,
        }
        resp, err := c.AttachCDSVolume(args)
        if err != nil {
            log.Printf("fail to attach cds %s: %v", cdsId, err)
        } else {
            log.Printf("attach cds %s to %s on %s successfully", resp.VolumeId, resp.InstanceId, resp.Device)
        }
    }
}

func newBccClient(accessKeyID, secretAccessKey, region string) *bcc.Client {
    return bcc.NewClient(&bcc.Config{
        &bce.Config{
            Credentials: bce.NewCredentials(accessKeyID, secretAccessKey),
            Checksum:    true,
            Timeout:     30 * time.Second,
            Region:      region,
        },
    })
}
```
