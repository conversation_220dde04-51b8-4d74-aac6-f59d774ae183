package testutil

import (
	"net/http"
	"net/http/httptest"

	"github.com/gorilla/mux"
)

type TestEnv struct {
	Method  string
	Path    string
	Handler func(w http.ResponseWriter, r *http.Request)
}

func SetupTestEnv(envs []*TestEnv) *httptest.Server {
	r := mux.NewRouter()
	for _, env := range envs {
		if env == nil {
			continue
		}
		r.<PERSON>(env.Path, env.Handler).Methods(env.Method)
	}

	return httptest.NewServer(r)
}

func TearDownTestEnv(s *httptest.Server) {
	if s != nil {
		s.Close()
	}
}

func NewJSONHandler(code int, body []byte) func(w http.ResponseWriter, r *http.Request) {
	return func(w http.ResponseWriter, r *http.Request) {
		w.<PERSON><PERSON>().Set("Content-Type", "application/json")
		w.WriteHeader(code)
		if body != nil {
			w.Write(body)
		}
	}
}
