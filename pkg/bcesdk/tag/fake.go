package tag

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
)

type FakeClient struct {
	tags   []Tag
	tagMap map[Tag][]Resource
}

func NewFakeClient() *FakeClient {
	return &FakeClient{
		tags:   make([]Tag, 0),
		tagMap: make(map[Tag][]Resource),
	}
}

func (f *FakeClient) SetDebug(debug bool) {
	return
}

func (f *FakeClient) CreateTag(ctx context.Context, args *CreateTagsArgs, option *bce.SignOption) error {
	if args == nil {
		return fmt.Errorf("args is nil")
	}
	for _, tag := range args.Tags {
		if !checkTagListContains(f.tags, tag) {
			f.tags = append(f.tags, tag)
			f.tagMap[tag] = make([]Resource, 0)
		}
	}
	return nil
}

func checkTagListContains(list []Tag, tag Tag) bool {
	for _, candidate := range list {
		if candidate == tag {
			return true
		}
	}
	return false
}

func (f *FakeClient) DeleteTags(ctx context.Context, tags []Tag, option *bce.SignOption) error {
	for _, toDeleteTag := range tags {
		tempTags := make([]Tag, 0)
		for _, tag := range f.tags {
			if tag != toDeleteTag {
				tempTags = append(tempTags, tag)
			}
		}
		f.tags = tempTags
		f.tagMap[toDeleteTag] = make([]Resource, 0)
	}
	return nil
}

func (f *FakeClient) ListTags(ctx context.Context, args ListTagsParams, option *bce.SignOption) (*ListTagsResult, error) {
	tempTag := make([]*Tag, 0)
	for _, tag := range f.tags {
		if tag.TagKey == args.TagKey {
			if args.TagValue == nil || *args.TagValue == tag.TagValue {
				tempTag = append(tempTag, &tag)
			}
		}
	}
	return &ListTagsResult{Tags: tempTag}, nil
}

func (f *FakeClient) ListBoundResources(ctx context.Context, args *ListBoundResourcesParams, option *bce.SignOption) (*ListBoundResourcesResult, error) {
	if args == nil {
		return nil, fmt.Errorf("args is nil")
	}
	return &ListBoundResourcesResult{}, nil
}

func (f *FakeClient) BindResourcesInBatch(ctx context.Context, args *BindResourceInBatchArgs, option *bce.SignOption) error {
	if args == nil {
		return fmt.Errorf("args is nil")
	}

	targetTag := Tag{
		TagValue: args.TagValue,
		TagKey:   args.TagKey,
	}

	resourceList, ok := f.tagMap[targetTag]
	if !ok {
		resourceList = make([]Resource, 0)
	}
	for _, targetResource := range args.Resources {
		resourceList = append(resourceList, targetResource)
	}
	f.tagMap[targetTag] = resourceList

	return nil
}

func (f *FakeClient) UnbindResourcesInBatch(ctx context.Context, args *UnbindResourceInBatchArgs, option *bce.SignOption) error {
	if args == nil {
		return fmt.Errorf("args is nil")
	}

	targetTag := Tag{
		TagValue: args.TagValue,
		TagKey:   args.TagKey,
	}
	resourceList, ok := f.tagMap[targetTag]
	if !ok {
		return nil
	}

	resourceList = make([]Resource, 0)
	for _, targetResource := range resourceList {
		if !checkResourceContains(args.Resources, targetResource) {
			resourceList = append(resourceList, targetResource)
		}
	}

	return nil
}

func checkResourceContains(list []Resource, resource Resource) bool {
	for _, candidate := range list {
		if candidate == resource {
			return true
		}
	}
	return false
}

func (f *FakeClient) DeleteTagAssociations(ctx context.Context, args *DeleteTagAssociationsArgs, option *bce.SignOption) error {
	if args == nil {
		return fmt.Errorf("args is nil")
	}

	for tag, resources := range f.tagMap {
		resourceList := make([]Resource, 0)
		for _, targetResource := range resources {
			if targetResource != args.Resource {
				resourceList = append(resourceList, targetResource)
			}
		}
		f.tagMap[tag] = resourceList
	}

	return nil
}

func (f *FakeClient) QueryUserTagsInfoList(ctx context.Context, strongAssociation bool, args *QueryUserTagListArgs, option *bce.SignOption) (*AssociationsResult, error) {
	if args == nil {
		return nil, fmt.Errorf("args is nil")
	}
	return &AssociationsResult{}, nil
}

func (f *FakeClient) UpdateResourceAssociations(ctx context.Context, args *CreateAndAssignArgs, option *bce.SignOption) error {
	if args == nil {
		return fmt.Errorf("args is nil")
	}
	return nil
}
