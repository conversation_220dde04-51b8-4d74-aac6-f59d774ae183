package tag

import (
	"context"
	"testing"
	"time"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/iam"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/sts"
)

var (
	region      = "gz"
	serviceName = "cce"
	password    = "c8QBqhOKxmGu0Sl2p13guMil3U2grbYt"
	accountID   = "eca97e148cb74e9683d7b7240829d1ff" // online 账号

	tagClient = NewClient(&bce.Config{
		Endpoint: "tag.baidubce.com/v1",
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   region,
	})

	stsclient = sts.NewClient(context.TODO(), &bce.Config{
		Endpoint: sts.Endpoints[region],
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   region,
	}, &bce.Config{
		Endpoint: iam.Endpoints[region],
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   region,
	}, sts.RoleName, serviceName, password)
)

// 跑完测试的时候记得释放资源

func testClient_CreateTags(t *testing.T) {
	args := CreateTagsArgs{
		Tags: []Tag{
			{
				TagKey:   "jichao-bcesdk-unit-test",
				TagValue: "jichao-bcesdk-unit-test",
			},
		},
	}

	err := tagClient.CreateTag(context.TODO(), &args, stsclient.NewSignOption(context.TODO(), accountID))
	if err != nil {
		t.Errorf("CreateTags failed: %v", err)
		return
	}

	t.Logf("CreateTags Success")
}

func testClient_DeleteTags(t *testing.T) {
	tags := []Tag{
		{
			TagKey:   "jichao-bcesdk-unit-test",
			TagValue: "jichao-bcesdk-unit-test",
		},
	}

	err := tagClient.DeleteTags(context.TODO(), tags, stsclient.NewSignOption(context.TODO(), accountID))
	if err != nil {
		t.Errorf("DeleteTags failed: %v", err)
		return
	}

	t.Logf("DeleteTags Success")
}

func testClient_ListTags(t *testing.T) {
	listParams := ListTagsParams{
		TagKey: "jichao-bcesdk-unit-test",
	}

	listResult, err := tagClient.ListTags(context.TODO(), listParams, stsclient.NewSignOption(context.TODO(), accountID))
	if err != nil {
		t.Errorf("ListTags failed: %v", err)
		return
	}

	t.Logf("ListTags Success: %+v", listResult)
}

func testClient_ListBoundResult(t *testing.T) {
	listBoundParams := &ListBoundResourcesParams{
		TagKey: "jichao-bcesdk-unit-test",
	}
	listBoundResult, err := tagClient.ListBoundResources(context.TODO(), listBoundParams, stsclient.NewSignOption(context.TODO(), accountID))
	if err != nil {
		t.Errorf("ListTags failed: %v", err)
		return
	}

	t.Logf("ListBoundResources Success: %+v", listBoundResult)
}

func testClient_BindResourcesInBatch(t *testing.T) {
	batchBindArgs := &BindResourceInBatchArgs{
		TagKey:      "jichao-bcesdk-unit-test",
		TagValue:    "jichao-bcesdk-unit-test",
		ServiceType: "BLB",
		Resources: []Resource{
			{
				ResourceID:  "lb-8a6e5021",
				ServiceType: "BLB",
				Region:      "gz",
			},
		},
	}

	err := tagClient.BindResourcesInBatch(context.TODO(), batchBindArgs, stsclient.NewSignOption(context.TODO(), accountID))
	if err != nil {
		t.Errorf("BindResourcesInBatch failed: %v", err)
		return
	}

	t.Logf("BindResourcesInBatch Success")
}

func testClient_UnbindResourcesInBatch(t *testing.T) {
	batchUnbindArgs := &UnbindResourceInBatchArgs{
		TagKey:      "jichao-bcesdk-unit-test",
		TagValue:    "jichao-bcesdk-unit-test",
		ServiceType: "BLB",
		Resources: []Resource{
			{
				ResourceID:  "lb-8a6e5021",
				ServiceType: "BLB",
				Region:      "gz",
			},
		},
	}

	err := tagClient.UnbindResourcesInBatch(context.TODO(), batchUnbindArgs, stsclient.NewSignOption(context.TODO(), accountID))
	if err != nil {
		t.Errorf("UnbindResourcesInBatch failed: %v", err)
		return
	}

	t.Logf("UnbindResourcesInBatch Success")
}

func testClient_DeleteTagAssociations(t *testing.T) {
	args := &DeleteTagAssociationsArgs{
		Resource{
			ResourceID:  "lb-8a6e5021",
			ServiceType: "BLB",
			Region:      "gz",
		},
	}

	err := tagClient.DeleteTagAssociations(context.TODO(), args, stsclient.NewSignOption(context.TODO(), accountID))
	if err != nil {
		t.Errorf("DeleteTagAssociations failed: %v", err)
		return
	}

	t.Logf("DeleteTagAssociations Success")
}

func testClient_QueryUserTagsInfoList(t *testing.T) {
	tagKey := "jichao-bcesdk-unit-test"
	args := &QueryUserTagListArgs{
		Regions:      []string{"gz"},
		TagKey:       &tagKey,
		ServiceTypes: []string{"BLB"},
	}

	result, err := tagClient.QueryUserTagsInfoList(context.TODO(), false, args, stsclient.NewSignOption(context.TODO(), accountID))
	if err != nil {
		t.Errorf("QueryUserTagsInfoList failed: %v", err)
		return
	}

	t.Logf("QueryUserTagsInfoList Success: %+v", result)
}

func testClient_UpdateResourceAssociations(t *testing.T) {
	args := &CreateAndAssignArgs{
		Resources: []ResourceWithTag{
			{
				ResourceID:  "lb-8a6e5021",
				ServiceType: "BLB",
				Region:      "gz",
				Tags: []Tag{
					{
						TagKey:   "jichao-bcesdk-unit-test",
						TagValue: "jichao-bcesdk-unit-test",
					},
				},
			},
		},
	}

	err := tagClient.UpdateResourceAssociations(context.TODO(), args, stsclient.NewSignOption(context.TODO(), accountID))
	if err != nil {
		t.Errorf("UpdateResourceAssociations failed: %v", err)
		return
	}

	t.Logf("UpdateResourceAssociations Success")
}
