package iam

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/logger"
)

func (c *Client) GetToken(ctx context.Context, serviceName string, password string, option *bce.SignOption) (*Token, error) {
	params := map[string]string{}

	tokenRequest := TokenRequest{
		Auth: Authentication{
			Identity: Identity{
				Methods: []string{
					"password",
				},
				Password: Password{
					User: User{
						Domain: Domain{
							Name: "Default",
						},
						Name:     serviceName,
						Password: password,
					},
				},
			},
			Scope: Scope{
				Domain: Domain{
					ID: "default",
				},
			},
		},
	}

	postContent, err := json.Marshal(tokenRequest)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL("auth/tokens", params), bytes.<PERSON><PERSON><PERSON>er(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var tokenResponse *TokenResponse
	err = json.Unmarshal(bodyContent, &tokenResponse)
	if err != nil {
		return nil, err
	}
	token := tokenResponse.Token
	token.ID = resp.Header.Get("X-Subject-Token")

	return &token, nil
}

func (c *Client) GetAkSkByToken(ctx context.Context, serviceName string, password string, option *bce.SignOption) (*AccessKey, error) {
	token, err := c.GetToken(ctx, serviceName, password, bce.NewSignOptionWithoutAuth())
	if err != nil {
		return nil, err
	}
	option.AddHeader("X-Auth-Token", token.ID)

	params := map[string]string{}
	url := fmt.Sprintf("users/%s/accesskeys", token.User.ID)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), bytes.NewBuffer(nil))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var accessKeyResp *AccessKeyResponse
	err = json.Unmarshal(bodyContent, &accessKeyResp)
	if err != nil {
		return nil, err
	}

	accessKeys := accessKeyResp.AccessKeys
	if len(accessKeys) == 0 {
		return nil, errors.New("GetAkSkByToken: accesskeys list get from IAM is empty.")
	}

	var (
		enabledAccessKey *AccessKey
		index            int
	)
	for index = 0; index < len(accessKeys); index++ {
		if &accessKeys[index] == nil {
			continue
		}
		// 仅当ak被禁用时，iam才会返回该ak的enabled字段，且字段值为false
		if accessKeys[index].Enabled == nil || *accessKeys[index].Enabled {
			enabledAccessKey = &accessKeys[index]
			break
		}
	}

	if enabledAccessKey == nil {
		err := errors.New(fmt.Sprintf("GetAkSkByToken: all accesskeys get from from IAM are not enabled, accesskeys list size: %d.", len(accessKeys)))
		return enabledAccessKey, err
	}
	logger.Infof(ctx, "GetAkSkByToken: get enabled accesskey from IAM, accountId: %s, AK: %s, index: %d, accesskeys list size: %d.",
		token.User.ID, enabledAccessKey.Access, index, len(accessKeys))

	return enabledAccessKey, nil
}

func (c *Client) BatchVerifyPermissionByToken(ctx context.Context, verifyReq *VerifyRequest, subUserID, serviceName, password string, option *bce.SignOption) (*VerifyResponse, error) {
	if verifyReq == nil || len(verifyReq.VerifyList) == 0 {
		return nil, fmt.Errorf("verifyReq is nil")
	}

	token, err := c.GetToken(ctx, serviceName, password, bce.NewSignOptionWithoutAuth())
	if err != nil {
		return nil, err
	}
	option.AddHeader("X-Auth-Token", token.ID)

	params := map[string]string{}
	url := fmt.Sprintf("users/%s/batch_permissions", subUserID)

	postContent, err := json.Marshal(verifyReq)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var verifyResp VerifyResponse
	err = json.Unmarshal(bodyContent, &verifyResp)
	if err != nil {
		return nil, err
	}

	return &verifyResp, nil
}

func (c *Client) GetSubUser(ctx context.Context, request *UserListRequest, serviceName, password string, option *bce.SignOption) (*UserListResponse, error) {
	if request == nil {
		return nil, fmt.Errorf("request is nil")
	}

	token, err := c.GetToken(ctx, serviceName, password, bce.NewSignOptionWithoutAuth())
	if err != nil {
		return nil, err
	}
	option.AddHeader("X-Auth-Token", token.ID)

	option.AddHeader("X-Subuser-Support", "true")
	params := map[string]string{}

	if request.DomainID != "" {
		params["domain_id"] = request.DomainID
	}

	params["subuser"] = strconv.FormatBool(request.SubUser)

	req, err := bce.NewRequest("GET", c.GetURL("/users", params), bytes.NewBuffer(nil))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var userListResp UserListResponse
	err = json.Unmarshal(bodyContent, &userListResp)
	if err != nil {
		return nil, err
	}

	return &userListResp, nil
}
