// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/03/19 20:19:00, by <EMAIL>, create
*/
/*
DESCRIPTION
bce-sdk-go 重试策略: 等间隔重试策略
*/

package retrypolicy

import (
	"context"
	"time"
)

// IntervalRetryPolicy 实现等间隔重试
type IntervalRetryPolicy struct {
	maxRetryTime  int // 最大重试次数
	retryInterval int // 重试间隔
}

// NewIntervalRetryPolicy 初始化 BCESDKRetryPolicy
func NewIntervalRetryPolicy(ctx context.Context, maxRetryTime, retryInterval int) RetryPolicy {
	if maxRetryTime < 0 {
		maxRetryTime = 0
	}

	if retryInterval < 0 {
		retryInterval = 0
	}

	return &IntervalRetryPolicy{
		maxRetryTime:  maxRetryTime,
		retryInterval: retryInterval,
	}
}

// GetMaxErrorRetry 最大重试次数
func (r *IntervalRetryPolicy) GetMaxErrorRetry() int {
	return r.maxRetryTime
}

// GetDelayBeforeNextRetry 不知道啥意思
func (r *IntervalRetryPolicy) GetDelayBeforeNextRetry(ctx context.Context, err error, retriesAttempted int) time.Duration {
	// 超过最大次数则返回 0 退出
	if retriesAttempted > r.maxRetryTime {
		return -1
	}

	return time.Duration(r.retryInterval * int(time.Second))
}
