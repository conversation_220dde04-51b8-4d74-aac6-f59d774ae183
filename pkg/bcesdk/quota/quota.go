package quota

import (
	"bytes"
	"context"
	"encoding/json"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
)

func (c *Client) GetQuotas(ctx context.Context, request GetQuotasRequest, option *bce.SignOption) (*GetQuotasResponse, error) {
	params := map[string]string{
		"serviceType": string(request.ServiceType),
		"region":      request.Region,
		"name":        string(request.Name),
	}

	req, err := bce.NewRequest("GET", c.GetURL("v1/quota_center", params), bytes.NewBuffer(nil))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var quotaResp *GetQuotasResponse
	err = json.Unmarshal(bodyContent, &quotaResp)
	if err != nil {
		return nil, err
	}

	return quotaResp, nil
}
