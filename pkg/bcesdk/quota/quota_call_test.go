package quota

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
)

var client Interface
var region string

func init() {
	accessKeyID := "e0c68be8495540f280b3e9ef03ec25d2"
	secretAccessKey := "b599d5f4f30e41bcb0e1482c9de65bd6"
	region = "bj"

	client = NewClient(context.TODO(), &bce.Config{
		Credentials: bce.NewCredentials(accessKeyID, secretAccessKey),
		Endpoint:    Endpoints[region],
		Checksum:    true,
		Timeout:     30 * time.Second,
		Region:      region,
	})

	client.SetDebug(true)
}

func testGetQuota(t *testing.T) {

	request := GetQuotasRequest{
		ServiceType: "EIP",
		Region:      region,
		Name:        "eipInstanceQuota",
	}
	resp, err := client.GetQuotas(context.TODO(), request, NewSignOption())
	if err != nil {
		t.Errorf("GetQuotas failed: %v", err)
	}

	if respBytes, err := json.Marshal(resp); err == nil {
		t.Logf("GetQuotas succeeded: %s", string(respBytes))
	}
}
