#!/bin/bash
set -eu
set -o pipefail
# log control
: ${SCIRPT:="$0"}

function build_test_env() {
    export GOPATH=$PWD/../../../../../
    if [[ -d "/home/<USER>/buildkit/go/go_1.11.5"  ]]; then
        export GOROOT=/home/<USER>/buildkit/go/go_1.11.5
        export PATH=$GOROOT/bin/:$PATH
    fi
    git config --global http.sslVerify false
    git clone ssh://*******************:8235/baidu/god-env/god-v0-8-0-linux-amd64
    ./god-v0-8-0-linux-amd64/bin/god restore -v
    WORKROOT=$(pwd)
    UT_DIR=$WORKROOT/test
    mkdir -p $UT_DIR && cd $UT_DIR
    mkdir -p cover_output
    workdir=`pwd`/cover_output
    profile="$workdir/cover.out"
    mode=count
}


function generate_cover_data() {
    rm -rf "$workdir"
    mkdir "$workdir"

    for pkg in "$@"; do
        f="$workdir/$(echo $pkg | tr / -).cover"
        go test -v -covermode="$mode" -coverprofile="$f" "$pkg"
    done

    echo "mode: $mode" >"$profile"
    grep -h -v "^mode:" "$workdir"/*.cover >>"$profile"
}

function show_cover_report() {
    cd $workdir
    # auto show cover in browser
    go tool cover -html=cover.out
    # generate cover.html for jenkins
    go tool cover -html=cover.out -o $WORKROOT/cover.html
    go tool cover -func=cover.out
}

function build()
{
    mkdir -p output
    mv README.md ./output/
}

parameter=${1:-build}
case $parameter in
    "quick")
        build_test_env
        generate_cover_data $(go list ../... | grep bce-sdk-go | grep -v appblb | grep -v bcc | grep -v bos | grep -v eip)
        show_cover_report
        ;;
    "build")
        build
esac
