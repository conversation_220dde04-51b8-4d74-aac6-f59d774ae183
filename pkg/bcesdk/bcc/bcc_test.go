package bcc

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"testing"

	"github.com/gorilla/mux"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/util"
)

const (
	existInternalIP    = "existInternalIP"
	notExistInternalIP = "notExistInternalIP"
)

func testDescribeInstance(t *testing.T) {
	// ts := httptest.NewServer(InstancesHandler())
	// defer ts.Close()
	bccClient.SetDebug(true)
	// bccClient.Endpoint = ts.URL
	// ins, err := bccClient.DescribeInstance("i-YufwpQAe", nil)
	ins, err := bccClient.DescribeInstance(context.Background(), "i-7VUJvwqR", nil)
	if err != nil {
		t.Error(util.FormatTest("ListInstances", err.Error(), "nil"))
	} else {
		if ins.InstanceName != "instance-luz2ef4l-1" {
			t.Error("name error!")
		}
	}
}

func testListInstances(t *testing.T) {
	// ts := httptest.NewServer(InstancesHandler())
	// defer ts.Close()
	// bccClient.Endpoint = ts.URL
	// bccClient.Endpoint = "bcc.bce-api.baidu.com"
	bccClient.SetDebug(true)
	list, err := bccClient.ListInstances(context.Background(), nil)

	if err != nil {
		t.Error(util.FormatTest("ListInstances", err.Error(), "nil"))
	}
	for _, ins := range list {
		fmt.Println(ins.VPCID)
		if ins.InstanceID != "i-IyWRtII7" {
			// t.Error("instanceId error")
		}
	}
}

func newListInstancesByIPHandler() http.Handler {
	r := mux.NewRouter()
	r.HandleFunc("/v2/instance", func(w http.ResponseWriter, r *http.Request) {
		internalIP := r.URL.Query().Get("internalIp")
		if internalIP != existInternalIP {
			w.WriteHeader(http.StatusNotFound)
			return
		}

		respBody, err := json.Marshal(ListInstancesResponse{})
		if err != nil {
			w.WriteHeader(http.StatusBadRequest)
			return
		}
		w.WriteHeader(http.StatusOK)
		w.Write(respBody)
	}).Methods("GET")
	return r
}

func testListInstancesByIP(t *testing.T) {
	setupTestEnv(newListInstancesByIPHandler())
	defer tearDownTestEnv()

	testCases := []struct {
		name        string
		internalIP  string
		expectedErr bool
	}{
		{
			name:        "internal IP exist",
			internalIP:  existInternalIP,
			expectedErr: false,
		},
		{
			name:        "internal IP not exist",
			internalIP:  notExistInternalIP,
			expectedErr: true,
		},
	}

	for _, c := range testCases {
		_, err := bccClient.ListInstancesByIP(context.Background(), c.internalIP, nil)
		if (err == nil && c.expectedErr) || (err != nil && !c.expectedErr) {
			t.Errorf("name: %s, expected err: %v, actual: %v", c.name, c.expectedErr, err)
		}
	}
}
