package bcc

import (
	"context"
	"encoding/json"
	"io/ioutil"
	"net/http"
	"testing"

	"github.com/gorilla/mux"
)

const (
	existVolumeID    = "existVolumeID"
	notExistVolumeID = "notExistVolumeID"

	existInstanceID = "existInstanceID"

	existSnapshotID    = "existSnapshotID"
	notExistSnapshotID = "notExistSnapshotID"
)

func newCreateVolumeHandler() http.Handler {
	r := mux.NewRouter()
	r.<PERSON>le<PERSON>("/v2/volume", func(w http.ResponseWriter, r *http.Request) {
		reqBody, err := ioutil.ReadAll(r.Body)
		if err != nil {
			return
		}
		var args CreateVolumeArgs
		if err := json.Unmarshal(reqBody, &args); err != nil {
			w.WriteHeader(http.StatusBadRequest)
			return
		}
		if err := args.validate(); err != nil {
			w.WriteHeader(http.StatusBadRequest)
			return
		}

		resp := CreateVolumeResponse{
			VolumeIDs: []string{"testVolumeID"},
		}
		respBody, err := json.Marshal(resp)
		if err != nil {
			w.WriteHeader(http.StatusBadRequest)
			return
		}
		w.WriteHeader(http.StatusCreated)
		w.Write(respBody)
	}).Methods("POST")
	return r
}

func TestCreateVolumes(t *testing.T) {
	setupTestEnv(newCreateVolumeHandler())
	defer tearDownTestEnv()

	testCases := []struct {
		name        string
		args        CreateVolumeArgs
		expectedErr bool
	}{
		{
			name: "valid request",
			args: CreateVolumeArgs{
				PurchaseCount: 1,
				CdsSizeInGB:   5,
				StorageType:   StorageTypeHP1,
				Billing: &Billing{
					PaymentTiming: "Postpaid",
				},
				SnapshotID: "",
				ZoneName:   "",
				Name:       "",
			},
			expectedErr: false,
		},
		{
			name: "invalid request",
			args: CreateVolumeArgs{
				PurchaseCount: 0,
				CdsSizeInGB:   0,
				StorageType:   "",
				Billing:       nil,
				SnapshotID:    "",
				ZoneName:      "",
				Name:          "",
			},
			expectedErr: true,
		},
	}

	for _, c := range testCases {
		_, err := bccClient.CreateVolumes(context.Background(), &c.args, nil)
		if (err == nil && c.expectedErr) || (err != nil && !c.expectedErr) {
			t.Errorf("name: %s, expected err: %v, actual: %v", c.name, c.expectedErr, err)
		}
	}
}

func newDeleteVolumeHandler() http.Handler {
	r := mux.NewRouter()
	r.HandleFunc("/v2/volume/{volumeID}", func(w http.ResponseWriter, r *http.Request) {
		volumeID := mux.Vars(r)["volumeID"]
		if volumeID != existVolumeID {
			w.WriteHeader(http.StatusNotFound)
			return
		}

		w.WriteHeader(http.StatusOK)
	}).Methods("DELETE")
	return r
}

func TestDeleteVolume(t *testing.T) {
	setupTestEnv(newDeleteVolumeHandler())
	defer tearDownTestEnv()

	testCases := []struct {
		name        string
		volumeID    string
		expectedErr bool
	}{
		{
			name:        "volume exist",
			volumeID:    existVolumeID,
			expectedErr: false,
		},
		{
			name:        "volume not exist",
			volumeID:    notExistVolumeID,
			expectedErr: true,
		},
	}

	for _, c := range testCases {
		err := bccClient.DeleteVolume(context.Background(), c.volumeID, nil)
		if (err == nil && c.expectedErr) || (err != nil && !c.expectedErr) {
			t.Errorf("name: %s, expected err: %v, actual: %v", c.name, c.expectedErr, err)
		}
	}
}

func newGetVolumeListHandler() http.Handler {
	r := mux.NewRouter()
	r.HandleFunc("/v2/volume", func(w http.ResponseWriter, r *http.Request) {
		respBody, err := json.Marshal(ListVolumesResponse{})
		if err != nil {
			w.WriteHeader(http.StatusBadRequest)
			return
		}
		w.WriteHeader(http.StatusOK)
		w.Write(respBody)
	}).Methods("GET")
	return r
}

func TestGetVolumeList(t *testing.T) {
	setupTestEnv(newGetVolumeListHandler())
	defer tearDownTestEnv()

	testCases := []struct {
		name        string
		instanceID  string
		zoneName    string
		expectedErr bool
	}{
		{
			name:        "valid request",
			instanceID:  "",
			zoneName:    "",
			expectedErr: false,
		},
	}

	for _, c := range testCases {
		_, err := bccClient.ListVolumes(context.Background(), c.instanceID, c.zoneName, nil)
		if (err == nil && c.expectedErr) || (err != nil && !c.expectedErr) {
			t.Errorf("name: %s, expected err: %v, actual: %v", c.name, c.expectedErr, err)
		}
	}
}

func newDescribeVolumeHandler() http.Handler {
	r := mux.NewRouter()
	r.HandleFunc("/v2/volume/{volumeID}", func(w http.ResponseWriter, r *http.Request) {
		volumeID := mux.Vars(r)["volumeID"]
		if volumeID != existVolumeID {
			w.WriteHeader(http.StatusNotFound)
			return
		}

		respBody, err := json.Marshal(&DescribeVolumeResponse{
			Volume: &Volume{},
		})
		if err != nil {
			w.WriteHeader(http.StatusBadRequest)
			return
		}
		w.WriteHeader(http.StatusOK)
		w.Write(respBody)
	}).Methods("GET")
	return r
}

func TestDescribeVolume(t *testing.T) {
	setupTestEnv(newDescribeVolumeHandler())
	defer tearDownTestEnv()

	testCases := []struct {
		name        string
		volumeID    string
		expectedErr bool
	}{
		{
			name:        "volume exist",
			volumeID:    existVolumeID,
			expectedErr: false,
		},
		{
			name:        "volume not exist",
			volumeID:    notExistVolumeID,
			expectedErr: true,
		},
	}

	for _, c := range testCases {
		_, err := bccClient.DescribeVolume(context.Background(), c.volumeID, nil)
		if (err == nil && c.expectedErr) || (err != nil && !c.expectedErr) {
			t.Errorf("name: %s, expected err: %v, actual: %v", c.name, c.expectedErr, err)
		}
	}
}

func newAttachCDSVolumeHandler() http.Handler {
	r := mux.NewRouter()
	r.HandleFunc("/v2/volume/{volumeID}", func(w http.ResponseWriter, r *http.Request) {
		reqBody, err := ioutil.ReadAll(r.Body)
		if err != nil {
			return
		}
		var args AttachCDSVolumeArgs
		if err := json.Unmarshal(reqBody, &args); err != nil {
			w.WriteHeader(http.StatusBadRequest)
			return
		}
		volumeID := mux.Vars(r)["volumeID"]
		if volumeID != existVolumeID || args.InstanceId != existInstanceID {
			w.WriteHeader(http.StatusNotFound)
			return
		}

		resp := AttachCDSVolumeResponse{
			VolumeAttachment: &VolumeAttachment{
				VolumeID:   "",
				InstanceID: "",
				Device:     "",
				Serial:     "",
			},
		}
		respBody, err := json.Marshal(resp)
		if err != nil {
			w.WriteHeader(http.StatusBadRequest)
			return
		}
		w.WriteHeader(http.StatusOK)
		w.Write(respBody)
	}).Methods("PUT")
	return r
}

func TestAttachCDSVolume(t *testing.T) {
	setupTestEnv(newAttachCDSVolumeHandler())
	defer tearDownTestEnv()

	testCases := []struct {
		name        string
		args        AttachCDSVolumeArgs
		expectedErr bool
	}{
		{
			name: "valid request",
			args: AttachCDSVolumeArgs{
				VolumeId:   existVolumeID,
				InstanceId: existInstanceID,
			},
			expectedErr: false,
		},
		{
			name:        "invalid request",
			args:        AttachCDSVolumeArgs{},
			expectedErr: true,
		},
	}

	for _, c := range testCases {
		_, err := bccClient.AttachCDSVolume(context.Background(), &c.args, nil)
		if (err == nil && c.expectedErr) || (err != nil && !c.expectedErr) {
			t.Errorf("name: %s, expected err: %v, actual: %v", c.name, c.expectedErr, err)
		}
	}
}

func newDetachCDSVolumeHandler() http.Handler {
	r := mux.NewRouter()
	r.HandleFunc("/v2/volume/{volumeID}", func(w http.ResponseWriter, r *http.Request) {
		reqBody, err := ioutil.ReadAll(r.Body)
		if err != nil {
			return
		}
		var args AttachCDSVolumeArgs
		if err := json.Unmarshal(reqBody, &args); err != nil {
			w.WriteHeader(http.StatusBadRequest)
			return
		}
		volumeID := mux.Vars(r)["volumeID"]
		if volumeID != existVolumeID || args.InstanceId != existInstanceID {
			w.WriteHeader(http.StatusNotFound)
			return
		}

		w.WriteHeader(http.StatusOK)
	}).Methods("PUT")
	return r
}

func TestDetachCDSVolume(t *testing.T) {
	setupTestEnv(newDetachCDSVolumeHandler())
	defer tearDownTestEnv()

	testCases := []struct {
		name        string
		args        AttachCDSVolumeArgs
		expectedErr bool
	}{
		{
			name: "valid request",
			args: AttachCDSVolumeArgs{
				VolumeId:   existVolumeID,
				InstanceId: existInstanceID,
			},
			expectedErr: false,
		},
		{
			name:        "invalid request",
			args:        AttachCDSVolumeArgs{},
			expectedErr: true,
		},
	}

	for _, c := range testCases {
		err := bccClient.DetachCDSVolume(context.Background(), &c.args, nil)
		if (err == nil && c.expectedErr) || (err != nil && !c.expectedErr) {
			t.Errorf("name: %s, expected err: %v, actual: %v", c.name, c.expectedErr, err)
		}
	}
}

func newCreateSnapshotHandler() http.Handler {
	r := mux.NewRouter()
	r.HandleFunc("/v2/snapshot", func(w http.ResponseWriter, r *http.Request) {
		reqBody, err := ioutil.ReadAll(r.Body)
		if err != nil {
			return
		}
		var args CreateSnapShotArgs
		if err := json.Unmarshal(reqBody, &args); err != nil {
			w.WriteHeader(http.StatusBadRequest)
			return
		}
		if args.VolumeId != existVolumeID {
			w.WriteHeader(http.StatusNotFound)
			return
		}

		resp := CreateSnapShotResponse{
			SnapshotId: existSnapshotID,
		}
		respBody, err := json.Marshal(resp)
		if err != nil {
			w.WriteHeader(http.StatusBadRequest)
			return
		}
		w.WriteHeader(http.StatusCreated)
		w.Write(respBody)
	}).Methods("POST")
	return r
}

func TestCreateSnapshot(t *testing.T) {
	setupTestEnv(newCreateSnapshotHandler())
	defer tearDownTestEnv()

	testCases := []struct {
		name        string
		args        CreateSnapShotArgs
		expectedErr bool
	}{
		{
			name: "volume exist",
			args: CreateSnapShotArgs{
				VolumeId: existVolumeID,
			},
			expectedErr: false,
		},
		{
			name: "volume not exist",
			args: CreateSnapShotArgs{
				VolumeId:     notExistVolumeID,
				SnapshotName: "",
				Desc:         "",
			},
			expectedErr: true,
		},
	}

	for _, c := range testCases {
		_, err := bccClient.CreateSnapshot(context.Background(), &c.args, nil)
		if (err == nil && c.expectedErr) || (err != nil && !c.expectedErr) {
			t.Errorf("name: %s, expected err: %v, actual: %v", c.name, c.expectedErr, err)
		}
	}
}

func newDeleteSnapshotHandler() http.Handler {
	r := mux.NewRouter()
	r.HandleFunc("/v2/snapshot/{snapshotID}", func(w http.ResponseWriter, r *http.Request) {
		snapshotID := mux.Vars(r)["snapshotID"]
		if snapshotID != existSnapshotID {
			w.WriteHeader(http.StatusNotFound)
			return
		}

		w.WriteHeader(http.StatusOK)
	}).Methods("DELETE")
	return r
}

func TestDeleteSnapshot(t *testing.T) {
	setupTestEnv(newDeleteSnapshotHandler())
	defer tearDownTestEnv()

	testCases := []struct {
		name        string
		snapshotID  string
		expectedErr bool
	}{
		{
			name:        "snapshot exist",
			snapshotID:  existSnapshotID,
			expectedErr: false,
		},
		{
			name:        "snapshot not exist",
			snapshotID:  notExistSnapshotID,
			expectedErr: true,
		},
	}

	for _, c := range testCases {
		err := bccClient.DeleteSnapshot(context.Background(), c.snapshotID, nil)
		if (err == nil && c.expectedErr) || (err != nil && !c.expectedErr) {
			t.Errorf("name: %s, expected err: %v, actual: %v", c.name, c.expectedErr, err)
		}
	}
}

func newDescribeSnapshotHandler() http.Handler {
	r := mux.NewRouter()
	r.HandleFunc("/v2/snapshot/{snapshotID}", func(w http.ResponseWriter, r *http.Request) {
		snapshotID := mux.Vars(r)["snapshotID"]
		if snapshotID != existSnapshotID {
			w.WriteHeader(http.StatusNotFound)
			return
		}

		respBody, err := json.Marshal(DescribeSnapshotResponse{
			Snapshot: &Snapshot{},
		})
		if err != nil {
			w.WriteHeader(http.StatusBadRequest)
			return
		}
		w.WriteHeader(http.StatusOK)
		w.Write(respBody)
	}).Methods("GET")
	return r
}

func TestDescribeSnapshot(t *testing.T) {
	setupTestEnv(newDescribeSnapshotHandler())
	defer tearDownTestEnv()

	testCases := []struct {
		name        string
		snapshotID  string
		expectedErr bool
	}{
		{
			name:        "snapshot exist",
			snapshotID:  existSnapshotID,
			expectedErr: false,
		},
		{
			name:        "snapshot not exist",
			snapshotID:  notExistSnapshotID,
			expectedErr: true,
		},
	}

	for _, c := range testCases {
		_, err := bccClient.DescribeSnapshot(context.Background(), c.snapshotID, nil)
		if (err == nil && c.expectedErr) || (err != nil && !c.expectedErr) {
			t.Errorf("name: %s, expected err: %v, actual: %v", c.name, c.expectedErr, err)
		}
	}
}

func newGetSnapshotListHandler() http.Handler {
	r := mux.NewRouter()
	r.HandleFunc("/v2/snapshot", func(w http.ResponseWriter, r *http.Request) {
		volumeID := r.URL.Query().Get("volumeId")
		if volumeID != existVolumeID {
			w.WriteHeader(http.StatusNotFound)
			return
		}

		respBody, err := json.Marshal(GetSnapshotListResponse{})
		if err != nil {
			w.WriteHeader(http.StatusBadRequest)
			return
		}
		w.WriteHeader(http.StatusOK)
		w.Write(respBody)
	}).Methods("GET")
	return r
}

func TestGetSnapshotList(t *testing.T) {
	setupTestEnv(newGetSnapshotListHandler())
	defer tearDownTestEnv()

	testCases := []struct {
		name        string
		volumeID    string
		expectedErr bool
	}{
		{
			name:        "volume exist",
			volumeID:    existVolumeID,
			expectedErr: false,
		},
		{
			name:        "volume not exist",
			volumeID:    notExistVolumeID,
			expectedErr: true,
		},
	}

	for _, c := range testCases {
		_, err := bccClient.GetSnapshotList(context.Background(), c.volumeID, nil)
		if (err == nil && c.expectedErr) || (err != nil && !c.expectedErr) {
			t.Errorf("name: %s, expected err: %v, actual: %v", c.name, c.expectedErr, err)
		}
	}
}

func testDeleteCDSVolume(t *testing.T) {
	bccClient.SetDebug(true)
	err := bccClient.DeleteCDS(context.Background(), "v-JCvK3cpI")
	if err != nil {
		t.Error(err)
	}
}

func TestCreateVolumeArgs_validate(t *testing.T) {
	testCases := []struct {
		name          string
		args          CreateVolumeArgs
		expectedError bool
	}{
		{
			name: "valid args",
			args: CreateVolumeArgs{
				PurchaseCount: 0,
				CdsSizeInGB:   1,
				StorageType:   StorageTypeHP1,
				Billing: &Billing{
					PaymentTiming: "",
				},
				SnapshotID: "",
				ZoneName:   "",
				Name:       "",
			},
			expectedError: false,
		},
		{
			name: "empty StorageType",
			args: CreateVolumeArgs{
				PurchaseCount: 0,
				CdsSizeInGB:   1,
				StorageType:   "",
				Billing: &Billing{
					PaymentTiming: "",
				},
				SnapshotID: "",
				ZoneName:   "",
				Name:       "",
			},
			expectedError: true,
		},
		{
			name: "nil billing",
			args: CreateVolumeArgs{
				PurchaseCount: 0,
				CdsSizeInGB:   1,
				StorageType:   StorageTypeHP1,
				Billing:       nil,
				SnapshotID:    "",
				ZoneName:      "",
				Name:          "",
			},
			expectedError: true,
		},
		{
			name: "0 CdsSizeInGB",
			args: CreateVolumeArgs{
				PurchaseCount: 0,
				CdsSizeInGB:   0,
				StorageType:   StorageTypeHP1,
				Billing: &Billing{
					PaymentTiming: "",
				},
				SnapshotID: "",
				ZoneName:   "",
				Name:       "",
			},
			expectedError: true,
		},
		{
			name: "too long volume name",
			args: CreateVolumeArgs{
				PurchaseCount: 0,
				CdsSizeInGB:   1,
				StorageType:   StorageTypeHP1,
				Billing: &Billing{
					PaymentTiming: "",
				},
				SnapshotID: "",
				ZoneName:   "",
				Name:       "toooooooooooooooooooooooooooooooooooooooooo looooooooooooooooooooooooooong",
			},
			expectedError: true,
		},
	}

	for _, c := range testCases {
		if err := c.args.validate(); (c.expectedError && err == nil) || (!c.expectedError && err != nil) {
			t.Errorf("name: %s, expected err: %v, actual err: %v", c.name, c.expectedError, err)
		}
	}
}
