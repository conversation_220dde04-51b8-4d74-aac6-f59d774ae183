/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package bcc

import (
	"context"
	"io/ioutil"
	"net/http"
	"reflect"
	"testing"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
)

func handleBatchAddIp(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	_, _ = ioutil.ReadAll(r.Body)
	defer r.Body.Close()

	w.WriteHeader(http.StatusOK)
	w.Write([]byte(` {
		"privateIps": [
		 "*********",
		 "*********"
		]
	 }`))
}

func handleBatchDelIp(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	_, _ = ioutil.ReadAll(r.Body)
	defer r.Body.Close()

	w.WriteHeader(http.StatusOK)
}

func TestClient_BatchAddIP(t *testing.T) {
	setupTestEnv(InstancesHandler())
	defer tearDownTestEnv()

	type fields struct {
		Client *bce.Client
	}
	type args struct {
		ctx     context.Context
		args    *BatchAddIpArgs
		signOpt *bce.SignOption
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *BatchAddIpResponse
		wantErr bool
	}{
		{
			name: "正常流程",
			fields: fields{
				Client: &bce.Client{
					Config: &bce.Config{
						Credentials: &bce.Credentials{
							AccessKeyID:     "",
							SecretAccessKey: "",
						},
						Region:         "",
						Endpoint:       "",
						APIVersion:     "",
						Protocol:       "",
						UserAgent:      "",
						ProxyHost:      "",
						ProxyPort:      0,
						MaxConnections: 0,
						Timeout:        0,
						RetryPolicy:    nil,
						Checksum:       false,
					},
				},
			},
			args: args{
				ctx: context.TODO(),
				args: &BatchAddIpArgs{
					InstanceId:                     "i-xxx",
					SecondaryPrivateIpAddressCount: 2,
					ClientToken:                    "",
				},
				signOpt: nil,
			},
			want: &BatchAddIpResponse{
				PrivateIps: []string{
					"*********",
					"*********",
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := bccClient
			got, err := c.BatchAddIP(tt.args.ctx, tt.args.args, tt.args.signOpt)
			if (err != nil) != tt.wantErr {
				t.Errorf("Client.BatchAddIP() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Client.BatchAddIP() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestClient_BatchDelIP(t *testing.T) {
	setupTestEnv(InstancesHandler())
	defer tearDownTestEnv()

	type fields struct {
		Client *bce.Client
	}
	type args struct {
		ctx     context.Context
		args    *BatchDelIpArgs
		signOpt *bce.SignOption
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "正常流程",
			fields: fields{
				Client: &bce.Client{
					Config: &bce.Config{
						Credentials: &bce.Credentials{
							AccessKeyID:     "",
							SecretAccessKey: "",
						},
						Region:         "",
						Endpoint:       "",
						APIVersion:     "",
						Protocol:       "",
						UserAgent:      "",
						ProxyHost:      "",
						ProxyPort:      0,
						MaxConnections: 0,
						Timeout:        0,
						RetryPolicy:    nil,
						Checksum:       false,
					},
				},
			},
			args: args{
				ctx: context.TODO(),
				args: &BatchDelIpArgs{
					InstanceId:  "",
					PrivateIps:  []string{},
					ClientToken: "",
				},
				signOpt: nil,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := bccClient
			if err := c.BatchDelIP(tt.args.ctx, tt.args.args, tt.args.signOpt); (err != nil) != tt.wantErr {
				t.Errorf("Client.BatchDelIP() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
