package bcc

import (
	"context"

	bccapi "github.com/baidubce/bce-sdk-go/services/bcc/api"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/tag"
)

// go:generate mockgen -copyright_file=${GOPATH}/src/icode.baidu.com/baidu/bci2/bci-cni-driver/hack/boilerplate.go.txt -destination=./mock/mock_client.go -package=mock icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bcc Interface

// Interface 定义 BCC OpenAPI
type Interface interface {
	SetDebug(debug bool)

	// BCC
	CreateInstance(ctx context.Context, args *bccapi.CreateInstanceArgs, option *bce.SignOption) (*bccapi.CreateInstanceResult, error) // 复用官网数据结构
	CreateInstanceBySpec(ctx context.Context, args *CreateInstanceBySpecArgsShell, clientToken string, option *bce.SignOption) (*bccapi.CreateInstanceBySpecResult, error)
	CreateBidInstance(ctx context.Context, args *bccapi.CreateInstanceArgs, clientToken string, option *bce.SignOption) (*bccapi.CreateInstanceResult, error)
	DescribeInstance(ctx context.Context, instanceID string, option *bce.SignOption) (*Instance, error)
	DescribeInstanceWithDeploySet(ctx context.Context, instanceID string, isDeploySet bool, option *bce.SignOption) (*Instance, error)
	ListInstances(ctx context.Context, option *bce.SignOption) ([]Instance, error)
	ListInstancesByIP(ctx context.Context, internalIP string, option *bce.SignOption) (*ListInstancesResponse, error)
	DeleteInstance(ctx context.Context, instanceID string, option *bce.SignOption) error
	DeleteInstanceWithArgs(ctx context.Context, instanceID string, args *DeleteInstanceArgs, option *bce.SignOption) error
	BatchDeleteInstance(ctx context.Context, args *BatchDeleteInstanceArgs, option *bce.SignOption) error

	// CDS
	ListVolumes(ctx context.Context, instanceID, zoneName string, option *bce.SignOption) (*ListVolumesResponse, error)
	CreateVolumes(ctx context.Context, args *CreateVolumeArgs, option *bce.SignOption) ([]string, error)
	AttachCDSVolume(ctx context.Context, args *AttachCDSVolumeArgs, option *bce.SignOption) (*VolumeAttachment, error)
	DetachCDSVolume(ctx context.Context, args *AttachCDSVolumeArgs, option *bce.SignOption) error
	DescribeVolume(ctx context.Context, id string, option *bce.SignOption) (*Volume, error)
	DeleteVolume(ctx context.Context, volumeId string, option *bce.SignOption) error
	CreateSnapshot(ctx context.Context, args *CreateSnapShotArgs, option *bce.SignOption) (string, error)
	DeleteSnapshot(ctx context.Context, snapShotId string, option *bce.SignOption) error
	DescribeSnapshot(ctx context.Context, id string, option *bce.SignOption) (*Snapshot, error)
	GetSnapshotList(ctx context.Context, volumeId string, option *bce.SignOption) ([]Snapshot, error)

	// 安全组
	GetSecurityGroups(ctx context.Context, request *GetSecurityGroupsRequest, option *bce.SignOption) (*GetSecurityGroupsResponse, error)
	CreateSecurityGroup(ctx context.Context, request *CreateSecurityGroupRequest, option *bce.SignOption) (*CreateSecurityGroupResponse, error)
	CreateSecurityGroupRule(ctx context.Context, request *CreateSecurityGroupRuleRequest, option *bce.SignOption) error
	DeleteSecurityGroup(ctx context.Context, securityGroupID string, option *bce.SignOption) error
	BindSecurityGroup(ctx context.Context, instanceID, securityGroupID string, option *bce.SignOption) error
	UnbindSecurityGroup(ctx context.Context, instanceID, securityGroupID string, option *bce.SignOption) error

	// Tag
	BindTags(ctx context.Context, instanceID string, tags []tag.Tag, option *bce.SignOption) error
	UnbindTags(ctx context.Context, instanceID string, tags []tag.Tag, option *bce.SignOption) error

	BatchAddIP(ctx context.Context, args *BatchAddIpArgs, signOpt *bce.SignOption) (*BatchAddIpResponse, error)
	BatchDelIP(ctx context.Context, args *BatchDelIpArgs, signOpt *bce.SignOption) error
}

type CreateInstanceBySpecArgsShell struct {
	bccapi.CreateInstanceBySpecArgs
	UserData                  string `json:"userData,omitempty"`
	EnterpriseSecurityGroupID string `json:"enterpriseSecurityGroupId,omitempty"`
}

// EIP 数据

type InternetChargeType string // https://cloud.baidu.com/doc/BCC/s/6jwvyo0q2#internetchargetype

const (
	InternetChargePrePay    InternetChargeType = "BANDWIDTH_PREPAID"          // 预付费按带宽结算
	InternetChargeTraffic   InternetChargeType = "TRAFFIC_POSTPAID_BY_HOUR"   // 流量按小时后付费
	InternetChargeBandwidth InternetChargeType = "BANDWIDTH_POSTPAID_BY_HOUR" // 带宽按小时后付费
)

// CDS 数据结构定义
type Billing struct {
	PaymentTiming string      `json:"paymentTiming"`
	BillingMethod string      `json:"billingMethod,omitempty"`
	Reservation   Reservation `json:"reservation,omitempty"`
}
type Reservation struct {
	ReservationLength   int    `json:"reservationLength"`
	ReservationTimeUnit string `json:"reservationTimeUnit"`
}

// 安全组数据结构定义

// GetSecurityGroupsRequest 查询安全组请求
type GetSecurityGroupsRequest struct {
	VPCID string `json:"vpcID"`
	// instance 长短 ID 都支持
	InstanceID string `json:"instanceId"`
	Marker     string `json:"marker"`
	MaxKeys    int64  `json:"maxKeys"`
}

// GetSecurityGroupsResponse 查询安全组返回
type GetSecurityGroupsResponse struct {
	NextMarker     string          `json:"nextMarker"`
	Marker         string          `json:"marker"`
	MaxKeys        int64           `json:"maxKeys"`
	IsTruncated    bool            `json:"isTruncated"`
	SecurityGroups []SecurityGroup `json:"securityGroups"`
}

// SecurityGroup 安全组详情
type SecurityGroup struct {
	ID    string              `json:"id"`
	Name  string              `json:"name"`
	Desc  string              `json:"desc"`
	VPCID string              `json:"vpcId"`
	Rules []SecurityGroupRule `json:"rules"`
}

// SecurityGroupRule 安全组规则
type SecurityGroupRule struct {
	SecurityGroupID string    `json:"securityGroupId"`
	EtherType       EtherType `json:"ethertype"`
	Direction       Direction `json:"direction"`
	Protocol        Protocol  `json:"protocol"`
	SourceGroupID   string    `json:"sourceGroupId"`
	SourceIP        string    `json:"sourceIp"`
	DestGroupID     string    `json:"destGroupId"`
	DestIP          string    `json:"destIp"`
	PortRange       string    `json:"portRange"`
	Remark          string    `json:"remark"`
}

type Direction string

const (
	DirectionIngress Direction = "ingress"
	DirectionEgress  Direction = "egress"
)

type EtherType string

const (
	EtherTypeIPv4 EtherType = "IPv4"
	EtherTypeIPv6 EtherType = "IPv6"
)

type Protocol string

// TODO: 列举其他 Protocol
const (
	ProtocolAll  Protocol = "all"
	ProtocolTCP  Protocol = "tcp"
	ProtocolUDP  Protocol = "udp"
	ProtocolICMP Protocol = "icmp"
)

// CreateSecurityGroupRequest 创建安全组请求
type CreateSecurityGroupRequest struct {
	VPCID       string              `json:"vpcId"`
	Name        string              `json:"name"`
	Description string              `json:"desc"`
	SGRules     []SecurityGroupRule `json:"rules"`
}

// CreateSecurityGroupResponse 创建安全组返回
type CreateSecurityGroupResponse struct {
	SecurityGroupID string `json:"securityGroupId"`
}

// CreateSecurityGroupRuleRequest 创建安全组规则请求
type CreateSecurityGroupRuleRequest struct {
	SecurityGroupID string            `json:"securityGroupId"`
	SGRule          SecurityGroupRule `json:"rule"`
}

type DeleteInstanceArgs struct {
	RelatedReleaseFlag    bool `json:"relatedReleaseFlag"`
	DeleteCdsSnapshotFlag bool `json:"deleteCdsSnapshotFlag"`
	BccRecycleFlag        bool `json:"bccRecycleFlag"`
	DeleteRelatedEnisFlag bool `json:"deleteRelatedEnisFlag"`
}

type BatchDeleteInstanceArgs struct {
	RelatedReleaseFlag    bool     `json:"relatedRelease"`
	DeleteCdsSnapshotFlag bool     `json:"deleteCdsSnapshotFlag"` // bcc 还不支持
	BccRecycleFlag        bool     `json:"bccRecycleFlag"`
	DeleteRelatedEnisFlag bool     `json:"deleteRelatedEnisFlag"` // bcc 还不支持
	InstanceIDs           []string `json:"instanceIds"`
}

func removeInvalidChar(data []byte) []byte {
	for i, ch := range data {

		switch ch {
		case '\r':
			data[i] = ' '
		case '\n':
			data[i] = ' '
		case '\t':
			data[i] = ' '
		}
	}

	return data
}
