/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package bcc

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/logger"
)

type BatchAddIpArgs struct {
	InstanceId                     string   `json:"instanceId"`
	PrivateIps                     []string `json:"privateIps"`
	SecondaryPrivateIpAddressCount int      `json:"secondaryPrivateIpAddressCount"`
	ClientToken                    string   `json:"-"`
}

type BatchAddIpResponse struct {
	PrivateIps []string `json:"privateIps"`
}

type BatchDelIpArgs struct {
	InstanceId  string   `json:"instanceId"`
	PrivateIps  []string `json:"privateIps"`
	ClientToken string   `json:"-"`
}

const (
	URI_PREFIXV3 = "v3"
	URI_PREFIXV2 = "v2"
	URI_PREFIXV1 = "v1"

	REQUEST_INSTANCE_URI = "/instance"

	REQUEST_BATCHADDIP_URI = "/batchAddIp"
	REQUEST_BATCHDELIP_URI = "/batchDelIp"
)

func getBatchAddIpUri() string {
	return URI_PREFIXV2 + REQUEST_INSTANCE_URI + REQUEST_BATCHADDIP_URI
}

func getBatchDelIpUri() string {
	return URI_PREFIXV2 + REQUEST_INSTANCE_URI + REQUEST_BATCHDELIP_URI
}

// BatchAddIP - Add ips to instance
//
// PARAMS:
//      - args: the arguments to add ips to bcc instance
// RETURNS:
//     - error: nil if success otherwise the specific error
func (c *Client) BatchAddIP(ctx context.Context, args *BatchAddIpArgs, signOpt *bce.SignOption) (*BatchAddIpResponse, error) {
	ctx = logger.EnsureTraceIDInCtx(ctx)

	if args == nil {
		return nil, errors.New("BatchAddIP: args cannot be nil")
	}

	content, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("PUT", c.GetURL(getBatchAddIpUri(), nil), bytes.NewBuffer(content))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, signOpt)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	batchAddResp := &BatchAddIpResponse{}
	err = json.Unmarshal(bodyContent, batchAddResp)
	if err != nil {
		return nil, err
	}

	return batchAddResp, nil
}

// BatchDelIP - Delete ips of instance
//
// PARAMS:
//      - args: the arguments to add ips to bcc instance
// RETURNS:
//     - error: nil if success otherwise the specific error
func (c *Client) BatchDelIP(ctx context.Context, args *BatchDelIpArgs, signOpt *bce.SignOption) error {
	ctx = logger.EnsureTraceIDInCtx(ctx)

	if args == nil {
		return errors.New("BatchDelIP: args cannot be nil")
	}

	content, err := json.Marshal(args)
	if err != nil {
		return err
	}

	req, err := bce.NewRequest("PUT", c.GetURL(getBatchDelIpUri(), nil), bytes.NewBuffer(content))
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, signOpt)
	if err != nil {
		return err
	}

	_, err = resp.GetBodyContent()
	if err != nil {
		return err
	}

	return nil
}
