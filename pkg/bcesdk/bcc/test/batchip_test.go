/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package main

import (
	"bufio"
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"testing"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bcc"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
)

var (
	ak = "x"
	sk = "x"

	instanceId = "i-riCxPyQj"
)

func test_main(t *testing.T) {
	client := bcc.NewClient(&bce.Config{
		Credentials: bce.NewCredentials(ak, sk),
		Region:      "bj",
	})

	resp, err := client.BatchAddIP(context.TODO(), &bcc.BatchAddIpArgs{
		InstanceId:                     instanceId,
		SecondaryPrivateIpAddressCount: 2,
	}, nil)
	if err != nil {
		log.Fatalln("BatchAddIP", err)
	}

	data, _ := json.MarshalIndent(resp, "", " ")
	log.Println(string(data))

	prompt()

	client.BatchDelIP(context.TODO(), &bcc.BatchDelIpArgs{
		InstanceId: instanceId,
		PrivateIps: resp.PrivateIps,
	}, nil)
	if err != nil {
		log.Fatalln("BatchDelIP", err)
	}
}

func prompt() {
	fmt.Printf("-> Press Return key to continue.")
	scanner := bufio.NewScanner(os.Stdin)
	for scanner.Scan() {
		break
	}
	if err := scanner.Err(); err != nil {
		panic(err)
	}
	fmt.Println()
}
