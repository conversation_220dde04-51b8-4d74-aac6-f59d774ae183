package eip

import (
	"context"
	"errors"
	"fmt"
	"math/rand"
	"time"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
)

// FakeClient for unit test
type FakeClient struct {
	EIPMap        map[string]*EIP
	RecycleEIPMap map[string]*RecycleEIP
}

// NewFakeClient for EIP fake client
func NewFakeClient() *FakeClient {
	return &FakeClient{
		EIPMap:        map[string]*EIP{},
		RecycleEIPMap: map[string]*RecycleEIP{},
	}
}

// SetDebug 开启调试
func (f *FakeClient) SetDebug(debug bool) {
	return
}

// CreateEIP create EIP
func (f *FakeClient) CreateEIP(ctx context.Context, args *CreateEIPArgs, option *bce.SignOption) (string, error) {
	if args == nil {
		return "", errors.New("CreateEIP faile: args is nil")
	}

	eip := &EIP{
		Name:            args.Name,
		Status:          EIPAvailable,
		BandwidthInMbps: args.BandwidthInMbps,
	}

	for {
		ip := generateRandomEIP()
		if _, ok := f.EIPMap[ip]; !ok {
			eip.EIP = ip
			f.EIPMap[ip] = eip
			break
		}
	}

	return eip.EIP, nil
}

// BindEIP bind eip with instance
func (f *FakeClient) BindEIP(ctx context.Context, eip string, args *BindEIPArgs, option *bce.SignOption) error {
	e, ok := f.EIPMap[eip]
	if !ok {
		return fmt.Errorf("EIP %s not exist", eip)
	}

	if len(e.InstanceType) != 0 || len(e.InstanceID) != 0 {
		return fmt.Errorf("EIP %s has already been binded", eip)
	}

	e.Status = EIPBinded
	e.InstanceType = args.InstanceType
	e.InstanceID = args.InstanceID

	return nil
}

// UnbindEIP unbind EIP with instance
// If eip.status == EIPAvailable, return nil
func (f *FakeClient) UnbindEIP(ctx context.Context, eip string, option *bce.SignOption) error {
	e, ok := f.EIPMap[eip]
	if !ok {
		return fmt.Errorf("EIP %s not exist", eip)
	}

	e.Status = EIPAvailable
	e.InstanceType = ""
	e.InstanceID = ""

	return nil
}

// DeleteEIP delete pointed EIP
func (f *FakeClient) DeleteEIP(ctx context.Context, eip string, option *bce.SignOption) error {
	if _, ok := f.EIPMap[eip]; ok {
		delete(f.EIPMap, eip)
		return nil
	}

	return fmt.Errorf("DeleteEIP %s not exist", eip)
}

// DeleteEIPWithDeletionOption delete pointed EIP
func (f *FakeClient) DeleteEIPWithDeletionOption(ctx context.Context, eip string, deleteOption EIPDeletionOption, option *bce.SignOption) error {
	if _, ok := f.EIPMap[eip]; ok {
		delete(f.EIPMap, eip)
		return nil
	}

	return fmt.Errorf("DeleteEIP %s not exist", eip)
}

// ResizeEIP resize EIP bindwidth
func (f *FakeClient) ResizeEIP(ctx context.Context, eip string, args *ResizeEIPArgs, option *bce.SignOption) error {
	if args == nil {
		return errors.New("ResizeEIP failed: args is nil")
	}

	if args.BandwidthInMbps < 1 || args.BandwidthInMbps > 1000 {
		return fmt.Errorf("ResizeEIP failed: %d out of range", args.BandwidthInMbps)
	}

	e, ok := f.EIPMap[eip]
	if !ok {
		return fmt.Errorf("EIP %s not exist", eip)
	}

	e.BandwidthInMbps = args.BandwidthInMbps

	return nil
}

// GetEIPs to get eips by condition
func (f *FakeClient) GetEIPs(ctx context.Context, args *GetEIPsArgs, option *bce.SignOption) ([]*EIP, error) {
	result := []*EIP{}

	// Return all EIPs
	if args == nil || len(args.EIP) == 0 {
		for _, eip := range f.EIPMap {
			result = append(result, eip)
		}
		return result, nil
	}

	// Only process EIP
	if args != nil && len(args.EIP) != 0 {
		for _, eip := range f.EIPMap {
			if eip.EIP == args.EIP {
				result = append(result, eip)
				return result, nil
			}
		}
	}

	return []*EIP{}, nil
}

// GetRecycleEIPs to get recycle eips by condition
func (f *FakeClient) GetRecycleEIPs(ctx context.Context, args *GetEIPsArgs, option *bce.SignOption) ([]*RecycleEIP, error) {
	result := []*RecycleEIP{}

	// Return all EIPs
	if args == nil || len(args.EIP) == 0 {
		for _, eip := range f.RecycleEIPMap {
			result = append(result, eip)
		}
		return result, nil
	}

	// Only process EIP
	if args != nil && len(args.EIP) != 0 {
		for _, eip := range f.RecycleEIPMap {
			if eip.EIP == args.EIP {
				result = append(result, eip)
				return result, nil
			}
		}
	}

	return []*RecycleEIP{}, nil
}

// QueryEIPPrice to query EIP price with specified configuration
func (f *FakeClient) QueryEIPPrice(ctx context.Context, request *QueryEIPPriceRequest, option *bce.SignOption) (*QueryEIPPriceResponse, error) {
	return nil, nil
}

func (f *FakeClient) CreateEIPWithClientToken(ctx context.Context, args *CreateEIPArgs,
	clientToken string, option *bce.SignOption) (string, error) {
	if args == nil {
		return "", errors.New("CreateEIPWithClientToken failed: args is nil")
	}

	if len(clientToken) == 0 {
		return "", errors.New("CreateEIPWithClientToken failed: clientToken is nil")
	}

	eip := &EIP{
		Name:            args.Name,
		Status:          EIPAvailable,
		BandwidthInMbps: args.BandwidthInMbps,
	}

	for {
		ip := generateRandomEIP()
		if _, ok := f.EIPMap[ip]; !ok {
			eip.EIP = ip
			f.EIPMap[ip] = eip
			break
		}
	}

	return eip.EIP, nil
}

func generateRandomEIP() string {
	rand.Seed(time.Now().Unix())
	ip := fmt.Sprintf("100.%d.%d.%d", rand.Intn(255), rand.Intn(255), rand.Intn(255))
	return ip
}
