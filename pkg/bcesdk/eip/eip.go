package eip

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
)

func (args *CreateEIPArgs) validate() error {
	if args == nil {
		return errors.New("CreateEipArgs need args")
	}
	if args.BandwidthInMbps == 0 {
		return errors.New("CreateEipArgs need BandwidthInMbps")
	}
	if args.Billing == nil {
		return errors.New("CreateEipArgs need Billing")
	}
	return nil
}

func (c *Client) CreateEIPWithClientToken(ctx context.Context, args *CreateEIPArgs, clientToken string, option *bce.SignOption) (string, error) {
	err := args.validate()
	if err != nil {
		return "", err
	}

	params := map[string]string{
		"clientToken": clientToken,
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return "", err
	}

	req, err := bce.NewRequest("POST", c.GetURL("v1/eip", params), bytes.NewBuffer(postContent))
	if err != nil {
		return "", err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return "", err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return "", err
	}

	var eip *CreateEIPResponse
	err = json.Unmarshal(bodyContent, &eip)
	if err != nil {
		return "", err
	}

	return eip.EIP, nil
}

// CreateEIP for create EIP
func (c *Client) CreateEIP(ctx context.Context, args *CreateEIPArgs, option *bce.SignOption) (string, error) {
	err := args.validate()
	if err != nil {
		return "", err
	}

	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return "", err
	}

	req, err := bce.NewRequest("POST", c.GetURL("v1/eip", params), bytes.NewBuffer(postContent))
	if err != nil {
		return "", err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return "", err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return "", err
	}

	var blbsResp *CreateEIPResponse
	err = json.Unmarshal(bodyContent, &blbsResp)
	if err != nil {
		return "", err
	}

	return blbsResp.EIP, nil
}

// ResizeEIP to resize EIP
func (c *Client) ResizeEIP(ctx context.Context, eip string, args *ResizeEIPArgs, option *bce.SignOption) error {
	if args == nil {
		return errors.New("ResizeEIP failed: args is nil")
	}

	params := map[string]string{
		"resize":      "",
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("v1/eip/%s", eip)
	req, err := bce.NewRequest("PUT", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	return nil
}

func (args *BindEIPArgs) validate() error {
	if args == nil {
		return errors.New("BindEip need args")
	}

	if len(args.InstanceType) == 0 {
		return errors.New("BindEip need InstanceType")
	}

	if len(args.InstanceID) == 0 {
		return errors.New("BindEip need InstanceId")
	}
	return nil
}

// BindEIP to Bind EIP with BLB, BCC ..
func (c *Client) BindEIP(ctx context.Context, eip string, args *BindEIPArgs, option *bce.SignOption) error {
	err := args.validate()
	if err != nil {
		return err
	}
	params := map[string]string{
		"bind":        "",
		"clientToken": c.GenerateClientToken(),
	}
	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}
	req, err := bce.NewRequest("PUT", c.GetURL("v1/eip"+"/"+eip, params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}
	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}
	return nil
}

// UnbindEIP to Unbind EIP with BLB, BCC ...
func (c *Client) UnbindEIP(ctx context.Context, eip string, option *bce.SignOption) error {
	params := map[string]string{
		"unbind":      "",
		"clientToken": c.GenerateClientToken(),
	}

	req, err := bce.NewRequest("PUT", c.GetURL("v1/eip"+"/"+eip, params), nil)
	if err != nil {
		return err
	}
	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}
	return nil
}

// DeleteEIP delete EIP
func (c *Client) DeleteEIP(ctx context.Context, eip string, option *bce.SignOption) error {
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	url := fmt.Sprintf("v1/eip/%s", eip)
	req, err := bce.NewRequest("DELETE", c.GetURL(url, params), nil)
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	return nil
}

// DeleteEIPWithDeletionOption delete EIP with delete option
func (c *Client) DeleteEIPWithDeletionOption(ctx context.Context, eip string, deleteOption EIPDeletionOption, option *bce.SignOption) error {
	params := map[string]string{
		"clientToken":      c.GenerateClientToken(),
		"releaseToRecycle": strconv.FormatBool(deleteOption.ReleaseToRecycle),
	}

	url := fmt.Sprintf("v1/eip/%s", eip)
	req, err := bce.NewRequest("DELETE", c.GetURL(url, params), nil)
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	return nil
}

// GetEIPs to get eip list
func (c *Client) GetEIPs(ctx context.Context, args *GetEIPsArgs, option *bce.SignOption) ([]*EIP, error) {
	if args == nil {
		args = &GetEIPsArgs{}
	}

	params := map[string]string{}

	if len(args.EIP) != 0 {
		params["eip"] = args.EIP
	}

	if len(args.InstanceType) != 0 {
		params["instanceType"] = string(args.InstanceType)
	}

	if len(args.InstanceID) != 0 {
		params["instanceId"] = args.InstanceID
	}

	if len(args.Status) != 0 {
		params["status"] = string(args.Status)
	}

	if len(args.Marker) != 0 {
		params["marker"] = args.Marker
	}

	if args.MaxKeys != 0 {
		params["maxKeys"] = fmt.Sprintf("%d", args.MaxKeys)
	}

	if len(args.Name) != 0 {
		params["name"] = fmt.Sprintf("%s", args.Name)
	}

	req, err := bce.NewRequest("GET", c.GetURL("v1/eip", params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()

	if err != nil {
		return nil, err
	}

	var blbsResp *GetEIPsResponse
	err = json.Unmarshal(bodyContent, &blbsResp)
	if err != nil {
		return nil, err
	}

	return blbsResp.EIPList, nil
}

// GetRecycleEIPs to get recycle eip list
func (c *Client) GetRecycleEIPs(ctx context.Context, args *GetEIPsArgs, option *bce.SignOption) ([]*RecycleEIP, error) {
	if args == nil {
		args = &GetEIPsArgs{}
	}

	params := map[string]string{}

	if len(args.EIP) != 0 {
		params["eip"] = args.EIP
	}

	if len(args.InstanceType) != 0 {
		params["instanceType"] = string(args.InstanceType)
	}

	if len(args.InstanceID) != 0 {
		params["instanceId"] = args.InstanceID
	}

	if len(args.Status) != 0 {
		params["status"] = string(args.Status)
	}

	if len(args.Marker) != 0 {
		params["marker"] = args.Marker
	}

	if args.MaxKeys != 0 {
		params["maxKeys"] = fmt.Sprintf("%d", args.MaxKeys)
	}

	if len(args.Name) != 0 {
		params["name"] = fmt.Sprintf("%s", args.Name)
	}

	req, err := bce.NewRequest("GET", c.GetURL("v1/eip/recycle", params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()

	if err != nil {
		return nil, err
	}

	var blbsResp *GetRecycleEIPsResponse
	err = json.Unmarshal(bodyContent, &blbsResp)
	if err != nil {
		return nil, err
	}

	return blbsResp.EIPList, nil
}
