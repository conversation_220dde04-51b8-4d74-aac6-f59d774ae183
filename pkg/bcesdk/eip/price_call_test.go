package eip

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
)

func testQueryEIPPrice(t *testing.T) {
	accessKeyID := "********************************"
	secretAccessKey := "6b109261727a4fee8fc5db4bf5ec6c51"
	region := "sandbox"

	eipclient := NewClient(&bce.Config{
		Credentials: bce.NewCredentials(accessKeyID, secretAccessKey),
		Checksum:    true,
		Timeout:     30 * time.Second,
		Region:      region,
		Endpoint:    Endpoint[region],
	})
	eipclient.SetDebug(true)

	req := &QueryEIPPriceRequest{
		BandwidthInMbps: 1,
		PurchaseNum:     1,
		Billing: Billing{
			PaymentTiming: PaymentTimingPostpaid,
			BillingMethod: BillingMethodByBandwidth,
			// Reservation: &Reservation{
			//	ReservationLength:   0,
			//	ReservationTimeUnit: "month",
			// },
		},
	}

	resp, err := eipclient.QueryEIPPrice(context.TODO(), req, NewSignOption())
	if err != nil {
		t.Errorf("QueryEIPPrice failed: %v", err)
	}

	if str, err := json.Marshal(resp); err == nil {
		t.Logf("QueryEIPPrice success: %v", string(str))
	}
}
