package eip

import (
	"context"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
)

// go:generate mockgen -copyright_file=${GOPATH}/src/icode.baidu.com/baidu/bci2/bci-cni-driver/hack/boilerplate.go.txt -destination=./mock/mock_client.go -package=mock icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/eip Interface

// Interface for EIP SDK interface
type Interface interface {
	SetDebug(debug bool)
	CreateEIP(ctx context.Context, args *CreateEIPArgs, option *bce.SignOption) (string, error)
	CreateEIPWithClientToken(ctx context.Context, args *CreateEIPArgs, clientToken string, option *bce.SignOption) (string, error)
	ResizeEIP(ctx context.Context, eip string, args *ResizeEIPArgs, option *bce.SignOption) error
	BindEIP(ctx context.Context, eip string, args *BindEIPArgs, option *bce.SignOption) error
	UnbindEIP(ctx context.Context, eip string, option *bce.SignOption) error
	DeleteEIP(ctx context.Context, eip string, option *bce.SignOption) error
	DeleteEIPWithDeletionOption(ctx context.Context, eip string, deleteOption EIPDeletionOption, option *bce.SignOption) error
	GetEIPs(ctx context.Context, args *GetEIPsArgs, option *bce.SignOption) ([]*EIP, error)
	GetRecycleEIPs(ctx context.Context, args *GetEIPsArgs, option *bce.SignOption) ([]*RecycleEIP, error)

	// 查询 EIP 价格
	QueryEIPPrice(ctx context.Context, request *QueryEIPPriceRequest, option *bce.SignOption) (*QueryEIPPriceResponse, error)
}

// EIP struct fot EIP
type EIP struct {
	Name            string          `json:"name"`
	EIP             string          `json:"eip"`
	EIPID           string          `json:"eipId"`
	Status          Status          `json:"status"`
	EIPInstanceType EIPInstanceType `json:"eipInstanceType"`
	InstanceType    InstanceType    `json:"instanceType"`
	InstanceID      string          `json:"instanceId"`
	InstanceIP      string          `json:"instanceIp"`
	ShareGroupID    string          `json:"shareGroupId"`
	RouteType       string          `json:"routeType"`
	BandwidthInMbps int             `json:"bandwidthInMbps"`
	PaymentTiming   string          `json:"paymentTiming"`
	BillingMethod   string          `json:"billingMethod"`
	CreateTime      string          `json:"createTime"`
	ExpireTime      string          `json:"expireTime"`
}

// RecycleEIP struct for recycle EIP
type RecycleEIP struct {
	Name                string       `json:"name"`
	EIP                 string       `json:"eip"`
	EIPID               string       `json:"eipId"`
	Status              Status       `json:"status"`
	RouteType           PurchaseType `json:"routeType,omitempty"`
	BandwidthInMbps     int          `json:"bandwidthInMbps"`
	PaymentTiming       string       `json:"paymentTiming"`
	BillingMethod       string       `json:"billingMethod"`
	RecycleTime         string       `json:"recycleTime"`
	ScheduledDeleteTime string       `json:"scheduledDeleteTime"`
}

// Status for EIP status
type Status string

const (
	// EIPCreating EIP creating
	EIPCreating Status = "creating"

	// EIPAvailable EIP available
	EIPAvailable Status = "available"

	// EIPBinded EIP binded
	EIPBinded Status = "binded"

	// EIPBinding EIP binding
	EIPBinding Status = "binding"

	// EIPUnBinding EIP unbinding
	EIPUnBinding Status = "unbinding"

	// EIPUpdating EIP updating
	EIPUpdating Status = "updating"

	// EIPPaused EIP paused
	EIPPaused Status = "paused"

	// EIPUnavaliable EIP unavailable
	EIPUnavaliable Status = "unavailable"
)

// InstanceType for instance Type
type InstanceType string

const (
	// BCC instance type = "BCC"
	BCC InstanceType = "BCC"

	// BBC instance type = "BBC"
	BBC InstanceType = "BBC"

	// BLB instance type = "BLB"
	BLB InstanceType = "BLB"

	// VPN instance type = "VPN"
	VPN InstanceType = "VPN"

	// NAT instance type = "NAT"
	NAT InstanceType = "NAT"

	// ENI instance type = "ENI"
	ENI InstanceType = "ENI"
)

// EIPInstanceType for EIP Type
type EIPInstanceType string

const (
	// NORMAL for EIP Instance type = "Normal"
	NORMAL EIPInstanceType = "normal"

	// SHARED for EIP Instance type = "Shared"
	SHARED EIPInstanceType = "shared"
)

// Billing 付费方式、计费方式详情
type Billing struct {
	PaymentTiming PaymentTiming `json:"paymentTiming"`
	BillingMethod BillingMethod `json:"billingMethod"`
	Reservation   *Reservation  `json:"reservation"`
}

// PaymentTiming 付费时间选择
type PaymentTiming string

const (
	// PaymentTimingPrepaid 预付费
	PaymentTimingPrepaid PaymentTiming = "Prepaid"

	// PaymentTimingPostpaid 后付费
	PaymentTimingPostpaid PaymentTiming = "Postpaid"
)

// BillingMethod 计费方式
type BillingMethod string

const (
	// BillingMethodByTraffic 按照流量计费
	BillingMethodByTraffic BillingMethod = "ByTraffic"

	// BillingMethodByBandwidth 按带宽计费
	BillingMethodByBandwidth BillingMethod = "ByBandwidth"

	// PeakBandwidth_Percent_95_A  EIP BOS 合并计费 , 暂时没有 open api 对应的类型值
	PeakBandwidthPercent95A BillingMethod = "PeakBandwidth_Percent_95_A"
)

// Reservation - 预付费时长, 时长单位
type Reservation struct {
	ReservationLength   int    `json:"reservationLength"`
	ReservationTimeUnit string `json:"reservationTimeUnit"`
}

// CreateEIPArgs for create EIP args
type CreateEIPArgs struct {
	//  公网带宽，单位为Mbps。
	// 对于prepay以及bandwidth类型的EIP，限制为为1~200之间的整数，
	// 对于traffic类型的EIP，限制为1~200之前的整数。
	BandwidthInMbps int `json:"bandwidthInMbps"`

	Billing   *Billing     `json:"billing"`
	Name      string       `json:"name,omitempty"`
	RouteType PurchaseType `json:"routeType,omitempty"`
}

// CreateEIPResponse for create EIP response
type CreateEIPResponse struct {
	EIP string `json:"eip"`
}

// ResizeEIPArgs resize eip args
type ResizeEIPArgs struct {
	BandwidthInMbps int    `json:"newBandwidthInMbps"`
	IP              string `json:"-"`
}

// BindEIPArgs for bind EIP args
type BindEIPArgs struct {
	InstanceType InstanceType `json:"instanceType"`
	InstanceID   string       `json:"instanceId"`
	IP           string       `json:"-"`
}

type EIPDeletionOption struct {
	ReleaseToRecycle bool `json:"releaseToRecycle"` // 是否将指定EIP放入回收站，缺省值为false，代表将EIP直接释放。
}

// GetEIPsArgs for get eips args
type GetEIPsArgs struct {
	EIP          string       `json:"ip,omitempty"`
	Name         string       `json:"name,omitempty"`
	InstanceType InstanceType `json:"instanceType,omitempty"`
	InstanceID   string       `json:"instanceId,omitempty"`
	Status       Status       `json:"status,omitempty"`
	Marker       string       `json:"marker,omitempty"`
	MaxKeys      int          `json:"maxKeys,omitempty"`
}

// GetEIPsResponse for get EIPss response
type GetEIPsResponse struct {
	EIPList     []*EIP `json:"eipList"`
	Marker      string `json:"marker"`
	IsTruncated bool   `json:"isTruncated"`
	NextMarker  string `json:"nextMarker"`
	MaxKeys     int    `json:"maxKeys"`
}

// GetRecycleEIPsResponse for get RecycleEIPs response
type GetRecycleEIPsResponse struct {
	EIPList     []*RecycleEIP `json:"eipList"`
	Marker      string        `json:"marker"`
	IsTruncated bool          `json:"isTruncated"`
	NextMarker  string        `json:"nextMarker"`
	MaxKeys     int           `json:"maxKeys"`
}

// QueryEIPPriceRequest - EIP 询价请求
type QueryEIPPriceRequest struct {
	BandwidthInMbps int     `json:"bandwidthInMbps"`
	Billing         Billing `json:"billing"`
	PurchaseNum     int     `json:"count"`
}

// QueryEIPPriceResponse - EIP 询价返回
type QueryEIPPriceResponse struct {
	Price Price `json:"prices"`
}

// Price - EIP 价格
type Price struct {
	// EIPPostpaidBandwidthPrice 后付费 EIP 的带宽价格 (如: 0.00032/minute)
	EIPPostpaidBandwidthPrice string `json:"configPrice"`

	// EIPPostpaidNetTrafficPrice 后付费且按流量计费的 EIP 的流量价格 (如: 0.76/GB)
	EIPPostpaidNetTrafficPrice string `json:"netrafficPrice"`

	// EIPPrepaidPrice 预付费 EIP 指定时长的价格 (如: 23.00000)
	EIPPrepaidPrice string `json:"purchasePrice"`
}

type PurchaseType string

const (
	PurchaseTypeBGP         PurchaseType = "BGP"
	PurchaseTypeBGP_S       PurchaseType = "BGP_S"
	PurchaseTypeChinaMobile PurchaseType = "ChinaMobile"
	PurchaseTypeChinaTelcom PurchaseType = "ChinaTelcom"
	PurchaseTypeChinaUnicom PurchaseType = "ChinaUnicom"
	PurchaseTypeStatic      PurchaseType = "Static"
)
