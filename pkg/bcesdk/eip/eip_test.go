package eip

import (
	"context"
	"fmt"
	"testing"
	"time"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
)

const (
	ak       = "********************************"
	sk       = "xxx"
	region   = "sz"
	endpoint = "eip.su.baidubce.com"
)

func TestCreateEip(t *testing.T) {
	bill := &Billing{
		PaymentTiming: "Postpaid",
		BillingMethod: "ByTraffic",
	}
	args := &CreateEIPArgs{
		BandwidthInMbps: 998,
		Billing:         bill,
		Name:            "k8stestcgy",
	}
	ip, err := eipClient.CreateEIP(context.Background(), args, nil)
	if err != nil {
		t.Error(err)
	}
	if ip != "*************" {
		t.Error("ip error")
	}
}

var expectResizeEip = &ResizeEIPArgs{
	BandwidthInMbps: 111,
}

func TestResizeEip(t *testing.T) {
	err := eipClient.ResizeEIP(context.Background(), "**************", expectResizeEip, nil)
	if err != nil {
		t.Error(err)
	}
}

func TestBindEip(t *testing.T) {
	if err := eipClient.BindEIP(context.Background(), "*************", &BindEIPArgs{
		InstanceType: "BCC",
		InstanceID:   "i-VAEyKKTh",
	}, nil); err != nil {
		t.Error(err)
	}
}

func TestUnbindEip(t *testing.T) {
	if err := eipClient.UnbindEIP(context.Background(), "*************", nil); err != nil {
		t.Error(err)
	}
}

func TestDeleteEip(t *testing.T) {
	err := eipClient.DeleteEIP(context.Background(), "*************", nil)
	if err != nil {
		t.Error(err)
	}
}

func TestDeleteEIPWithDeleteOption(t *testing.T) {
	err := eipClient.DeleteEIPWithDeletionOption(context.Background(), "*************", EIPDeletionOption{ReleaseToRecycle: true}, nil)
	if err != nil {
		t.Error(err)
	}
}

func TestGetEips(t *testing.T) {
	eips, err := eipClient.GetEIPs(context.Background(), nil, nil)
	if err != nil {
		t.Error(err)
	}

	for _, eip := range eips {
		if eip.EIP != "*************" && eip.EIP != "*************" {
			t.Fatal("eip errpr")
		}
	}
}

func TestGetRecycleEips(t *testing.T) {
	eips, err := eipClient.GetRecycleEIPs(context.Background(), &GetEIPsArgs{EIP: "***********"}, nil)
	if err != nil {
		t.Error(err)
	}

	for _, eip := range eips {
		if eip.EIP != "***********" {
			t.Fatal("eip errpr")
		}
	}
}

func testBindEIP(t *testing.T) {
	// Create bce.Client on sandbox
	ak := "********************************"
	sk := "xxx"
	region := "sz"
	endpoint := "eip.su.baidubce.com"

	c := newAppEIPClient(ak, sk, region, endpoint)

	// Bind EIP
	if err := c.BindEIP(context.Background(), "*************", &BindEIPArgs{
		InstanceType: BLB,
		InstanceID:   "lb-ff96b880",
	}, nil); err != nil {
		t.Errorf("BindEIP failed: %v", err)
	}

	if err := c.UnbindEIP(context.Background(), "*************", nil); err != nil {
		t.Errorf("UnBindEIP failed: %v", err)
	}
}

func testGetEIPs(t *testing.T) {
	ctx := context.Background()

	// Init BCE Client
	c := newClient(ak, sk, region, endpoint)
	c.SetDebug(true)

	// Create BLB
	eips, err := c.GetEIPs(ctx, &GetEIPsArgs{}, nil)
	if err != nil {
		t.Errorf("GetEIPs: %v", err)
		return
	}

	t.Logf(fmt.Sprintf("%v", eips))
}

func testResizeEIP(t *testing.T) {
	ctx := context.Background()

	// Init BCE Client
	c := newClient(ak, sk, region, endpoint)
	c.SetDebug(true)

	// Create BLB
	if err := c.ResizeEIP(ctx, "************", &ResizeEIPArgs{
		BandwidthInMbps: 100,
	}, nil); err != nil {
		t.Errorf("ResizeEIPArgs failed: %v", err)
	}
}

func testUnbindingEIP(t *testing.T) {
	ctx := context.Background()

	// Init BCE Client
	c := newClient(ak, sk, region, endpoint)
	c.SetDebug(true)

	// Create BLB
	if err := c.UnbindEIP(ctx, "***********", nil); err != nil {
		t.Errorf("UnbindEIP failed: %v", err)
	}
}

func newClient(accessKeyID, secretAccessKey, region, endpoint string) *Client {
	return NewClient(&bce.Config{
		Credentials: bce.NewCredentials(accessKeyID, secretAccessKey),
		Checksum:    true,
		Timeout:     30 * time.Second,
		Region:      region,
		Endpoint:    endpoint,
	})
}
