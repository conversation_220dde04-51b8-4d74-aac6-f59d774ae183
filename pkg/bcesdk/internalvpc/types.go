/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package internalvpc

import (
	"context"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
)

// go:generate mockgen -destination=./mock/mock_client.go -package=mock icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/internalvpc Interface

// Interface 定义 internal vpc 相关接口
type Interface interface {
	MapByLongID(ctx context.Context, longIDs []string, serviceType int, option *bce.SignOption) (map[string]string, error)
	MapByShortID(ctx context.Context, shortIDs []string, option *bce.SignOption) (map[string]string, error)

	GetSubnetByID(ctx context.Context, shortID string, option *bce.SignOption) (*Subnet, error)

	GetENIs(ctx context.Context, request *GetENIsRequest, option *bce.SignOption) (*GetENIsResponse, error)
	AttachENI(ctx context.Context, eniID, deviceID string, option *bce.SignOption) error
	DetachENI(ctx context.Context, eniID, deviceID string, option *bce.SignOption) error
	DeleteENI(ctx context.Context, eniID string, option *bce.SignOption) error
}

// ================== ID map interface ==================
type LongIDRequest struct {
	LongIDs []string `json:"longIds"`
	Type    int      `json:"type"`
}

type ShortIDRequest struct {
	ShortIDs []string `json:"shortIds"`
}
type IDMapperResponse struct {
	Map map[string]string `json:"map"`
}

type SubnetResponse struct {
	Subnet *Subnet `json:"subnet"`
}

// ================== subnet interface ==================
type Subnet struct {
	SubnetID       string        `json:"shortId"`
	SubnetUUID     string        `json:"subnetId"`
	SubnetName     string        `json:"name"`
	SubnetType     int           `json:"subnetType"`
	Description    string        `json:"description"`
	SubnetCIDR     string        `json:"cidr"`
	SubnetCIDRIPv6 string        `json:"ipv6Cidr"`
	AvailableZone  AvailableZone `json:"az"`
	VPCID          string        `json:"vpcShortId"`
	VPCUUID        string        `json:"vpcId"`
	CreateTime     string        `json:"createdTime"`
	UpdatedTime    string        `json:"updatedTime"`
}

// AvailableZone 可用区
type AvailableZone string

const (
	// ZoneA 可用区 A
	ZoneA AvailableZone = "zoneA"

	// ZoneB 可用区 B
	ZoneB AvailableZone = "zoneB"

	// ZoneC 可用区 C
	ZoneC AvailableZone = "zoneC"

	// ZoneD 可用区 D
	ZoneD AvailableZone = "zoneD"

	// ZoneE 可用区 E
	ZoneE AvailableZone = "zoneE"

	// ZoneF 可用区 F
	ZoneF AvailableZone = "zoneF"
)

// ================== ENI interface ==================
type GetENIsRequest struct {
	PageNo   int    `json:"pageNo"`
	PageSize int    `json:"pageSize"`
	VPCUUID  string `json:"vpcId"`
}

type GetENIsResponse struct {
	OrderBy    string `json:"orderBy"`
	Order      string `json:"order"`
	PageNo     int    `json:"pageNo"`
	PageSize   int    `json:"pageSize"`
	TotalCount int    `json:"totalCount"`
	ENIs       []ENI  `json:"result"`
}

type ENI struct {
	ENIID       string    `json:"eniId"`
	ENIUUID     string    `json:"eniUuid"`
	ENIName     string    `json:"name"`
	ENIType     ENIType   `json:"type"`
	VPCID       string    `json:"vpcShortId"`
	VPCUUID     string    `json:"vpcId"`
	SubnetUUID  string    `json:"subnetId"`
	DeviceID    string    `json:"deviceId"`
	ENIStatus   ENIStatus `json:"status"`
	Description string    `json:"description"`
}

type ENIType string

const (
	ENITypeSecondary ENIType = "secondary"
)

type ENIStatus string

const (
	ENIStatusAvailable = "available"
	ENIStatusInuse     = "inuse"
	ENIStatusAttaching = "attaching"
	ENIStatusDetaching = "detaching"
	ENIStatusDeleting  = "deleting"
)
