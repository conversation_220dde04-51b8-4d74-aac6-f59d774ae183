/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package internalvpc

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
)

func (c *Client) MapByLongID(ctx context.Context, longIDs []string, serviceType int, option *bce.SignOption) (map[string]string, error) {
	params := map[string]string{}

	var request LongIDRequest
	request.Type = serviceType
	request.LongIDs = longIDs
	postContent, err := json.Marshal(request)
	if err != nil {
		return nil, err
	}

	url := fmt.Sprintf("v1/api/logical/network/id/long")
	req, err := bce.NewRequest("POST", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	respContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var resultMap IDMapperResponse
	err = json.Unmarshal(respContent, &resultMap)
	if err != nil {
		return nil, err
	}

	return resultMap.Map, err
}

func (c *Client) MapByShortID(ctx context.Context, shortIDs []string, option *bce.SignOption) (map[string]string, error) {
	params := map[string]string{}

	var request ShortIDRequest
	request.ShortIDs = shortIDs
	postContent, err := json.Marshal(request)
	if err != nil {
		return nil, err
	}

	url := fmt.Sprintf("v1/api/logical/network/id/short")
	req, err := bce.NewRequest("POST", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	respContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var resultMap IDMapperResponse
	err = json.Unmarshal(respContent, &resultMap)
	if err != nil {
		return nil, err
	}

	return resultMap.Map, nil
}
