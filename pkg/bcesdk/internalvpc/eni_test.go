/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package internalvpc

import (
	"context"
	"io/ioutil"
	"net/http"
	"reflect"
	"strings"
	"testing"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
)

func TestClient_GetENIs(t *testing.T) {
	type args struct {
		ctx     context.Context
		request *GetENIsRequest
		option  *bce.SignOption
	}
	tests := []struct {
		name    string
		c       *Client
		args    args
		want    *GetENIsResponse
		wantErr bool
	}{
		{
			name: "",
			c: &Client{
				Client: &bce.Client{
					Config: &bce.Config{
						Credentials: &bce.Credentials{
							AccessKeyID:     "",
							SecretAccessKey: "",
						},
						Region:         "",
						Endpoint:       "",
						APIVersion:     "",
						Protocol:       "",
						UserAgent:      "",
						ProxyHost:      "",
						ProxyPort:      0,
						MaxConnections: 0,
						Timeout:        0,
						RetryPolicy:    nil,
						Checksum:       false,
					},
					HTTPClient: &http.Client{
						Transport: RoundTripFunc(func(req *http.Request) *http.Response {
							return &http.Response{
								StatusCode: http.StatusOK,
								Body:       ioutil.NopCloser(strings.NewReader(`{}`)),
							}
						}),
						CheckRedirect: func(*http.Request, []*http.Request) error { panic("not implemented") },
						Jar:           nil,
						Timeout:       0,
					},
				},
			},
			args: args{
				ctx: context.TODO(),
				request: &GetENIsRequest{
					PageNo:   0,
					PageSize: 0,
					VPCUUID:  "",
				},
				option: nil,
			},
			want: &GetENIsResponse{
				OrderBy:    "",
				Order:      "",
				PageNo:     0,
				PageSize:   0,
				TotalCount: 0,
				ENIs:       nil,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.c.GetENIs(tt.args.ctx, tt.args.request, tt.args.option)
			if (err != nil) != tt.wantErr {
				t.Errorf("Client.GetENIs() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Client.GetENIs() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestClient_AttachENI(t *testing.T) {
	type args struct {
		ctx      context.Context
		eniID    string
		deviceID string
		option   *bce.SignOption
	}
	tests := []struct {
		name    string
		c       *Client
		args    args
		wantErr bool
	}{
		{
			name: "",
			c: &Client{
				Client: &bce.Client{
					Config: &bce.Config{
						Credentials: &bce.Credentials{
							AccessKeyID:     "",
							SecretAccessKey: "",
						},
						Region:         "",
						Endpoint:       "",
						APIVersion:     "",
						Protocol:       "",
						UserAgent:      "",
						ProxyHost:      "",
						ProxyPort:      0,
						MaxConnections: 0,
						Timeout:        0,
						RetryPolicy:    nil,
						Checksum:       false,
					},
					HTTPClient: &http.Client{
						Transport: RoundTripFunc(func(req *http.Request) *http.Response {
							return &http.Response{
								StatusCode: http.StatusOK,
								Body:       ioutil.NopCloser(strings.NewReader(`{}`)),
							}
						}),
						CheckRedirect: func(*http.Request, []*http.Request) error { panic("not implemented") },
						Jar:           nil,
						Timeout:       0,
					},
				},
			},
			args: args{
				ctx:      context.TODO(),
				eniID:    "eni-x",
				deviceID: "d-x",
				option:   nil,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := tt.c.AttachENI(tt.args.ctx, tt.args.eniID, tt.args.deviceID, tt.args.option); (err != nil) != tt.wantErr {
				t.Errorf("Client.AttachENI() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestClient_DetachENI(t *testing.T) {
	type args struct {
		ctx      context.Context
		eniID    string
		deviceID string
		option   *bce.SignOption
	}
	tests := []struct {
		name    string
		c       *Client
		args    args
		wantErr bool
	}{
		{
			name: "",
			c: &Client{
				Client: &bce.Client{
					Config: &bce.Config{
						Credentials: &bce.Credentials{
							AccessKeyID:     "",
							SecretAccessKey: "",
						},
						Region:         "",
						Endpoint:       "",
						APIVersion:     "",
						Protocol:       "",
						UserAgent:      "",
						ProxyHost:      "",
						ProxyPort:      0,
						MaxConnections: 0,
						Timeout:        0,
						RetryPolicy:    nil,
						Checksum:       false,
					},
					HTTPClient: &http.Client{
						Transport: RoundTripFunc(func(req *http.Request) *http.Response {
							return &http.Response{
								StatusCode: http.StatusOK,
								Body:       ioutil.NopCloser(strings.NewReader(`{}`)),
							}
						}),
						CheckRedirect: func(*http.Request, []*http.Request) error { panic("not implemented") },
						Jar:           nil,
						Timeout:       0,
					},
				},
			},
			args: args{
				ctx:      context.TODO(),
				eniID:    "eni-x",
				deviceID: "d-x",
				option:   nil,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := tt.c.DetachENI(tt.args.ctx, tt.args.eniID, tt.args.deviceID, tt.args.option); (err != nil) != tt.wantErr {
				t.Errorf("Client.DetachENI() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestClient_DeleteENI(t *testing.T) {
	type args struct {
		ctx    context.Context
		eniID  string
		option *bce.SignOption
	}
	tests := []struct {
		name    string
		c       *Client
		args    args
		wantErr bool
	}{
		{
			name: "",
			c: &Client{
				Client: &bce.Client{
					Config: &bce.Config{
						Credentials: &bce.Credentials{
							AccessKeyID:     "",
							SecretAccessKey: "",
						},
						Region:         "",
						Endpoint:       "",
						APIVersion:     "",
						Protocol:       "",
						UserAgent:      "",
						ProxyHost:      "",
						ProxyPort:      0,
						MaxConnections: 0,
						Timeout:        0,
						RetryPolicy:    nil,
						Checksum:       false,
					},
					HTTPClient: &http.Client{
						Transport: RoundTripFunc(func(req *http.Request) *http.Response {
							return &http.Response{
								StatusCode: http.StatusOK,
								Body:       ioutil.NopCloser(strings.NewReader(`{}`)),
							}
						}),
						CheckRedirect: func(*http.Request, []*http.Request) error { panic("not implemented") },
						Jar:           nil,
						Timeout:       0,
					},
				},
			},
			args: args{
				ctx:    context.TODO(),
				eniID:  "",
				option: nil,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := tt.c.DeleteENI(tt.args.ctx, tt.args.eniID, tt.args.option); (err != nil) != tt.wantErr {
				t.Errorf("Client.DeleteENI() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
