/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package internalvpc

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
)

func testGetENIs(t *testing.T) {
	accessKeyID := "e0c68be8495540f280b3e9ef03ec25d2"
	secretAccessKey := "b599d5f4f30e41bcb0e1482c9de65bd6"
	region := "gz"

	eniclient := NewClient(&bce.Config{
		Credentials: bce.NewCredentials(accessKeyID, secretAccessKey),
		Checksum:    true,
		Timeout:     30 * time.Second,
		Region:      region,
		Endpoint:    Endpoints[region],
	})
	eniclient.SetDebug(true)

	req := &GetENIsRequest{
		PageNo:   1,
		PageSize: 10000,
		VPCUUID:  "314f0fae-87ea-4e80-87d7-199b95eef5f3",
	}

	resp, err := eniclient.GetENIs(context.TODO(), req, NewSignOption())
	if err != nil {
		t.Errorf("GetENIs failed: %v", err)
	}

	if str, err := json.Marshal(resp); err == nil {
		t.Logf("GetENIs success: %v", string(str))
	}
}

func testDetachENI(t *testing.T) {
	accessKeyID := "e0c68be8495540f280b3e9ef03ec25d2"
	secretAccessKey := "b599d5f4f30e41bcb0e1482c9de65bd6"
	region := "gz"

	eniclient := NewClient(&bce.Config{
		Credentials: bce.NewCredentials(accessKeyID, secretAccessKey),
		Checksum:    true,
		Timeout:     30 * time.Second,
		Region:      region,
		Endpoint:    Endpoints[region],
	})
	eniclient.SetDebug(true)

	err := eniclient.DetachENI(context.TODO(), "eni-gps1mnafhbjq", "27b5f101-c7bc-4f1a-9f26-7c0a2b4c47f0", NewSignOption())
	if err != nil {
		t.Errorf("DeleteENI failed: %v", err)
	}
}

func testDeleteENI(t *testing.T) {
	accessKeyID := "e0c68be8495540f280b3e9ef03ec25d2"
	secretAccessKey := "b599d5f4f30e41bcb0e1482c9de65bd6"
	region := "gz"

	eniclient := NewClient(&bce.Config{
		Credentials: bce.NewCredentials(accessKeyID, secretAccessKey),
		Checksum:    true,
		Timeout:     30 * time.Second,
		Region:      region,
		Endpoint:    Endpoints[region],
	})
	eniclient.SetDebug(true)

	err := eniclient.DeleteENI(context.TODO(), "eni-gps1mnafhbjq", NewSignOption())
	if err != nil {
		t.Errorf("DeleteENI failed: %v", err)
	}
}
