/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package internalvpc

import (
	"context"
	"net/http"
	"testing"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
)

func TestNewClient(t *testing.T) {
	type args struct {
		config *bce.Config
	}
	tests := []struct {
		name string
		args args
		want *Client
	}{
		{
			name: "",
			args: args{
				config: &bce.Config{
					Credentials: &bce.Credentials{
						AccessKeyID:     "",
						SecretAccessKey: "",
					},
					Region:         "",
					Endpoint:       "",
					APIVersion:     "",
					Protocol:       "",
					UserAgent:      "",
					ProxyHost:      "",
					ProxyPort:      0,
					MaxConnections: 0,
					Timeout:        0,
					RetryPolicy:    nil,
					Checksum:       false,
				},
			},
			want: &Client{
				Client: &bce.Client{
					Config: &bce.Config{
						Credentials: &bce.Credentials{
							AccessKeyID:     "",
							SecretAccessKey: "",
						},
						Region:         "",
						Endpoint:       "",
						APIVersion:     "",
						Protocol:       "",
						UserAgent:      "",
						ProxyHost:      "",
						ProxyPort:      0,
						MaxConnections: 0,
						Timeout:        0,
						RetryPolicy:    nil,
						Checksum:       false,
					},
					HTTPClient: &http.Client{
						Transport:     nil,
						CheckRedirect: func(*http.Request, []*http.Request) error { panic("not implemented") },
						Jar:           nil,
						Timeout:       0,
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewClient(tt.args.config); got == nil {
				t.Errorf("NewClient() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestClient_GetURL(t *testing.T) {
	type args struct {
		version string
		params  map[string]string
	}
	tests := []struct {
		name string
		c    *Client
		args args
		want string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.c.GetURL(tt.args.version, tt.args.params); got != tt.want {
				t.Errorf("Client.GetURL() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestNewSignOption(t *testing.T) {
	tests := []struct {
		name string
		want *bce.SignOption
	}{
		{
			name: "",
			want: &bce.SignOption{
				Timestamp:                 "",
				ExpirationPeriodInSeconds: 0,
				Headers: map[string]string{
					"": "",
				},
				HeadersToSign: []string{},
				Credentials: &bce.Credentials{
					AccessKeyID:     "",
					SecretAccessKey: "",
				},
				CustomSignFunc:  func(context.Context, *bce.Request) { panic("not implemented") },
				CustomUserAgent: "",
				PostForm:        false,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewSignOption(); got == nil {
				t.Errorf("NewSignOption() = %v, want %v", got, tt.want)
			}
		})
	}
}
