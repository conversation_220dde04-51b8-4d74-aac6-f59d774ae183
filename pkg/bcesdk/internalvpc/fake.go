/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package internalvpc

//// FakeClient for AppBLB fake client
//type FakeClient struct {
//	LoadBalancerMap map[string]*BlbInstance
//}
//
//// NewFakeClient for logical BLB fake client
//func NewFakeClient() *FakeClient {
//	return &FakeClient{
//		// Key = blb_id
//		LoadBalancerMap: map[string]*BlbInstance{},
//	}
//}
//
//func (f *FakeClient) CreateAppLoadBalancer(ctx context.Context, args *CreateAppLoadBalancerArgs, option *bce.SignOption)
// (*CreateAppLoadBalancerResponse, error) {
//	if args == nil {
//		return nil, fmt.Errorf("CreateAppLoadBalancer faile: args is nil")
//	}
//
//	blb := &AppLoadBalancer{
//		Name:   args.Name,
//		Status: BLBStatusAvailable,
//	}
//
//	for {
//		blbID := util.GenerateBCEShortID("lb")
//		if _, ok := f.AppLoadBalancerMap[blbID]; !ok {
//			blb.BLBID = blbID
//			f.AppLoadBalancerMap[blbID] = blb
//
//			break
//		}
//	}
//
//	return &CreateAppLoadBalancerResponse{
//		Name:  blb.Name,
//		BLBID: blb.BLBID,
//	}, nil
//}
