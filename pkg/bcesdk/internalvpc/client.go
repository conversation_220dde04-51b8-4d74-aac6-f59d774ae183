/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package internalvpc

import (
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/util"
)

var Endpoints = map[string]string{
	"bj":  "",
	"gz":  "vpc.gz.bce-internal.baidu.com",
	"su":  "",
	"hkg": "",
	"fwh": "",
	"bd":  "",
}

// Client is the blb client implemention for Interface.
type Client struct {
	*bce.Client
}

// NewClient return client
func NewClient(config *bce.Config) *Client {
	bceClient := bce.NewClient(config)
	return &Client{bceClient}
}

// GetURL generates the full URL of http request for Baidu Cloud  API.
func (c *Client) GetURL(version string, params map[string]string) string {
	host := c.Endpoint
	if host == "" {
		host = Endpoints[c.GetRegion()]
	}
	uriPath := version
	return c.Client.GetURL(host, uriPath, params)
}

// NewSignOption return a app blb specified sign option
func NewSignOption() *bce.SignOption {
	option := &bce.SignOption{}
	option.AddHeader("x-bce-request-id", util.GetRequestID())
	option.AddHeadersToSign("host", "x-bce-date")

	return option
}
