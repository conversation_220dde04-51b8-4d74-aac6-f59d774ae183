/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package internalvpc

import (
	"context"
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
)

func (c *Client) GetSubnetByID(ctx context.Context, shortID string, option *bce.SignOption) (*Subnet, error) {
	if shortID == "" {
		return nil, fmt.Errorf("shortID cannot be empty")
	}
	params := map[string]string{
		"id": shortID,
	}

	url := fmt.Sprintf("v1/api/logical/network/subnet")
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	respContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var result SubnetResponse
	err = json.Unmarshal(respContent, &result)
	if err != nil {
		return nil, err
	}

	return result.Subnet, nil
}
