/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package internalvpc

import (
	"context"
	"io/ioutil"
	"net/http"
	"reflect"
	"strings"
	"testing"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
)

func TestClient_GetSubnetByID(t *testing.T) {
	type args struct {
		ctx     context.Context
		shortID string
		option  *bce.SignOption
	}
	tests := []struct {
		name    string
		c       *Client
		args    args
		want    *Subnet
		wantErr bool
	}{
		{
			name: "",
			c: &Client{
				Client: &bce.Client{
					Config: &bce.Config{
						Credentials: &bce.Credentials{
							AccessKeyID:     "",
							SecretAccessKey: "",
						},
						Region:         "",
						Endpoint:       "",
						APIVersion:     "",
						Protocol:       "",
						UserAgent:      "",
						ProxyHost:      "",
						ProxyPort:      0,
						MaxConnections: 0,
						Timeout:        0,
						RetryPolicy:    nil,
						Checksum:       false,
					},
					HTTPClient: &http.Client{
						Transport: RoundTripFunc(func(req *http.Request) *http.Response {
							return &http.Response{
								StatusCode: http.StatusOK,
								Body: ioutil.NopCloser(strings.NewReader(`{
									"subnet": {}
								}`)),
							}
						}),
						CheckRedirect: func(*http.Request, []*http.Request) error { panic("not implemented") },
						Jar:           nil,
						Timeout:       0,
					},
				},
			},
			args: args{
				ctx:     context.TODO(),
				shortID: "xx",
				option:  nil,
			},
			want: &Subnet{
				SubnetID:       "",
				SubnetUUID:     "",
				SubnetName:     "",
				SubnetType:     0,
				Description:    "",
				SubnetCIDR:     "",
				SubnetCIDRIPv6: "",
				AvailableZone:  "",
				VPCID:          "",
				VPCUUID:        "",
				CreateTime:     "",
				UpdatedTime:    "",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.c.GetSubnetByID(tt.args.ctx, tt.args.shortID, tt.args.option)
			if (err != nil) != tt.wantErr {
				t.Errorf("Client.GetSubnetByID() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Client.GetSubnetByID() = %v, want %v", got, tt.want)
			}
		})
	}
}
