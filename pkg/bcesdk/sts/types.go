package sts

import (
	"context"
	"time"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
)

// go:generate mockgen -copyright_file=${GOPATH}/src/icode.baidu.com/baidu/bci2/bci-cni-driver/hack/boilerplate.go.txt -destination=./mock/mock_client.go -package=mock icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/sts Interface

// Interface 定义服务号代用户签名的接口
type Interface interface {
	SetDebug(debug bool)

	NewSignOption(ctx context.Context, accountID string) *bce.SignOption
	NewSignOptionWithCCEAKSk(ctx context.Context, ak, sk, accountID string) *bce.SignOption
	AssumeRole(ctx context.Context, accountID string) (*Credential, error)
	AssumeRoleWithToken(ctx context.Context, accountID string) (*Credential, error)
}

const (
	// RoleName CCE 角色名字
	RoleName = "BceServiceRole_SERVICE_CCE"
)

// Credential STS 签名返回
type Credential struct {
	AccessKeyID     string    `json:"AccessKeyID"`
	SecretAccessKey string    `json:"SecretAccessKey"`
	SessionToken    string    `json:"sessionToken"`
	Token           Token     `json:"token"`
	CreateTime      time.Time `json:"createTime"`
	Expiration      time.Time `json:"expiration"`
	UserID          string    `json:"userId"`
}

// Token -
type Token struct {
	ID      string    `json:"id"`
	Catalog []Service `json:"catalog"`
}

// Service -
type Service struct {
	ID        string     `json:"id"`
	Type      string     `json:"type"`
	Endpoints []Endpoint `json:"endpoints"`
}

// Endpoint -
type Endpoint struct {
	ID        string `json:"id"`
	URL       string `json:"url"`
	Region    string `json:"region"`
	Interface string `json:"interface"`
}
