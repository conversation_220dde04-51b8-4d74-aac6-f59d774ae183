package sts

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
)

var (
	endpoint    = "nmg02-bce-test6.nmg02.baidu.com:8586/v1"
	region      = "sandbox"
	serviceName = "cce"
	password    = "********************************"
	accountID   = "15c123dded9d405681648da96ceaaa8b"
)

func testAssumeRole(t *testing.T) {
	client := NewClient(context.TODO(), &bce.Config{
		Endpoint: endpoint,
		Checksum: true,
		Timeout:  3 * time.Second,
		Region:   region,
	}, &bce.Config{
		Endpoint: "nmg02-bce-test6.nmg02.baidu.com:35357/v3",
		Checksum: true,
		Timeout:  3 * time.Second,
		Region:   region,
	}, RoleName, serviceName, password)

	client.SetDebug(true)

	credential, err := client.AssumeRole(context.TODO(), accountID)
	if err != nil {
		t.Errorf("get credential failed: %v", err)
	}

	if responseByte, err := json.<PERSON>(credential); err == nil {
		t.Logf("get credential succeeded: %s", string(responseByte))
	}
}

func testAssumeRoleWithToken(t *testing.T) {
	client := NewClient(context.TODO(), &bce.Config{
		Endpoint: endpoint,
		Checksum: true,
		Timeout:  3 * time.Second,
		Region:   region,
	}, &bce.Config{
		Endpoint: "nmg02-bce-test6.nmg02.baidu.com:35357/v3",
		Checksum: true,
		Timeout:  3 * time.Second,
		Region:   region,
	}, RoleName, serviceName, password)

	client.SetDebug(true)

	STSCredential, err := client.AssumeRoleWithToken(context.TODO(), accountID)
	if err != nil {
		t.Errorf("get STSCredential failed: %v", err)
	}

	if responseByte, err := json.Marshal(STSCredential); err == nil {
		t.Logf("get STSCredential succeeded: %s", string(responseByte))
	}
}
