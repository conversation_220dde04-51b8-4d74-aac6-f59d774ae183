// /*
// Copyright 2022 Baidu, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
// */

// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/sts (interfaces: Interface)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	bce "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
	sts "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/sts"
)

// MockInterface is a mock of Interface interface.
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface.
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance.
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// AssumeRole mocks base method.
func (m *MockInterface) AssumeRole(arg0 context.Context, arg1 string) (*sts.Credential, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AssumeRole", arg0, arg1)
	ret0, _ := ret[0].(*sts.Credential)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AssumeRole indicates an expected call of AssumeRole.
func (mr *MockInterfaceMockRecorder) AssumeRole(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AssumeRole", reflect.TypeOf((*MockInterface)(nil).AssumeRole), arg0, arg1)
}

// AssumeRoleWithToken mocks base method.
func (m *MockInterface) AssumeRoleWithToken(arg0 context.Context, arg1 string) (*sts.Credential, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AssumeRoleWithToken", arg0, arg1)
	ret0, _ := ret[0].(*sts.Credential)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AssumeRoleWithToken indicates an expected call of AssumeRoleWithToken.
func (mr *MockInterfaceMockRecorder) AssumeRoleWithToken(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AssumeRoleWithToken", reflect.TypeOf((*MockInterface)(nil).AssumeRoleWithToken), arg0, arg1)
}

// NewSignOption mocks base method.
func (m *MockInterface) NewSignOption(arg0 context.Context, arg1 string) *bce.SignOption {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewSignOption", arg0, arg1)
	ret0, _ := ret[0].(*bce.SignOption)
	return ret0
}

// NewSignOption indicates an expected call of NewSignOption.
func (mr *MockInterfaceMockRecorder) NewSignOption(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewSignOption", reflect.TypeOf((*MockInterface)(nil).NewSignOption), arg0, arg1)
}

// NewSignOptionWithCCEAKSk mocks base method.
func (m *MockInterface) NewSignOptionWithCCEAKSk(arg0 context.Context, arg1, arg2, arg3 string) *bce.SignOption {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewSignOptionWithCCEAKSk", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*bce.SignOption)
	return ret0
}

// NewSignOptionWithCCEAKSk indicates an expected call of NewSignOptionWithCCEAKSk.
func (mr *MockInterfaceMockRecorder) NewSignOptionWithCCEAKSk(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewSignOptionWithCCEAKSk", reflect.TypeOf((*MockInterface)(nil).NewSignOptionWithCCEAKSk), arg0, arg1, arg2, arg3)
}

// SetDebug mocks base method.
func (m *MockInterface) SetDebug(arg0 bool) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetDebug", arg0)
}

// SetDebug indicates an expected call of SetDebug.
func (mr *MockInterfaceMockRecorder) SetDebug(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDebug", reflect.TypeOf((*MockInterface)(nil).SetDebug), arg0)
}
