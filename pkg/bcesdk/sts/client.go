package sts

import (
	"context"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/iam"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/util"
)

// Endpoints 各地域 Endpoints
var Endpoints = map[string]string{
	"bj":      "sts.bj.iam.sdns.baidu.com:8586/v1",
	"gz":      "sts.gz.iam.sdns.baidu.com:8586/v1",
	"su":      "sts.su.iam.sdns.baidu.com:8586/v1",
	"hkg":     "sts.hkg.bce.baidu-int.com:8586/v1",
	"fwh":     "sts.fwh.bce.baidu-int.com:8586/v1",
	"bd":      "sts.bdbl.bce.baidu-int.com:8586/v1",
	"sandbox": "sts.bj.internal-qasandbox.baidu-int.com:8586/v1",
}

var _ Interface = &Client{}

// Client is the STS client implementation for Interface.
type Client struct {
	bceclient *bce.Client
	iamclient iam.Interface

	roleName        string
	serviceName     string
	servicePassword string
}

// NewClient - 初始化 Client
//
// PARAMS:
//  - ctx: The context to trace request
//  - stsConfig: STS 服务配置
//  - iamConfig: IAM 服务配置
//  - roleName: 服务号角色
//  - serviceName: 服务号名字
//  - servicePassword: 服务号密码
//
// RETURNS:
//   sts.Interface
func NewClient(ctx context.Context, stsConfig *bce.Config, iamConfig *bce.Config, roleName, serviceName, servicePassword string) *Client {
	return &Client{
		bceclient: bce.NewClient(stsConfig),
		iamclient: iam.NewClient(iamConfig),

		roleName:        roleName,
		serviceName:     serviceName,
		servicePassword: servicePassword,
	}
}

// GetURL generates the full URL of http request for BaiDu Cloud  API.
func (c *Client) GetURL(version string, params map[string]string) string {
	host := c.bceclient.Endpoint
	if host == "" {
		host = Endpoints[c.bceclient.GetRegion()]
	}

	uriPath := version
	return c.bceclient.GetURL(host, uriPath, params)
}

// SetDebug open or close Debug Mode
func (c *Client) SetDebug(debug bool) {
	c.bceclient.SetDebug(debug)
	c.iamclient.SetDebug(debug)
}

// NewSignOption return STS specified sign option
func NewSignOption() *bce.SignOption {
	option := &bce.SignOption{}
	option.AddHeader("x-bce-request-id", util.GetRequestID())
	option.AddHeadersToSign("host", "x-bce-date")

	return option
}
