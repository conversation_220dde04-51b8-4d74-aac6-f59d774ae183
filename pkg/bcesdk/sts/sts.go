package sts

import (
	"bytes"
	"context"
	"encoding/json"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/util"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/logger"
)

// AssumeRole 服务号代用户鉴权, 申请用户 AK/SK
func (c *Client) AssumeRole(ctx context.Context, accountID string) (*Credential, error) {
	// TODO: 该方法是否可以在初始化 client 是否完成?
	accessKey, err := c.iamclient.GetAkSkByToken(ctx, c.serviceName, c.servicePassword, bce.NewSignOptionWithoutAuth())
	if err != nil {
		logger.Errorf(ctx, "GetAkSkByToken failed: %v", err)
		return nil, err
	}

	// 设置必要 Option
	option := &bce.SignOption{}
	option.AddHeader("x-bce-request-id", util.GetRequestID())
	option.AddHeadersToSign("host", "x-bce-date")
	option.Credentials = bce.NewCredentials(accessKey.Access, accessKey.Secret)

	params := map[string]string{
		"assumeRole":  "",
		"accessKeyId": option.Credentials.AccessKeyID,
		"accountId":   accountID,
		"roleName":    c.roleName,
	}

	req, err := bce.NewRequest("POST", c.GetURL("credential", params), bytes.NewBuffer(nil))
	if err != nil {
		return nil, err
	}

	resp, err := c.bceclient.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	sts := new(Credential)
	err = json.Unmarshal(bodyContent, sts)
	if err != nil {
		return nil, err
	}

	return sts, nil
}

// AssumeRoleWithToken 服务号代用户鉴权, 申请用户 AK/SK
func (c *Client) AssumeRoleWithToken(ctx context.Context, accountID string) (*Credential, error) {
	// TODO: 该方法是否可以在初始化 client 是否完成?
	accessKey, err := c.iamclient.GetAkSkByToken(ctx, c.serviceName, c.servicePassword, bce.NewSignOptionWithoutAuth())
	if err != nil {
		logger.Errorf(ctx, "GetAkSkByToken failed: %v", err)
		return nil, err
	}

	// 设置必要 Option
	option := &bce.SignOption{}
	option.AddHeader("x-bce-request-id", util.GetRequestID())
	option.AddHeadersToSign("host", "x-bce-date")
	option.Credentials = bce.NewCredentials(accessKey.Access, accessKey.Secret)

	params := map[string]string{
		"assumeRole":  "",
		"accessKeyId": option.Credentials.AccessKeyID,
		"accountId":   accountID,
		"roleName":    c.roleName,
		"withToken":   "",
	}

	req, err := bce.NewRequest("POST", c.GetURL("credential", params), bytes.NewBuffer(nil))
	if err != nil {
		return nil, err
	}

	resp, err := c.bceclient.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	sts := new(Credential)
	err = json.Unmarshal(bodyContent, sts)
	if err != nil {
		return nil, err
	}

	return sts, nil
}

// NewSignOption 代签名方法
func (c *Client) NewSignOption(ctx context.Context, accountID string) *bce.SignOption {
	// 使用服务号代签名, 用户用户临时 Ak/SK
	// TODO: 这里做缓存逻辑
	sts, err := c.AssumeRoleWithToken(ctx, accountID)
	if err != nil {
		logger.Errorf(ctx, "AssumeRoleWithToken accountID=%s failed: %v", accountID, err)
		return nil
	}

	if sts == nil {
		logger.Errorf(ctx, "NewSignOption failed: sts is nil")
		return nil
	}

	// 构建 SignOption
	option := &bce.SignOption{}

	option.AddHeader("X-Bce-Request-Id", logger.GetUUID())
	option.AddHeader("X-Auth-Token", sts.Token.ID)
	option.AddHeader("X-Bce-Security-Token", sts.SessionToken)
	option.Credentials = bce.NewCredentials(sts.AccessKeyID, sts.SecretAccessKey)

	option.AddHeadersToSign("host")

	return option
}

func (c *Client) NewSignOptionWithCCEAKSk(ctx context.Context, ak, sk, accountID string) *bce.SignOption {
	// 设置必要 Option
	option := &bce.SignOption{}
	option.AddHeader("x-bce-request-id", util.GetRequestID())
	option.AddHeadersToSign("host", "x-bce-date")
	option.Credentials = bce.NewCredentials(ak, sk)

	params := map[string]string{
		"assumeRole":  "",
		"accessKeyId": option.Credentials.AccessKeyID,
		"accountId":   accountID,
		"roleName":    c.roleName,
		"withToken":   "",
	}

	req, err := bce.NewRequest("POST", c.GetURL("credential", params), bytes.NewBuffer(nil))
	if err != nil {
		return nil
	}

	resp, err := c.bceclient.SendRequest(ctx, req, option)
	if err != nil {
		return nil
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil
	}

	sts := new(Credential)
	err = json.Unmarshal(bodyContent, sts)
	if err != nil {
		return nil
	}

	// 构建 SignOption
	option = &bce.SignOption{}

	option.AddHeader("X-Bce-Request-Id", logger.GetUUID())
	option.AddHeader("X-Auth-Token", sts.Token.ID)
	option.AddHeader("X-Bce-Security-Token", sts.SessionToken)
	option.Credentials = bce.NewCredentials(sts.AccessKeyID, sts.SecretAccessKey)

	option.AddHeadersToSign("host")

	return option
}
