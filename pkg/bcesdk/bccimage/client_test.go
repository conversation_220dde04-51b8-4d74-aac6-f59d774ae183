/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package bccimage

import (
	"reflect"
	"testing"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
)

func TestNewClient(t *testing.T) {
	type args struct {
		config *bce.Config
	}
	tests := []struct {
		name string
		args args
		want *Client
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewClient(tt.args.config); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewClient() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestClient_GetURL(t *testing.T) {
	type args struct {
		version string
		params  map[string]string
	}
	tests := []struct {
		name string
		c    *Client
		args args
		want string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.c.GetURL(tt.args.version, tt.args.params); got != tt.want {
				t.Errorf("Client.GetURL() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestNewSignOption(t *testing.T) {
	tests := []struct {
		name string
		want *bce.SignOption
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewSignOption(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewSignOption() = %v, want %v", got, tt.want)
			}
		})
	}
}
