/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package bccimage

import (
	"context"
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
)

// GetImages - 查询指定类型的镜像
// PARAMS:
// ctx: The context to trace request
// args: 镜像查询请求参数
//
// RETURNS:
// *ImagesResponse: image list
// error: nil if succeed, error if fail
func (c *Client) GetImages(ctx context.Context, args *ImagesRequest, option *bce.SignOption) (*ImagesResponse, error) {
	params := map[string]string{}
	var version = "v2"
	if args.Version != "" {
		version = args.Version
	}
	if args.Marker != "" {
		params["marker"] = args.Marker
	}
	if args.MaxKeys != 0 {
		params["maxKeys"] = fmt.Sprintf("%d", args.MaxKeys)
	}
	if args.ImageType != "" {
		params["imageType"] = string(args.ImageType)
	}

	req, err := bce.NewRequest("GET", c.GetURL(version+"/image", params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var imageList *ImagesResponse
	err = json.Unmarshal(bodyContent, &imageList)
	if err != nil {
		return nil, err
	}

	return imageList, nil
}

// GetImage - 通过 ImageID 获取 Image 详细信息
// PARAMS:
// ctx: The context to trace request
// imageID: image ID
//
// RETURNS:
// *Image: image details
// error: nil if succeed, error if fail
func (c *Client) GetImage(ctx context.Context, imageID string, option *bce.SignOption) (*Image, error) {
	if imageID == "" {
		return nil, fmt.Errorf("image ID is empty")
	}

	params := map[string]string{}

	req, err := bce.NewRequest("GET", c.GetURL("v2/image/"+imageID, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var imageResp *ImageResponse
	err = json.Unmarshal(bodyContent, &imageResp)
	if err != nil {
		return nil, err
	}

	return &imageResp.Image, nil
}
