/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package bccimage

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
)

var (
	//region          = "sandbox"
	//accessKeyID     = "********************************"
	//secretAccessKey = "6b109261727a4fee8fc5db4bf5ec6c51"

	region          = "bj"
	accessKeyID     = "********************************"
	secretAccessKey = "b599d5f4f30e41bcb0e1482c9de65bd6"

	imageClient = NewClient(&bce.Config{
		Credentials: bce.NewCredentials(accessKeyID, secretAccessKey),
		Checksum:    true,
		Timeout:     30 * time.Second,
		Region:      region,
		Endpoint:    Endpoints[region],
	})
)

func init() {
	imageClient.SetDebug(true)
}

func testGetImages(t *testing.T) {
	args := &ImagesRequest{
		ImageType: ImageTypeSystem,
	}

	resp, err := imageClient.GetImages(context.Background(), args, NewSignOption())
	if err != nil {
		t.Errorf("GetImages failed: %v", err)
	}

	if respByte, err := json.Marshal(resp); err == nil {
		t.Logf("GetImages succeeded: %s", string(respByte))
	}
}

func testGetImage(t *testing.T) {
	resp, err := imageClient.GetImage(context.Background(), "m-ix2tRLmn", NewSignOption())
	if err != nil {
		t.Errorf("GetImage failed: %v", err)
	}

	if respByte, err := json.Marshal(resp); err == nil {
		t.Logf("GetImage succeeded: %s", string(respByte))
	}
}
