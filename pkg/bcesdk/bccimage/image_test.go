/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package bccimage

import (
	"context"
	"io/ioutil"
	"net/http"
	"reflect"
	"strings"
	"testing"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
)

type RoundTripFunc func(req *http.Request) *http.Response

func (f RoundTripFunc) RoundTrip(req *http.Request) (*http.Response, error) {
	return f(req), nil
}

func TestClient_GetImages(t *testing.T) {
	type args struct {
		ctx    context.Context
		args   *ImagesRequest
		option *bce.SignOption
	}
	tests := []struct {
		name    string
		c       *Client
		args    args
		want    *ImagesResponse
		wantErr bool
	}{
		{
			name: "",
			c: &Client{
				Client: &bce.Client{
					Config: &bce.Config{
						Credentials: &bce.Credentials{
							AccessKeyID:     "",
							SecretAccessKey: "",
						},
						Region:         "",
						Endpoint:       "",
						APIVersion:     "",
						Protocol:       "",
						UserAgent:      "",
						ProxyHost:      "",
						ProxyPort:      0,
						MaxConnections: 0,
						Timeout:        0,
						RetryPolicy:    nil,
						Checksum:       false,
					},
					HTTPClient: &http.Client{
						Transport: RoundTripFunc(func(req *http.Request) *http.Response {
							return &http.Response{
								StatusCode: http.StatusOK,
								Body:       ioutil.NopCloser(strings.NewReader(`{}`)),
							}
						}),
						CheckRedirect: func(*http.Request, []*http.Request) error { panic("not implemented") },
						Jar:           nil,
						Timeout:       0,
					},
				},
			},
			args: args{
				ctx: context.TODO(),
				args: &ImagesRequest{
					Version:   "",
					Marker:    "",
					MaxKeys:   0,
					ImageType: "",
				},
				option: nil,
			},
			want: &ImagesResponse{
				Marker:      "",
				IsTruncated: false,
				NextMarker:  "",
				MaxKeys:     0,
				Images:      nil,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.c.GetImages(tt.args.ctx, tt.args.args, tt.args.option)
			if (err != nil) != tt.wantErr {
				t.Errorf("Client.GetImages() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Client.GetImages() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestClient_GetImage(t *testing.T) {
	type args struct {
		ctx     context.Context
		imageID string
		option  *bce.SignOption
	}
	tests := []struct {
		name    string
		c       *Client
		args    args
		want    *Image
		wantErr bool
	}{
		{
			name: "",
			c: &Client{
				Client: &bce.Client{
					Config: &bce.Config{
						Credentials: &bce.Credentials{
							AccessKeyID:     "",
							SecretAccessKey: "",
						},
						Region:         "",
						Endpoint:       "",
						APIVersion:     "",
						Protocol:       "",
						UserAgent:      "",
						ProxyHost:      "",
						ProxyPort:      0,
						MaxConnections: 0,
						Timeout:        0,
						RetryPolicy:    nil,
						Checksum:       false,
					},
					HTTPClient: &http.Client{
						Transport: RoundTripFunc(func(req *http.Request) *http.Response {
							return &http.Response{
								StatusCode: http.StatusOK,
								Body:       ioutil.NopCloser(strings.NewReader(`{}`)),
							}
						}),
						CheckRedirect: func(*http.Request, []*http.Request) error { panic("not implemented") },
						Jar:           nil,
						Timeout:       0,
					},
				},
			},
			args: args{
				ctx:     context.TODO(),
				imageID: "xx",
				option:  nil,
			},
			want: &Image{
				ImageID:        "",
				ImageName:      "",
				ImageType:      "",
				OSType:         "",
				OSVersion:      "",
				OSArch:         "",
				OSName:         "",
				OSBuild:        "",
				CreateTime:     "",
				Status:         "",
				Desc:           "",
				SpecialVersion: "",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.c.GetImage(tt.args.ctx, tt.args.imageID, tt.args.option)
			if (err != nil) != tt.wantErr {
				t.Errorf("Client.GetImage() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Client.GetImage() = %v, want %v", got, tt.want)
			}
		})
	}
}
