package vpc

import (
	"context"
	"encoding/json"
	"testing"
)

func testDescribeSubnet(t *testing.T) {
	vpcclient.SetDebug(true)

	resp, err := vpcclient.DescribeSubnet(context.TODO(), "sbn-mnbvhnuupv1u", stsclient.NewSignOption(context.TODO(), accountID))
	if err != nil {
		t.<PERSON>("DescribeSubnet failed: %v", err)
		return
	}

	if respBytes, err := json.Marshal(resp); err == nil {
		t.Logf("DescribeSubnet succeeded: %s", string(respBytes))
	}
}
