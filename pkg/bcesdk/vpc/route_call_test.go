package vpc

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/iam"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/sts"
)

var (
	//region      = "sandbox"
	//serviceName = "cce"
	//password    = "nHrpnEDTIOonHC1QTOLGDOSjKbqfwKKy"
	//accountID   = "00dc1b52d8354d9193536e4dd2c41ae6" // 沙盒账号

	region      = "gz"
	serviceName = "cce"
	password    = "c8QBqhOKxmGu0Sl2p13guMil3U2grbYt"
	accountID   = "eca97e148cb74e9683d7b7240829d1ff" // online 账号

	vpcclient = NewClient(&bce.Config{
		Endpoint: Endpoints[region],
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   region,
	})

	stsclient = sts.NewClient(context.TODO(), &bce.Config{
		Endpoint: sts.Endpoints[region],
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   region,
	}, &bce.Config{
		Endpoint: iam.Endpoints[region],
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   region,
	}, sts.RoleName, serviceName, password)
)

func setDebug() {
	vpcclient.SetDebug(true)
	stsclient.SetDebug(true)
}

func testListRouteTable(t *testing.T) {
	vpcclient.SetDebug(true)

	args := ListRouteArgs{
		VpcID: "vpc-rn9auwd4qbr7",
	}

	resp, err := vpcclient.ListRouteTable(context.TODO(), &args, stsclient.NewSignOption(context.TODO(), accountID))
	if err != nil {
		t.Errorf("ListRouteTable failed: %v", err)
		return
	}

	if respBytes, err := json.Marshal(resp); err == nil {
		t.Logf("ListRouteTable succeeded: %s", string(respBytes))
	}
}

func testDeleteRoute(t *testing.T) {
	vpcclient.SetDebug(true)

	routeID := "rr-j6jyhbsypdp0"
	err := vpcclient.DeleteRoute(context.TODO(), routeID, stsclient.NewSignOption(context.TODO(), accountID))
	if err != nil {
		t.Errorf("DeleteRoute failed: %v", err)
		return
	}

	t.Logf("DeleteRoute succeeded")
}
