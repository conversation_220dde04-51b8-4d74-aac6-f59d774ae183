/*
Copyright 2018 The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package vpc

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
)

// CreateSubnet 在VPC中创建子网
func (c *Client) CreateSubnet(ctx context.Context, args *CreateSubnetArgs, option *bce.SignOption) (string, error) {
	if args == nil {
		return "", fmt.Errorf("CreateSubnet failed: CreateSubnetArgs is nil")
	}

	params := map[string]string{
		"clientToken": c.<PERSON>lientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return "", err
	}

	req, err := bce.NewRequest("POST", c.GetURL("v1/subnet", params), bytes.NewBuffer(postContent))
	if err != nil {
		return "", err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return "", err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return "", err
	}

	var createSubnetResponse *CreateSubnetResponse
	err = json.Unmarshal(bodyContent, &createSubnetResponse)
	if err != nil {
		return "", err
	}

	return createSubnetResponse.SubnetID, nil
}

// ListSubnet 查询指定VPC的所有子网列表信息
func (c *Client) ListSubnet(ctx context.Context, args *ListSubnetArgs, option *bce.SignOption) ([]*Subnet, error) {
	// Get conditions
	params := map[string]string{}

	if args != nil {
		if args.Marker != "" {
			params["marker"] = args.Marker
		}

		if args.MaxKeys != 0 {
			params["maxKeys"] = fmt.Sprintf("%d", args.MaxKeys)
		}

		if args.VPCID != "" {
			params["vpcId"] = args.VPCID
		}

		if args.ZoneName != "" {
			params["zoneName"] = args.ZoneName
		}

		if args.SubnetType != "" {
			params["subnetType"] = string(args.SubnetType)
		}
	}

	req, err := bce.NewRequest("GET", c.GetURL("v1/subnet", params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var listSubnetResponse *ListSubnetResponse
	err = json.Unmarshal(bodyContent, &listSubnetResponse)
	if err != nil {
		return nil, err
	}

	return listSubnetResponse.Subnets, nil
}

// DescribeSubnet 查询指定子网的详细信息
func (c *Client) DescribeSubnet(ctx context.Context, subnetID string, option *bce.SignOption) (*Subnet, error) {
	if len(subnetID) == 0 {
		return nil, fmt.Errorf("DescribeSubnet failed, subnetId must not be empty")
	}
	req, err := bce.NewRequest("GET", c.GetURL("v1/subnet/"+subnetID, nil), nil)
	if err != nil {
		return nil, err
	}
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var describeSubnetResponse *DescribeSubnetResponse
	err = json.Unmarshal(bodyContent, &describeSubnetResponse)
	if err != nil {
		return nil, err
	}
	return describeSubnetResponse.Subnet, nil
}
