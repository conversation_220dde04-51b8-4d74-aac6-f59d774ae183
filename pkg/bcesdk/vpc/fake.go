package vpc

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/util"
)

// FakeClient implement of vpc.Interface
type FakeClient struct {
	VPCMap    map[string]*VPC
	SubnetMap map[string]*Subnet
	// routeruleID |
	RouteRuleMap map[string]RouteRule
	//  RuleTableID | VpcID
	VpcRuleTableMap map[string]string
}

// NewFakeClient for VPC fake client
func NewFakeClient() *FakeClient {
	return &FakeClient{
		VPCMap:          map[string]*VPC{},
		SubnetMap:       map[string]*Subnet{},
		RouteRuleMap:    map[string]RouteRule{},
		VpcRuleTableMap: map[string]string{},
	}
}

// SetDebug 开启调试
func (f *FakeClient) SetDebug(debug bool) {
	return
}

// CreateVPC create VPC
func (f *FakeClient) CreateVPC(ctx context.Context, args *CreateVPCArgs, option *bce.SignOption) (string, error) {
	if args == nil {
		return "", fmt.Errorf("CreateVPC failed: args is nil")
	}
	var routeTableID string
	vpc := &VPC{
		Name:        args.Name,
		Description: args.Description,
		CIDR:        args.CIDR,
		IPv6CIDR:    "",
	}

	for {
		vpcID := util.GenerateBCEShortID("vpc")
		if _, ok := f.VPCMap[vpcID]; !ok {
			vpc.VPCID = vpcID
			f.VPCMap[vpcID] = vpc
			routeTableID = f.generateVpcRoutetableMap(vpcID)
			break
		}
	}

	return vpc.VPCID + "/" + routeTableID, nil
}

// ListVPC to list VPC of region
func (f *FakeClient) ListVPC(ctx context.Context, args *ListVPCArgs, option *bce.SignOption) ([]*VPC, error) {
	vpcs := []*VPC{}

	for _, vpc := range f.VPCMap {
		vpcs = append(vpcs, vpc)
	}

	return vpcs, nil
}

func (f *FakeClient) DescribeVPC(ctx context.Context, vpcID string, option *bce.SignOption) (*DescribeVPCResponse, error) {
	if vpc, ok := f.VPCMap[vpcID]; ok {
		subnets, _ := f.ListSubnet(context.Background(), &ListSubnetArgs{VPCID: vpcID}, option)
		return &DescribeVPCResponse{ShowVPCModel: &VPC{
			VPCID:     vpc.VPCID,
			Name:      vpc.Name,
			CIDR:      vpc.CIDR,
			IPv6CIDR:  "",
			IsDefault: vpc.IsDefault,
			Subnets:   subnets,
		}}, nil
	}

	return nil, fmt.Errorf("DescribeVPC failed: vpc not found")
}

// CreateSubnet to Create Subnet under VPC
func (f *FakeClient) CreateSubnet(ctx context.Context, args *CreateSubnetArgs, option *bce.SignOption) (string, error) {
	if args == nil {
		return "", fmt.Errorf("CreateSubnet failed: args is nil")
	}

	subnet := &Subnet{
		Name:        args.Name,
		ZoneName:    args.ZoneName,
		CIDR:        args.CIDR,
		IPv6CIDR:    "",
		VPCID:       args.VPCID,
		SubnetType:  args.SubnetType,
		Description: args.Description,
	}

	for {
		subnetID := util.GenerateBCEShortID("sbn")
		if _, ok := f.SubnetMap[subnetID]; !ok {
			subnet.SubnetID = subnetID
			f.SubnetMap[subnetID] = subnet
			break
		}
	}

	return subnet.SubnetID, nil
}

// ListSubnet to List Subnet under VPC
func (f *FakeClient) ListSubnet(ctx context.Context, args *ListSubnetArgs, option *bce.SignOption) ([]*Subnet, error) {
	if args == nil {
		return []*Subnet{}, fmt.Errorf("ListSubnet failed: args is nil")
	}

	subnets := []*Subnet{}

	for _, subnet := range f.SubnetMap {
		isMatch := true

		if args.VPCID != "" {
			if subnet.VPCID != args.VPCID {
				isMatch = false
			}
		} else if args.SubnetType != "" {
			if subnet.SubnetType != args.SubnetType {
				isMatch = false
			}
		} else if args.ZoneName != "" {
			if subnet.ZoneName != args.ZoneName {
				isMatch = false
			}
		}

		if isMatch {
			subnets = append(subnets, subnet)
		}
	}

	return subnets, nil
}

// DescribeSubnet to Describe Subnet under VPC
func (f *FakeClient) DescribeSubnet(ctx context.Context, subnetID string, option *bce.SignOption) (*Subnet, error) {
	for _, subnet := range f.SubnetMap {
		if subnet.SubnetID == subnetID {
			return subnet, nil
		}
	}

	return nil, fmt.Errorf("NoSuchObject")
}

// ListRouteTable list route table in VPC
func (f *FakeClient) ListRouteTable(ctx context.Context, args *ListRouteArgs, option *bce.SignOption) ([]RouteRule, error) {
	if args == nil {
		return nil, fmt.Errorf("args is nil")
	}
	routeTableID := args.RouteTableID
	for k, v := range f.VpcRuleTableMap {
		if v == args.VpcID {
			routeTableID = k
		}
	}
	routerules := []RouteRule{}
	for _, routerule := range f.RouteRuleMap {
		if routerule.RouteTableID == routeTableID {
			routerules = append(routerules, routerule)
		}
	}
	return routerules, nil
}

// DeleteRoute delete route by routeID
func (f *FakeClient) DeleteRoute(ctx context.Context, routeID string, option *bce.SignOption) error {
	if len(routeID) == 0 {
		return fmt.Errorf("routeID is nil")
	}
	for routeruleID, routerule := range f.RouteRuleMap {
		if routerule.RouteRuleID == routeID {
			delete(f.RouteRuleMap, routeruleID)
			return nil
		}
	}
	return fmt.Errorf("DeleteRoute %s not exist", routeID)
}

func (f *FakeClient) CreateRouteRule(ctx context.Context, args *CreateRouteRuleArgs, option *bce.SignOption) (string, error) {
	if args == nil {
		return "", fmt.Errorf("args is nil")
	}
	routerule := RouteRule{
		RouteTableID:       args.RouteTableID,
		SourceAddress:      args.SourceAddress,
		DestinationAddress: args.DestinationAddress,
		NexthopID:          args.NexthopID,
		NexthopType:        args.NexthopType,
		Description:        args.Description,
	}
	for {
		routeruleID := util.GenerateBCEShortID("rr")
		if _, ok := f.RouteRuleMap[routeruleID]; !ok {
			routerule.RouteRuleID = routeruleID
			f.RouteRuleMap[routeruleID] = routerule
			break
		}
	}
	return routerule.RouteRuleID, nil
}

// help func
func (f *FakeClient) generateVpcRoutetableMap(vpcID string) string {
	for {
		routeTableID := util.GenerateBCEShortID("rt")
		if _, ok := f.VpcRuleTableMap[routeTableID]; !ok {
			f.VpcRuleTableMap[routeTableID] = vpcID
			return routeTableID
		}
	}
}
