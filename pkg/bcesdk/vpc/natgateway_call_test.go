package vpc

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/iam"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/sts"
)

var (
	regionNatTest      = "gz"
	serviceNameNatTest = "cce"
	passwordNatTest    = "c8QBqhOKxmGu0Sl2p13guMil3U2grbYt"
	accountIDNatTest   = "eca97e148cb74e9683d7b7240829d1ff" // online 账号

	vpcclientNatTest = NewClient(&bce.Config{
		Endpoint: Endpoints[region],
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   region,
	})

	stsclientNatTest = sts.NewClient(context.TODO(), &bce.Config{
		Endpoint: sts.Endpoints[region],
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   region,
	}, &bce.Config{
		Endpoint: iam.Endpoints[region],
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   region,
	}, sts.RoleName, serviceName, password)
)

// 执行 Case 顺序 注意需要手动一个个去执行 这里由于测试会实际创建资源因此不作为单测执行
// 1.测试 NAT 接口     创建 NAT -> 更新NAT -> NAT列表 -> NAT详情 -> 删除NAT
// 2.测试 EIP 绑定接口  创建 NAT -> 绑定SNAT EIP —> 解绑SNAT EIP -> 绑定DNAT EIP -> 解绑DNAT EIP -> 删除 NAT
// 3.测试 SNAT Rule   创建 NAT -> 绑定 SNAT EIP -> 创建 SNAT Rule -> 更新 SNAT Rule -> SNAT Rule列表 -> 删除 SNAT Rule -> 解绑 SNAT EIP -> 删除 NAT
// 4.测试 DNAT Rule   创建 NAT -> 绑定 DNAT EIP -> 创建 DNAT Rule -> 更新 DNAT Rule -> DNAT Rule列表 -> 删除 DNAT Rule -> 解绑 DNAT EIP -> 删除 NAT
// 这些变量要在手动测试时视情况替换
var vpcIdNatTest = "vpc-2bam5p1zugkb"
var createdNatID = "nat-gs1qd2szcbay"
var createdNat *NAT
var createdEip = "************"
var subnetCIDR = "***********/24"
var subnetPrivateAddress = "************"
var createdRuleID = "rule-9yqqedhsv8k6"

func testClient_CreateNatGateway(t *testing.T) {
	args := CreateNatGatewayArgs{
		Name:  "jc-nat-bcesdk-test",
		VpcId: vpcIdNatTest,
		Spec:  NAT_GATEWAY_SPEC_SMALL,
		Billing: &Billing{
			PaymentTiming: PAYMENT_TIMING_POSTPAID,
		},
	}

	resp, err := vpcclientNatTest.CreateNatGateway(context.TODO(), &args, stsclient.NewSignOption(context.TODO(), accountID))
	if err != nil {
		t.Errorf("CreateNatGateway failed: %v", err)
		return
	}

	createdNatID = resp.NatId

	if respBytes, err := json.Marshal(resp); err == nil {
		t.Logf("CreateNatGateway succeeded: %s", string(respBytes))
	}

	t.Logf("CreateNatGateway Success")
}

func testClient_ListNatGateway(t *testing.T) {
	args := ListNatGatewayArgs{
		VpcId: vpcIdNatTest,
	}

	resp, err := vpcclientNatTest.ListNatGateway(context.TODO(), &args, stsclient.NewSignOption(context.TODO(), accountID))
	if err != nil {
		t.Errorf("ListRoutListNatGatewayeTable failed: %v", err)
		return
	}

	if respBytes, err := json.Marshal(resp); err == nil {
		t.Logf("CreateNatGateway succeeded: %s", string(respBytes))
	}

	t.Logf("ListNatGateway Success")
}

func testClient_GetNatGatewayDetail(t *testing.T) {
	nat, err := vpcclientNatTest.GetNatGatewayDetail(context.TODO(), createdNatID, stsclient.NewSignOption(context.TODO(), accountID))
	if err != nil {
		t.Errorf("GetNatGatewayDetail failed: %v", err)
		return
	}

	createdNat = nat

	if respBytes, err := json.Marshal(nat); err == nil {
		t.Logf("GetNatGatewayDetail succeeded: %s", string(respBytes))
	}

	t.Logf("GetNatGatewayDetail Success")
}

func testClient_BindSnatEips(t *testing.T) {
	args := BindAndUnbindSnatEipsArgs{
		Eips: []string{createdEip},
	}
	err := vpcclientNatTest.BindSnatEips(context.TODO(), createdNatID, &args, stsclient.NewSignOption(context.TODO(), accountID))
	if err != nil {
		t.Errorf("BindSnatEips failed: %v", err)
		return
	}

	t.Logf("BindSnatEips Success")
}

func testClient_UnBindSnatEips(t *testing.T) {
	args := BindAndUnbindSnatEipsArgs{
		Eips: []string{createdEip},
	}
	err := vpcclientNatTest.UnbindSnatEips(context.TODO(), createdNatID, &args, stsclient.NewSignOption(context.TODO(), accountID))
	if err != nil {
		t.Errorf("BindSnatEips failed: %v", err)
		return
	}

	t.Logf("UnbindSnatEips Success")
}

func testClient_BindDnatEips(t *testing.T) {
	args := BindAndUnbindDnatEipsArgs{
		DnatEips: []string{createdEip},
	}
	err := vpcclientNatTest.BindDnatEips(context.TODO(), createdNatID, &args, stsclient.NewSignOption(context.TODO(), accountID))
	if err != nil {
		t.Errorf("BindSnatEips failed: %v", err)
		return
	}

	t.Logf("BindDnatEips Success")
}

func testClient_UnBindDnatEips(t *testing.T) {
	args := BindAndUnbindDnatEipsArgs{
		DnatEips: []string{createdEip},
	}
	err := vpcclientNatTest.UnbindDnatEips(context.TODO(), createdNatID, &args, stsclient.NewSignOption(context.TODO(), accountID))
	if err != nil {
		t.Errorf("UnbindDnatEips failed: %v", err)
		return
	}

	t.Logf("UnbindDnatEips Success")
}

func testClient_CreateNatSnatRule(t *testing.T) {
	args := CreateNatSnatRuleArgs{
		RuleName:         "jichao-test-rule-create",
		PublicIpsAddress: []string{createdEip},
		SourceCIDR:       subnetCIDR,
	}

	ruleID, err := vpcclientNatTest.CreateNatSnatRule(context.TODO(), createdNatID, &args, stsclient.NewSignOption(context.TODO(), accountID))
	if err != nil {
		t.Errorf("CreateNatSnatRule failed: %v", err)
		return
	}

	createdRuleID = ruleID

	t.Logf("CreateNatSnatRule Success RuleID %s", ruleID)
}

func testClient_UpdateNatSnatRule(t *testing.T) {
	args := UpdateNatSnatRuleArgs{
		RuleName: "jichao-test-rule-update",
	}

	err := vpcclientNatTest.UpdateNatSnatRule(context.TODO(), createdNatID, createdRuleID, &args, stsclient.NewSignOption(context.TODO(), accountID))
	if err != nil {
		t.Errorf("UpdateNatSnatRule failed: %v", err)
		return
	}

	t.Logf("UpdateNatSnatRule Success RuleID")
}

func testClient_ListNatSnatRules(t *testing.T) {
	args := ListNatSnatRuleArgs{
		MaxKeys: 10,
	}

	resp, err := vpcclientNatTest.ListNatSnatRules(context.TODO(), createdNatID, &args, stsclient.NewSignOption(context.TODO(), accountID))
	if err != nil {
		t.Errorf("ListNatSnatRules failed: %v", err)
		return
	}

	if respBytes, err := json.Marshal(resp); err == nil {
		t.Logf("ListNatSnatRules succeeded: %s", string(respBytes))
	}

	t.Logf("ListNatSnatRules Success RuleID")
}

func testClient_DeleteNatSnatRule(t *testing.T) {
	err := vpcclientNatTest.DeleteNatSnatRule(context.TODO(), createdNatID, createdRuleID, stsclient.NewSignOption(context.TODO(), accountID))
	if err != nil {
		t.Errorf("DeleteNatSnatRule failed: %v", err)
		return
	}

	t.Logf("DeleteNatSnatRule Success RuleID")
}

func testClient_CreateNatDnatRule(t *testing.T) {
	args := CreateNatDnatRuleArgs{
		RuleName:          "jichao-test-rule-create",
		PublicIpsAddress:  createdEip,
		PrivateIpsAddress: subnetPrivateAddress,
		Protocol:          "TCP",
		PublicPort:        "2311",
		PrivatePort:       "2311",
	}
	ruleID, err := vpcclientNatTest.CreateNatDnatRule(context.TODO(), createdNatID, &args, stsclient.NewSignOption(context.TODO(), accountID))
	if err != nil {
		t.Errorf("CreateNatSnatRule failed: %v", err)
		return
	}

	createdRuleID = ruleID

	t.Logf("CreateNatDnatRule Success RuleID %s", ruleID)
}

func testClient_UpdateNatDnatRule(t *testing.T) {
	args := UpdateNatDnatRuleArgs{
		RuleName: "jichao-test-rule-create-update",
	}
	err := vpcclientNatTest.UpdateNatDnatRule(context.TODO(), createdNatID, createdRuleID, &args, stsclient.NewSignOption(context.TODO(), accountID))
	if err != nil {
		t.Errorf("UpdateNatSnatRule failed: %v", err)
		return
	}

	t.Logf("UpdateNatSnatRule Success RuleID")
}

func testClient_ListNatDnatRules(t *testing.T) {
	args := ListNatDnatRuleArgs{
		MaxKeys: 10,
	}
	resp, err := vpcclientNatTest.ListNatDnatRules(context.TODO(), createdNatID, &args, stsclient.NewSignOption(context.TODO(), accountID))
	if err != nil {
		t.Errorf("ListNatDnatRules failed: %v", err)
		return
	}

	if respBytes, err := json.Marshal(resp); err == nil {
		t.Logf("ListNatSnatRules succeeded: %s", string(respBytes))
	}

	t.Logf("ListNatSnatRules Success RuleID")
}

func testClient_DeleteNatDnatRule(t *testing.T) {
	err := vpcclientNatTest.DeleteNatDnatRule(context.TODO(), createdNatID, createdRuleID, stsclient.NewSignOption(context.TODO(), accountID))
	if err != nil {
		t.Errorf("DeleteNatDnatRule failed: %v", err)
		return
	}

	t.Logf("DeleteNatDnatRule Success RuleID")
}

func testClient_DeleteNatGateway(t *testing.T) {
	err := vpcclientNatTest.DeleteNatGateway(context.TODO(), createdNatID, stsclient.NewSignOption(context.TODO(), accountID))
	if err != nil {
		t.Errorf("DeleteNatGateway failed: %v", err)
		return
	}

	createdNat = nil
	createdNatID = ""

	t.Logf("DeleteNatGateway Success")
}
