package vpc

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
)

func (args *ListRouteArgs) validate() error {
	if args == nil {
		return fmt.Errorf("ListRouteArgs need args")
	}
	if args.RouteTableID == "" && args.VpcID == "" {
		return fmt.Errorf("ListRouteArgs need RouteTableID or VpcID")
	}

	return nil
}

// ListRouteTable list all routes
func (c *Client) ListRouteTable(ctx context.Context, args *ListRouteArgs, option *bce.SignOption) ([]RouteRule, error) {
	err := args.validate()
	if err != nil {
		return nil, err
	}

	params := make(map[string]string)
	if args.RouteTableID != "" {
		params["routeTableId"] = args.RouteTableID
	}
	if args.VpcID != "" {
		params["vpcId"] = args.VpcID
	}

	req, err := bce.NewRequest("GET", c.GetURL("v1/route", params), nil)
	if err != nil {
		return nil, err
	}
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}
	bodyContent, err := resp.GetBodyContent()

	if err != nil {
		return nil, err
	}
	var routesResp *ListRouteResponse
	err = json.Unmarshal(bodyContent, &routesResp)

	if err != nil {
		return nil, err
	}
	return routesResp.RouteRules, nil
}

// DeleteRoute delete a route
// http://gollum.baidu.com/Logical-Network-API#删除路由规则
func (c *Client) DeleteRoute(ctx context.Context, routeID string, option *bce.SignOption) error {
	if routeID == "" {
		return fmt.Errorf("DeleteRoute need routeID")
	}
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}
	req, err := bce.NewRequest("DELETE", c.GetURL("v1/route/rule"+"/"+routeID, params), nil)
	if err != nil {
		return err
	}
	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}
	return nil
}

func (args *CreateRouteRuleArgs) validate() error {
	if args == nil {
		return fmt.Errorf("CreateRouteRuleArgs need args")
	}
	if args.RouteTableID == "" {
		return fmt.Errorf("CreateRouteRuleArgs need RouteTableID")
	}
	if args.SourceAddress == "" || args.DestinationAddress == "" {
		return fmt.Errorf("CreateRouteRuleArgs need address")
	}
	if args.NexthopID == "" || args.NexthopType == "" {
		return fmt.Errorf("CreateRouteRuleArgs need NexthopID and NexthopType")
	}
	return nil
}

// CreateRouteRule create a route rule
func (c *Client) CreateRouteRule(ctx context.Context, args *CreateRouteRuleArgs, option *bce.SignOption) (string, error) {
	err := args.validate()
	if err != nil {
		return "", err
	}
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}
	postContent, err := json.Marshal(args)
	if err != nil {
		return "", err
	}
	req, err := bce.NewRequest("POST", c.GetURL("v1/route/rule", params), bytes.NewBuffer(postContent))
	if err != nil {
		return "", err
	}
	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return "", err
	}
	bodyContent, err := resp.GetBodyContent()

	if err != nil {
		return "", err
	}
	var crResp *CreateRouteResponse
	err = json.Unmarshal(bodyContent, &crResp)

	if err != nil {
		return "", err
	}
	return crResp.RouteRuleID, nil

}

func (args *DescribeRouteTableArgs) validate() error {
	if args == nil {
		return fmt.Errorf("DescribeRouteTableArgs must not be nil")
	}

	if args.RouteTableID == "" && args.VPCID == "" {
		return fmt.Errorf("either RouteTableID or VPCID should be valid in DescribeRouteTableArgs")
	}

	return nil
}

// DescribeRouteTable 查询指定路由表所有路由规则
func (c *Client) DescribeRouteTable(ctx context.Context, args *DescribeRouteTableArgs, signOpt *bce.SignOption) (*DescribeRouteTableResponse, error) {
	err := args.validate()
	if err != nil {
		return nil, err
	}

	params := make(map[string]string)
	if args.RouteTableID != "" {
		params["routeTableId"] = args.RouteTableID
	}
	if args.VPCID != "" {
		params["vpcId"] = args.VPCID
	}

	req, err := bce.NewRequest("GET", c.GetURL("v1/route", params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, signOpt)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	describeRouteTableResponse := new(DescribeRouteTableResponse)
	err = json.Unmarshal(bodyContent, describeRouteTableResponse)
	if err != nil {
		return nil, err
	}

	return describeRouteTableResponse, nil
}
