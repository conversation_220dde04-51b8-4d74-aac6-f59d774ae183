// /*
// Copyright 2022 Baidu, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
// */

// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/vpc (interfaces: Interface)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	bce "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
	vpc "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/vpc"
)

// MockInterface is a mock of Interface interface.
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface.
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance.
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// CreateRouteRule mocks base method.
func (m *MockInterface) CreateRouteRule(arg0 context.Context, arg1 *vpc.CreateRouteRuleArgs, arg2 *bce.SignOption) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateRouteRule", arg0, arg1, arg2)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRouteRule indicates an expected call of CreateRouteRule.
func (mr *MockInterfaceMockRecorder) CreateRouteRule(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRouteRule", reflect.TypeOf((*MockInterface)(nil).CreateRouteRule), arg0, arg1, arg2)
}

// CreateSubnet mocks base method.
func (m *MockInterface) CreateSubnet(arg0 context.Context, arg1 *vpc.CreateSubnetArgs, arg2 *bce.SignOption) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateSubnet", arg0, arg1, arg2)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateSubnet indicates an expected call of CreateSubnet.
func (mr *MockInterfaceMockRecorder) CreateSubnet(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSubnet", reflect.TypeOf((*MockInterface)(nil).CreateSubnet), arg0, arg1, arg2)
}

// CreateVPC mocks base method.
func (m *MockInterface) CreateVPC(arg0 context.Context, arg1 *vpc.CreateVPCArgs, arg2 *bce.SignOption) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateVPC", arg0, arg1, arg2)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateVPC indicates an expected call of CreateVPC.
func (mr *MockInterfaceMockRecorder) CreateVPC(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateVPC", reflect.TypeOf((*MockInterface)(nil).CreateVPC), arg0, arg1, arg2)
}

// DeleteRoute mocks base method.
func (m *MockInterface) DeleteRoute(arg0 context.Context, arg1 string, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteRoute", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteRoute indicates an expected call of DeleteRoute.
func (mr *MockInterfaceMockRecorder) DeleteRoute(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteRoute", reflect.TypeOf((*MockInterface)(nil).DeleteRoute), arg0, arg1, arg2)
}

// DescribeSubnet mocks base method.
func (m *MockInterface) DescribeSubnet(arg0 context.Context, arg1 string, arg2 *bce.SignOption) (*vpc.Subnet, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeSubnet", arg0, arg1, arg2)
	ret0, _ := ret[0].(*vpc.Subnet)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeSubnet indicates an expected call of DescribeSubnet.
func (mr *MockInterfaceMockRecorder) DescribeSubnet(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeSubnet", reflect.TypeOf((*MockInterface)(nil).DescribeSubnet), arg0, arg1, arg2)
}

// DescribeVPC mocks base method.
func (m *MockInterface) DescribeVPC(arg0 context.Context, arg1 string, arg2 *bce.SignOption) (*vpc.DescribeVPCResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeVPC", arg0, arg1, arg2)
	ret0, _ := ret[0].(*vpc.DescribeVPCResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeVPC indicates an expected call of DescribeVPC.
func (mr *MockInterfaceMockRecorder) DescribeVPC(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeVPC", reflect.TypeOf((*MockInterface)(nil).DescribeVPC), arg0, arg1, arg2)
}

// ListRouteTable mocks base method.
func (m *MockInterface) ListRouteTable(arg0 context.Context, arg1 *vpc.ListRouteArgs, arg2 *bce.SignOption) ([]vpc.RouteRule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListRouteTable", arg0, arg1, arg2)
	ret0, _ := ret[0].([]vpc.RouteRule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListRouteTable indicates an expected call of ListRouteTable.
func (mr *MockInterfaceMockRecorder) ListRouteTable(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListRouteTable", reflect.TypeOf((*MockInterface)(nil).ListRouteTable), arg0, arg1, arg2)
}

// ListSubnet mocks base method.
func (m *MockInterface) ListSubnet(arg0 context.Context, arg1 *vpc.ListSubnetArgs, arg2 *bce.SignOption) ([]*vpc.Subnet, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListSubnet", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*vpc.Subnet)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListSubnet indicates an expected call of ListSubnet.
func (mr *MockInterfaceMockRecorder) ListSubnet(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListSubnet", reflect.TypeOf((*MockInterface)(nil).ListSubnet), arg0, arg1, arg2)
}

// ListVPC mocks base method.
func (m *MockInterface) ListVPC(arg0 context.Context, arg1 *vpc.ListVPCArgs, arg2 *bce.SignOption) ([]*vpc.VPC, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListVPC", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*vpc.VPC)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListVPC indicates an expected call of ListVPC.
func (mr *MockInterfaceMockRecorder) ListVPC(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListVPC", reflect.TypeOf((*MockInterface)(nil).ListVPC), arg0, arg1, arg2)
}

// SetDebug mocks base method.
func (m *MockInterface) SetDebug(arg0 bool) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetDebug", arg0)
}

// SetDebug indicates an expected call of SetDebug.
func (mr *MockInterfaceMockRecorder) SetDebug(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDebug", reflect.TypeOf((*MockInterface)(nil).SetDebug), arg0)
}
