package vpc

import (
	"context"
	"encoding/json"
	"testing"
)

func testCreateVPC(t *testing.T) {
	vpcclient.SetDebug(true)
	accountID = "2e1be1eb99e946c3a543ec5a4eaa7d39"

	resp, err := vpcclient.CreateVPC(context.TODO(), &CreateVPCArgs{
		Name:        "meta-cluster-vpc",
		Description: "liqilong-test",
		CIDR:        "*******/16",
	}, stsclient.NewSignOption(context.TODO(), accountID))
	if err != nil {
		t.<PERSON><PERSON>("CreateVPC failed: %v", err)
		return
	}

	if respBytes, err := json.Marshal(resp); err == nil {
		t.Logf("CreateVPC succeeded: %s", string(respBytes))
	}
}
