package bce

import (
	"encoding/json"
	"errors"
	"net/http"

	bceerror "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/error"
)

func buildError(resp *Response) error {
	bodyContent, err := resp.GetBodyContent()

	if err == nil {
		if bodyContent == nil || string(bodyContent) == "" {
			return errors.New("Unknown Error")
		}

		var bceError *bceerror.Error
		err := json.Unmarshal(bodyContent, &bceError)

		if err != nil {
			return errors.New(string(bodyContent))
		}

		bceError.StatusCode = resp.StatusCode

		return bceError
	}

	return err
}

func IsNotFound(err error) bool {
	bceErr, ok := err.(*bceerror.Error)
	if !ok {
		return false
	}

	return bceErr.StatusCode == http.StatusNotFound
}
