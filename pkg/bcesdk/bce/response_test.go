package bce

import (
	"net/http"
	"testing"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/util"
)

// 遇到单测的机器无法联通 http://bcc.bj.baidubce.com
func testGetBodyContent(t *testing.T) {
	request, err := http.NewRequest("GET", "http://bcc.bj.baidubce.com", nil)

	if err != nil {
		t.Error(util.FormatTest("GetBodyContent", err.<PERSON>rror(), "nil"))
	}

	client := &http.Client{}
	resp, err := client.Do(request)

	if err != nil {
		t.Error(util.FormatTest("GetBodyContent", err.Error(), "nil"))
	}

	bceResponse := NewResponse(resp)
	bodyContent, err := bceResponse.GetBodyContent()

	if err != nil {
		t.Error(util.FormatTest("GetBodyContent", err.Error(), "nil"))
	}

	if bodyContent == nil {
		t.Error(util.FormatTest("GetBodyContent", "nil", "not nil"))
	} else if string(bodyContent) == "" {
		t.Error(util.FormatTest("GetBodyContent", "empty string", "none empty string"))
	}
}
