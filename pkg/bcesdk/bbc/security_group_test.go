/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package bbc

import (
	"context"
	"io/ioutil"
	"net/http"
	"strings"
	"testing"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
)

func TestClient_BindSecurityGroup(t *testing.T) {
	type args struct {
		ctx            context.Context
		instanceIDs    []string
		securityGroups []string
		option         *bce.SignOption
	}
	tests := []struct {
		name    string
		c       *Client
		args    args
		wantErr bool
	}{
		{
			name: "",
			c: &Client{
				Client: &bce.Client{
					Config: &bce.Config{
						Credentials: &bce.Credentials{
							AccessKeyID:     "",
							SecretAccessKey: "",
						},
						Region:         "",
						Endpoint:       "",
						APIVersion:     "",
						Protocol:       "",
						UserAgent:      "",
						ProxyHost:      "",
						ProxyPort:      0,
						MaxConnections: 0,
						Timeout:        0,
						RetryPolicy:    nil,
						Checksum:       false,
					},
					HTTPClient: &http.Client{
						Transport: RoundTripFunc(func(req *http.Request) *http.Response {
							return &http.Response{
								StatusCode: http.StatusOK,
								Body:       ioutil.NopCloser(strings.NewReader(`{}`)),
							}
						}),
						CheckRedirect: func(*http.Request, []*http.Request) error { panic("not implemented") },
						Jar:           nil,
						Timeout:       0,
					},
				},
			},
			args: args{
				ctx:            context.Background(),
				instanceIDs:    []string{"xx"},
				securityGroups: []string{"gg"},
				option:         nil,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := tt.c.BindSecurityGroup(tt.args.ctx, tt.args.instanceIDs, tt.args.securityGroups, tt.args.option); (err != nil) != tt.wantErr {
				t.Errorf("Client.BindSecurityGroup() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestClient_UnbindSecurityGroup(t *testing.T) {
	type args struct {
		ctx             context.Context
		instanceID      string
		securityGroupID string
		option          *bce.SignOption
	}
	tests := []struct {
		name    string
		c       *Client
		args    args
		wantErr bool
	}{
		{
			name: "",
			c: &Client{
				Client: &bce.Client{
					Config: &bce.Config{
						Credentials: &bce.Credentials{
							AccessKeyID:     "",
							SecretAccessKey: "",
						},
						Region:         "",
						Endpoint:       "",
						APIVersion:     "",
						Protocol:       "",
						UserAgent:      "",
						ProxyHost:      "",
						ProxyPort:      0,
						MaxConnections: 0,
						Timeout:        0,
						RetryPolicy:    nil,
						Checksum:       false,
					},
					HTTPClient: &http.Client{
						Transport: RoundTripFunc(func(req *http.Request) *http.Response {
							return &http.Response{
								StatusCode: http.StatusOK,
								Body:       ioutil.NopCloser(strings.NewReader(`{}`)),
							}
						}),
						CheckRedirect: func(*http.Request, []*http.Request) error { panic("not implemented") },
						Jar:           nil,
						Timeout:       0,
					},
				},
			},
			args: args{
				ctx:             context.Background(),
				instanceID:      "xx",
				securityGroupID: "xx",
				option:          nil,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := tt.c.UnbindSecurityGroup(tt.args.ctx, tt.args.instanceID, tt.args.securityGroupID, tt.args.option); (err != nil) != tt.wantErr {
				t.Errorf("Client.UnbindSecurityGroup() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
