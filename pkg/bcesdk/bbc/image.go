/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package bbc

import (
	"context"
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
)

func (c *Client) GetImage(ctx context.Context, imageID string, option *bce.SignOption) (*Image, error) {
	if imageID == "" {
		return nil, fmt.Errorf("image ID is empty")
	}

	params := map[string]string{}

	req, err := bce.NewRequest("GET", c.GetURL("v1/image/"+imageID, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	imageResp := struct {
		Image *Image `json:"image"`
	}{}
	err = json.Unmarshal(bodyContent, &imageResp)
	if err != nil {
		return nil, err
	}

	return imageResp.Image, nil
}

func (c *Client) GetImages(ctx context.Context, request *GetImagesRequest, option *bce.SignOption) (*GetImagesResponse, error) {
	params := map[string]string{}

	if request.Marker != "" {
		params["marker"] = request.Marker
	}
	if request.MaxKeys != 0 {
		params["maxKeys"] = fmt.Sprintf("%d", request.MaxKeys)
	}
	if request.ImageType != "" {
		params["imageType"] = string(request.ImageType)
	}

	req, err := bce.NewRequest("GET", c.GetURL("/v1/image", params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var getImagesResponse *GetImagesResponse
	err = json.Unmarshal(bodyContent, &getImagesResponse)
	if err != nil {
		return nil, err
	}

	return getImagesResponse, nil
}

func (c *Client) GetFlavorImages(ctx context.Context, option *bce.SignOption) (*FlavorImagesResponse, error) {
	params := map[string]string{}

	req, err := bce.NewRequest("POST", c.GetURL("/v1/flavor/image", params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var response *FlavorImagesResponse
	err = json.Unmarshal(bodyContent, &response)
	if err != nil {
		return nil, err
	}

	return response, nil
}

func (c *Client) GetCustomFlavorImages(ctx context.Context, option *bce.SignOption) (*FlavorImagesResponse, error) {
	params := map[string]string{}

	req, err := bce.NewRequest("POST", c.GetURL("/v1/customFlavor/image", params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	var response *FlavorImagesResponse
	err = json.Unmarshal(bodyContent, &response)
	if err != nil {
		return nil, err
	}

	return response, nil
}
