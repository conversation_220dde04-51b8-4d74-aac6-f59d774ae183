/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package bbc

import (
	"context"
	"io/ioutil"
	"net/http"
	"reflect"
	"strings"
	"testing"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
)

func TestBatchAddIPArgs_validate(t *testing.T) {
	tests := []struct {
		name    string
		args    *BatchAddIPArgs
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := tt.args.validate(); (err != nil) != tt.wantErr {
				t.<PERSON>("BatchAddIPArgs.validate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestBatchDelIPArgs_validate(t *testing.T) {
	tests := []struct {
		name    string
		args    *BatchDelIPArgs
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := tt.args.validate(); (err != nil) != tt.wantErr {
				t.Errorf("BatchDelIPArgs.validate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestClient_BatchAddIP(t *testing.T) {
	type args struct {
		ctx     context.Context
		args    *BatchAddIPArgs
		signOpt *bce.SignOption
	}
	tests := []struct {
		name    string
		c       *Client
		args    args
		want    *BatchAddIPResponse
		wantErr bool
	}{
		{
			name: "",
			c: &Client{
				Client: &bce.Client{
					Config: &bce.Config{
						Credentials: &bce.Credentials{
							AccessKeyID:     "",
							SecretAccessKey: "",
						},
						Region:         "",
						Endpoint:       "",
						APIVersion:     "",
						Protocol:       "",
						UserAgent:      "",
						ProxyHost:      "",
						ProxyPort:      0,
						MaxConnections: 0,
						Timeout:        0,
						RetryPolicy:    nil,
						Checksum:       false,
					},
					HTTPClient: &http.Client{
						Transport: RoundTripFunc(func(req *http.Request) *http.Response {
							return &http.Response{
								StatusCode: http.StatusOK,
								Body:       ioutil.NopCloser(strings.NewReader(`{}`)),
							}
						}),
						CheckRedirect: func(*http.Request, []*http.Request) error { panic("not implemented") },
						Jar:           nil,
						Timeout:       0,
					},
				},
			},
			args: args{
				ctx: context.Background(),
				args: &BatchAddIPArgs{
					InstanceID:                     "xx",
					PrivateIPs:                     []string{},
					SecondaryPrivateIPAddressCount: 0,
				},
				signOpt: nil,
			},
			want: &BatchAddIPResponse{
				PrivateIPs: nil,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.c.BatchAddIP(tt.args.ctx, tt.args.args, tt.args.signOpt)
			if (err != nil) != tt.wantErr {
				t.Errorf("Client.BatchAddIP() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Client.BatchAddIP() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestClient_BatchDelIP(t *testing.T) {
	type args struct {
		ctx     context.Context
		args    *BatchDelIPArgs
		signOpt *bce.SignOption
	}
	tests := []struct {
		name    string
		c       *Client
		args    args
		wantErr bool
	}{
		{
			name: "",
			c: &Client{
				Client: &bce.Client{
					Config: &bce.Config{
						Credentials: &bce.Credentials{
							AccessKeyID:     "",
							SecretAccessKey: "",
						},
						Region:         "",
						Endpoint:       "",
						APIVersion:     "",
						Protocol:       "",
						UserAgent:      "",
						ProxyHost:      "",
						ProxyPort:      0,
						MaxConnections: 0,
						Timeout:        0,
						RetryPolicy:    nil,
						Checksum:       false,
					},
					HTTPClient: &http.Client{
						Transport: RoundTripFunc(func(req *http.Request) *http.Response {
							return &http.Response{
								StatusCode: http.StatusOK,
								Body:       ioutil.NopCloser(strings.NewReader(`{}`)),
							}
						}),
						CheckRedirect: func(*http.Request, []*http.Request) error { panic("not implemented") },
						Jar:           nil,
						Timeout:       0,
					},
				},
			},
			args: args{
				ctx: context.Background(),
				args: &BatchDelIPArgs{
					InstanceID: "xx",
					PrivateIPs: []string{""},
				},
				signOpt: nil,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := tt.c.BatchDelIP(tt.args.ctx, tt.args.args, tt.args.signOpt); (err != nil) != tt.wantErr {
				t.Errorf("Client.BatchDelIP() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestClient_GetInstanceENI(t *testing.T) {
	type args struct {
		ctx        context.Context
		instanceID string
		signOpt    *bce.SignOption
	}
	tests := []struct {
		name    string
		c       *Client
		args    args
		want    *GetInstanceENIResponse
		wantErr bool
	}{
		{
			name: "",
			c: &Client{
				Client: &bce.Client{
					Config: &bce.Config{
						Credentials: &bce.Credentials{
							AccessKeyID:     "",
							SecretAccessKey: "",
						},
						Region:         "",
						Endpoint:       "",
						APIVersion:     "",
						Protocol:       "",
						UserAgent:      "",
						ProxyHost:      "",
						ProxyPort:      0,
						MaxConnections: 0,
						Timeout:        0,
						RetryPolicy:    nil,
						Checksum:       false,
					},
					HTTPClient: &http.Client{
						Transport: RoundTripFunc(func(req *http.Request) *http.Response {
							return &http.Response{
								StatusCode: http.StatusOK,
								Body:       ioutil.NopCloser(strings.NewReader(`{}`)),
							}
						}),
						CheckRedirect: func(*http.Request, []*http.Request) error { panic("not implemented") },
						Jar:           nil,
						Timeout:       0,
					},
				},
			},
			args: args{
				ctx:        context.Background(),
				instanceID: "xx",
				signOpt:    nil,
			},
			want: &GetInstanceENIResponse{
				ENIID:        "",
				Name:         "",
				ZoneName:     "",
				Description:  "",
				InstanceID:   "",
				MacAddress:   "",
				VPCID:        "",
				SubnetID:     "",
				Status:       "",
				PrivateIPSet: nil,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.c.GetInstanceENI(tt.args.ctx, tt.args.instanceID, tt.args.signOpt)
			if (err != nil) != tt.wantErr {
				t.Errorf("Client.GetInstanceENI() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Client.GetInstanceENI() = %v, want %v", got, tt.want)
			}
		})
	}
}
