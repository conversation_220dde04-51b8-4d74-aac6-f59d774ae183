/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package bbc

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
)

//instanceIds和securityGroups不同于BCC的字符串形式，在BBC中是list
func (c *Client) BindSecurityGroup(ctx context.Context, instanceIDs, securityGroups []string, option *bce.SignOption) error {
	if len(instanceIDs) == 0 || securityGroups == nil {
		return fmt.Errorf("instanceID or securityGroupID is empty")
	}

	params := map[string]string{
		"bind": "",
	}

	body := struct {
		InstanceIds    []string `json:"instanceIds"`
		SecurityGroups []string `json:"securityGroups"`
	}{
		InstanceIds:    instanceIDs,
		SecurityGroups: securityGroups,
	}
	postContent, err := json.Marshal(body)
	if err != nil {
		return err
	}

	req, err := bce.NewRequest("POST", c.GetURL("v1/instance/securitygroup", params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	return nil
}

func (c *Client) UnbindSecurityGroup(ctx context.Context, instanceID, securityGroupID string, option *bce.SignOption) error {
	if instanceID == "" || securityGroupID == "" {
		return fmt.Errorf("instanceID or securityGroupID is empty")
	}

	params := map[string]string{
		"unbind": "",
	}

	body := struct {
		InstanceID      string `json:"instanceId"`
		SecurityGroupId string `json:"securityGroupId"`
	}{
		InstanceID:      instanceID,
		SecurityGroupId: securityGroupID,
	}
	postContent, err := json.Marshal(body)
	if err != nil {
		return err
	}

	req, err := bce.NewRequest("POST", c.GetURL("v1/instance/securitygroup", params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	if err != nil {
		return err
	}

	return nil
}
