// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bbc (interfaces: Interface)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	bbc "github.com/baidubce/bce-sdk-go/services/bbc"
	gomock "github.com/golang/mock/gomock"
	bbc0 "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bbc"
	bce "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
	tag "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/tag"
)

// MockInterface is a mock of Interface interface.
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface.
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance.
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// BatchAddIP mocks base method.
func (m *MockInterface) BatchAddIP(arg0 context.Context, arg1 *bbc0.BatchAddIPArgs, arg2 *bce.SignOption) (*bbc0.BatchAddIPResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchAddIP", arg0, arg1, arg2)
	ret0, _ := ret[0].(*bbc0.BatchAddIPResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchAddIP indicates an expected call of BatchAddIP.
func (mr *MockInterfaceMockRecorder) BatchAddIP(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchAddIP", reflect.TypeOf((*MockInterface)(nil).BatchAddIP), arg0, arg1, arg2)
}

// BatchDelIP mocks base method.
func (m *MockInterface) BatchDelIP(arg0 context.Context, arg1 *bbc0.BatchDelIPArgs, arg2 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDelIP", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchDelIP indicates an expected call of BatchDelIP.
func (mr *MockInterfaceMockRecorder) BatchDelIP(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDelIP", reflect.TypeOf((*MockInterface)(nil).BatchDelIP), arg0, arg1, arg2)
}

// BindSecurityGroup mocks base method.
func (m *MockInterface) BindSecurityGroup(arg0 context.Context, arg1, arg2 []string, arg3 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BindSecurityGroup", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// BindSecurityGroup indicates an expected call of BindSecurityGroup.
func (mr *MockInterfaceMockRecorder) BindSecurityGroup(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BindSecurityGroup", reflect.TypeOf((*MockInterface)(nil).BindSecurityGroup), arg0, arg1, arg2, arg3)
}

// BindTags mocks base method.
func (m *MockInterface) BindTags(arg0 context.Context, arg1 string, arg2 []tag.Tag, arg3 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BindTags", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// BindTags indicates an expected call of BindTags.
func (mr *MockInterfaceMockRecorder) BindTags(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BindTags", reflect.TypeOf((*MockInterface)(nil).BindTags), arg0, arg1, arg2, arg3)
}

// CreateInstance mocks base method.
func (m *MockInterface) CreateInstance(arg0 context.Context, arg1 *bbc0.CreateInstanceArgsShell, arg2 string, arg3 *bce.SignOption) (*bbc.CreateInstanceResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateInstance", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*bbc.CreateInstanceResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateInstance indicates an expected call of CreateInstance.
func (mr *MockInterfaceMockRecorder) CreateInstance(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateInstance", reflect.TypeOf((*MockInterface)(nil).CreateInstance), arg0, arg1, arg2, arg3)
}

// GetCustomFlavorImages mocks base method.
func (m *MockInterface) GetCustomFlavorImages(arg0 context.Context, arg1 *bce.SignOption) (*bbc0.FlavorImagesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCustomFlavorImages", arg0, arg1)
	ret0, _ := ret[0].(*bbc0.FlavorImagesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCustomFlavorImages indicates an expected call of GetCustomFlavorImages.
func (mr *MockInterfaceMockRecorder) GetCustomFlavorImages(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCustomFlavorImages", reflect.TypeOf((*MockInterface)(nil).GetCustomFlavorImages), arg0, arg1)
}

// GetFlavorImages mocks base method.
func (m *MockInterface) GetFlavorImages(arg0 context.Context, arg1 *bce.SignOption) (*bbc0.FlavorImagesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFlavorImages", arg0, arg1)
	ret0, _ := ret[0].(*bbc0.FlavorImagesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFlavorImages indicates an expected call of GetFlavorImages.
func (mr *MockInterfaceMockRecorder) GetFlavorImages(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFlavorImages", reflect.TypeOf((*MockInterface)(nil).GetFlavorImages), arg0, arg1)
}

// GetFlavors mocks base method.
func (m *MockInterface) GetFlavors(arg0 context.Context, arg1 *bce.SignOption) (*bbc.ListFlavorsResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFlavors", arg0, arg1)
	ret0, _ := ret[0].(*bbc.ListFlavorsResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFlavors indicates an expected call of GetFlavors.
func (mr *MockInterfaceMockRecorder) GetFlavors(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFlavors", reflect.TypeOf((*MockInterface)(nil).GetFlavors), arg0, arg1)
}

// GetImage mocks base method.
func (m *MockInterface) GetImage(arg0 context.Context, arg1 string, arg2 *bce.SignOption) (*bbc0.Image, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetImage", arg0, arg1, arg2)
	ret0, _ := ret[0].(*bbc0.Image)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetImage indicates an expected call of GetImage.
func (mr *MockInterfaceMockRecorder) GetImage(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetImage", reflect.TypeOf((*MockInterface)(nil).GetImage), arg0, arg1, arg2)
}

// GetImages mocks base method.
func (m *MockInterface) GetImages(arg0 context.Context, arg1 *bbc0.GetImagesRequest, arg2 *bce.SignOption) (*bbc0.GetImagesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetImages", arg0, arg1, arg2)
	ret0, _ := ret[0].(*bbc0.GetImagesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetImages indicates an expected call of GetImages.
func (mr *MockInterfaceMockRecorder) GetImages(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetImages", reflect.TypeOf((*MockInterface)(nil).GetImages), arg0, arg1, arg2)
}

// GetInstance mocks base method.
func (m *MockInterface) GetInstance(arg0 context.Context, arg1 string, arg2 *bce.SignOption) (*bbc0.Instance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstance", arg0, arg1, arg2)
	ret0, _ := ret[0].(*bbc0.Instance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstance indicates an expected call of GetInstance.
func (mr *MockInterfaceMockRecorder) GetInstance(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstance", reflect.TypeOf((*MockInterface)(nil).GetInstance), arg0, arg1, arg2)
}

// GetInstanceENI mocks base method.
func (m *MockInterface) GetInstanceENI(arg0 context.Context, arg1 string, arg2 *bce.SignOption) (*bbc0.GetInstanceENIResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstanceENI", arg0, arg1, arg2)
	ret0, _ := ret[0].(*bbc0.GetInstanceENIResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstanceENI indicates an expected call of GetInstanceENI.
func (mr *MockInterfaceMockRecorder) GetInstanceENI(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstanceENI", reflect.TypeOf((*MockInterface)(nil).GetInstanceENI), arg0, arg1, arg2)
}

// GetInstanceWithDeploySet mocks base method.
func (m *MockInterface) GetInstanceWithDeploySet(arg0 context.Context, arg1 string, arg2 bool, arg3 *bce.SignOption) (*bbc0.Instance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstanceWithDeploySet", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*bbc0.Instance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstanceWithDeploySet indicates an expected call of GetInstanceWithDeploySet.
func (mr *MockInterfaceMockRecorder) GetInstanceWithDeploySet(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstanceWithDeploySet", reflect.TypeOf((*MockInterface)(nil).GetInstanceWithDeploySet), arg0, arg1, arg2, arg3)
}

// QueryPrice mocks base method.
func (m *MockInterface) QueryPrice(arg0 context.Context, arg1 *bbc0.PriceRequest, arg2 *bce.SignOption) (*bbc0.PriceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryPrice", arg0, arg1, arg2)
	ret0, _ := ret[0].(*bbc0.PriceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryPrice indicates an expected call of QueryPrice.
func (mr *MockInterfaceMockRecorder) QueryPrice(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryPrice", reflect.TypeOf((*MockInterface)(nil).QueryPrice), arg0, arg1, arg2)
}

// SetDebug mocks base method.
func (m *MockInterface) SetDebug(arg0 bool) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetDebug", arg0)
}

// SetDebug indicates an expected call of SetDebug.
func (mr *MockInterfaceMockRecorder) SetDebug(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDebug", reflect.TypeOf((*MockInterface)(nil).SetDebug), arg0)
}

// UnbindSecurityGroup mocks base method.
func (m *MockInterface) UnbindSecurityGroup(arg0 context.Context, arg1, arg2 string, arg3 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnbindSecurityGroup", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnbindSecurityGroup indicates an expected call of UnbindSecurityGroup.
func (mr *MockInterfaceMockRecorder) UnbindSecurityGroup(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnbindSecurityGroup", reflect.TypeOf((*MockInterface)(nil).UnbindSecurityGroup), arg0, arg1, arg2, arg3)
}

// UnbindTags mocks base method.
func (m *MockInterface) UnbindTags(arg0 context.Context, arg1 string, arg2 []tag.Tag, arg3 *bce.SignOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnbindTags", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnbindTags indicates an expected call of UnbindTags.
func (mr *MockInterfaceMockRecorder) UnbindTags(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnbindTags", reflect.TypeOf((*MockInterface)(nil).UnbindTags), arg0, arg1, arg2, arg3)
}
