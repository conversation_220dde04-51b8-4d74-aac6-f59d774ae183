/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package bbc

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"
	"time"

	bbcapi "github.com/baidubce/bce-sdk-go/services/bbc"
	bccapi "github.com/baidubce/bce-sdk-go/services/bcc/api"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bccimage"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/sts"
)

func ToJSON(obj interface{}) string {
	if str, err := json.Marshal(obj); err == nil {
		return string(str)
	}

	return ""
}

var bbcclient *Client

func init() {
	accessKeyID := "********************************"
	secretAccessKey := "6b109261727a4fee8fc5db4bf5ec6c51"

	// accessKeyID := "********************************"
	// secretAccessKey := "17cb19f68d40427996466932f8081690"

	bbcclient = NewClient(&bce.Config{
		Credentials: bce.NewCredentials(accessKeyID, secretAccessKey),
		Checksum:    true,
		Timeout:     30 * time.Second,
		Endpoint:    Endpoints["sandbox"],
	})
	bbcclient.SetDebug(true)
}

// {
// 	"name": name,
// 	"hostname": hostname,
// 	"flavorId":flavorId,
// 	"imageId": imageId,
// 	"raidId":raidId,
// 	"rootDiskSizeInGb":rootDiskSizeInGb,
// 	"purchaseCount": purchaseCount,
// 	"zoneName":zoneName,
// 	"subnetId":subnetId,
// 	"securityGroupId": securityGroupId,
// 	"autoRenewTimeUnit": autoRenewTimeUnit,
// 	"autoRenewTime": autoRenewTime,
// 	"billing":{
// 		"paymentTiming": paymentTiming,
// 		"reservation": {
// 			"reservationLength": reservationLength
// 		}
// 	}
// }

func testCreateInstance(t *testing.T) {
	clientToken := "lql-testlql-testlql-test1"
	encrpyPassword, err := bccapi.Aes128EncryptUseSecreteKey("6b109261727a4fee", "xhsjhkj4%w@oip")
	if err != nil {
		t.Errorf("Aes128EncryptUseSecreteKey failed: %s", err)
	}

	// 线上bj
	// args := bbcapi.CreateInstanceArgs{
	// 	Name: "lql-test",
	// 	// Hostname: "",
	// 	FlavorId:         "BBC-I4-01S",
	// 	ImageId:          "m-FupNsbWv",
	// 	RaidId:           "raid-6rKrxEwi",
	// 	RootDiskSizeInGb: 20,
	// 	PurchaseCount:    1,
	// 	ZoneName:         "cn-bj-e",
	// 	SubnetId:         "sbn-92cuj1c4grqv",
	// 	SecurityGroupId:  "g-0979335ayavp",
	// 	Billing: bbcapi.Billing{
	// 		PaymentTiming: "Postpaid",
	// 	},
	// 	AdminPass: encrpyPassword,
	// }

	args := CreateInstanceArgsShell{
		CreateInstanceArgs: bbcapi.CreateInstanceArgs{
			Name: "lql-test",
			// Hostname: "",
			FlavorId:         "BBC-I3-01S",
			ImageId:          "m-PSbhzT9x",
			RaidId:           "raid-KOh4qTRC",
			RootDiskSizeInGb: 20,
			PurchaseCount:    1,
			ZoneName:         "cn-bj-a",
			SubnetId:         "sbn-muebdgm5b42r",
			SecurityGroupId:  "g-41ihdmkwaxa5",
			Billing: bbcapi.Billing{
				PaymentTiming: "Postpaid",
			},
			AdminPass: encrpyPassword,
		},
	}

	result, err := bbcclient.CreateInstance(context.TODO(), &args, clientToken, NewSignOption())
	if err != nil {
		t.Logf("CreateInstance failed: %v", err)
	}

	t.Logf("instanceID: %s", result.InstanceIds[0])

}

func testCreateInstanceBySignOption(t *testing.T) {
	clientToken := "lql-testlql-testlql-testwwhhhdccd"
	ctx := context.TODO()
	userID := "eca97e148cb74e9683d7b7240829d1ff"

	// 初始化 sts client
	stsclient := sts.NewClient(ctx, &bce.Config{
		Endpoint: "sts.bj.iam.sdns.baidu.com:8586/v1",
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   "bj",
	}, &bce.Config{
		Endpoint: "iam.bj.bce-internal.baidu.com/v3",
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   "bj",
	}, "BceServiceRole_SERVICE_CCE", "cce", "c8QBqhOKxmGu0Sl2p13guMil3U2grbYt")

	// 使用 SK
	encrpyPassword, signOption, err := GetPasswordAndSignOption(ctx, stsclient, userID, "xhsjhkj4%w@oip")
	if err != nil {
		t.Errorf("Aes128EncryptUseSecreteKey failed: %s", err)
	}

	client := NewClient(&bce.Config{
		Checksum: true,
		Timeout:  30 * time.Second,
		Endpoint: Endpoints["bj"],
	})

	client.SetDebug(true)

	// 线上bj
	args := CreateInstanceArgsShell{
		CreateInstanceArgs: bbcapi.CreateInstanceArgs{
			Name: "lql-test",
			// Hostname: "",
			FlavorId:         "BBC-I4-01S",
			ImageId:          "m-FupNsbWv",
			RaidId:           "raid-6rKrxEwi",
			RootDiskSizeInGb: 20,
			PurchaseCount:    1,
			ZoneName:         "cn-bj-e",
			SubnetId:         "sbn-92cuj1c4grqv",
			SecurityGroupId:  "",
			Billing: bbcapi.Billing{
				PaymentTiming: "Postpaid",
			},
			AdminPass: encrpyPassword,
		},
		EnterpriseSecurityGroupID: "esg-qbii6qijaxc9",
	}

	// args := bbcapi.CreateInstanceArgs{
	// 	Name: "lql-1",
	// 	// Hostname: "",
	// 	FlavorId:         "BBC-I3-01S",
	// 	ImageId:          "m-PSbhzT9x",
	// 	RaidId:           "raid-KOh4qTRC",
	// 	RootDiskSizeInGb: 20,
	// 	PurchaseCount:    1,
	// 	ZoneName:         "cn-bj-a",
	// 	SubnetId:         "sbn-muebdgm5b42r",
	// 	SecurityGroupId:  "g-41ihdmkwaxa5",
	// 	Billing: bbcapi.Billing{
	// 		PaymentTiming: "Postpaid",
	// 	},
	// 	AdminPass: encrpyPassword,
	// }

	resp, err := client.CreateInstance(ctx, &args, clientToken, signOption)
	if err != nil {
		t.Errorf("CreateInstance failed: %v", err)
	}

	if respByte, err := json.Marshal(resp); err == nil {
		t.Logf("CreateInstance succeeded: %s", string(respByte))
	}
}

func GetPasswordAndSignOption(ctx context.Context, stsclient *sts.Client, userID string, password string) (string, *bce.SignOption, error) {
	cred, err := stsclient.GetCredential(ctx, userID)
	if err != nil {
		return "", nil, err
	}

	if cred == nil {
		return "", nil, fmt.Errorf("GetCredential return nil")
	}
	fmt.Printf("ak: %s", cred.AccessKeyID)
	fmt.Printf("sk: %s", cred.SecretAccessKey)

	encrpyPassword, err := bccapi.Aes128EncryptUseSecreteKey("fe2e4b7c70234a2c926a159112996820", password)
	if err != nil {
		return "", nil, err
	}

	// 构建 SignOption
	option := &bce.SignOption{}

	// option.AddHeader("X-Bce-Request-Id", logger.GetUUID())
	option.AddHeader("X-Auth-Token", cred.Token.ID)
	option.AddHeader("X-Bce-Security-Token", cred.SessionToken)
	option.Credentials = bce.NewCredentials(cred.AccessKeyID, cred.SecretAccessKey)

	// 传入服务账号 AK, 用于 BCC 查询 SK 解码 AdminPass
	option.AddHeader("encrypted-key", "c0d3690042364b47a8e5b91e3d834b83")

	option.AddHeadersToSign("host")

	return encrpyPassword, option, nil
}

func testGetInstance(t *testing.T) {
	instance, err := bbcclient.GetInstance(context.TODO(), "i-RxzyyNBQ", NewSignOption())
	if err != nil {
		t.Errorf("GetInstance failed: %v", err)
		return
	}
	t.Logf("GetInstance succeeded: %s", ToJSON(instance))
}

func testGetImage(t *testing.T) {
	image, err := bbcclient.GetImage(context.TODO(), "m-zGnlzI5v", NewSignOption())
	if err != nil {
		t.Errorf("GetImage failed: %v", err)
		return
	}
	t.Logf("GetImage succeeded: %s", ToJSON(image))
}

func testGetImages(t *testing.T) {
	req := &GetImagesRequest{
		Marker:    "",
		MaxKeys:   10,
		ImageType: bccimage.ImageTypeBBCSystem,
	}
	resp, err := bbcclient.GetImages(context.TODO(), req, NewSignOption())
	if err != nil {
		t.Errorf("GetImages failed: %v", err)
		return
	}
	t.Logf("GetImages succeeded: %s", ToJSON(resp))
}

func testGetPrice(t *testing.T) {
	req := &PriceRequest{
		PurchaseCount: 1,
		FlavorID:      "BBC-G3-01",
		Billing: Billing{
			PaymentTiming: "Postpaid",
		},
	}

	resp, err := bbcclient.QueryPrice(context.TODO(), req, NewSignOption())
	if err != nil {
		t.Errorf("GetPrice failed: %v", err)
		return
	}

	t.Logf("GetPrice : %s", ToJSON(resp))
}
