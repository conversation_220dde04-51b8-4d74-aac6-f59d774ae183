/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package bbc

import (
	"context"
	"io/ioutil"
	"net/http"
	"reflect"
	"strings"
	"testing"

	bbcapi "github.com/baidubce/bce-sdk-go/services/bbc"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/tag"
)

type RoundTripFunc func(req *http.Request) *http.Response

func (f RoundTripFunc) RoundTrip(req *http.Request) (*http.Response, error) {
	return f(req), nil
}

func TestClient_CreateInstance(t *testing.T) {
	type fields struct {
		Client *bce.Client
	}
	type args struct {
		ctx         context.Context
		args        *CreateInstanceArgsShell
		clientToken string
		option      *bce.SignOption
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *bbcapi.CreateInstanceResult
		wantErr bool
	}{
		{
			name: "正常流程",
			fields: fields{
				Client: &bce.Client{
					Config: &bce.Config{
						Credentials: &bce.Credentials{},
					},
					HTTPClient: &http.Client{
						Transport: RoundTripFunc(func(req *http.Request) *http.Response {
							return &http.Response{
								StatusCode: http.StatusOK,
								Body: ioutil.NopCloser(strings.NewReader(`{
									"instanceIds": []
								}`)),
							}
						}),
					},
				},
			},
			args: args{
				ctx: context.TODO(),
				args: &CreateInstanceArgsShell{
					CreateInstanceArgs: bbcapi.CreateInstanceArgs{
						FlavorId:          "",
						ImageId:           "",
						RaidId:            "",
						RootDiskSizeInGb:  0,
						PurchaseCount:     0,
						ZoneName:          "",
						SubnetId:          "",
						AutoRenewTimeUnit: "",
						AutoRenewTime:     0,
						Billing: bbcapi.Billing{
							PaymentTiming: "",
							Reservation: bbcapi.Reservation{
								Length:   0,
								TimeUnit: "",
							},
						},
						Name:                      "",
						Hostname:                  "",
						AdminPass:                 "",
						DeploySetId:               "",
						ClientToken:               "",
						SecurityGroupId:           "",
						EnterpriseSecurityGroupId: "",
						InternalIps:               []string{},
						RequestToken:              "",
						EnableNuma:                false,
						EnableHt:                  false,
						RootPartitionType:         "",
						DataPartitionType:         "",
					},
					EnterpriseSecurityGroupID: "",
				},
				clientToken: "",
				option:      nil,
			},
			want: &bbcapi.CreateInstanceResult{
				InstanceIds: []string{},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Client{
				Client: tt.fields.Client,
			}
			got, err := c.CreateInstance(tt.args.ctx, tt.args.args, tt.args.clientToken, tt.args.option)
			if (err != nil) != tt.wantErr {
				t.Errorf("Client.CreateInstance() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Client.CreateInstance() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestClient_GetInstance(t *testing.T) {
	type args struct {
		ctx        context.Context
		instanceID string
		option     *bce.SignOption
	}
	tests := []struct {
		name    string
		c       *Client
		args    args
		want    *Instance
		wantErr bool
	}{
		{
			name: "",
			c: &Client{
				Client: &bce.Client{
					Config: &bce.Config{
						Credentials: &bce.Credentials{
							AccessKeyID:     "",
							SecretAccessKey: "",
						},
						Region:         "",
						Endpoint:       "",
						APIVersion:     "",
						Protocol:       "",
						UserAgent:      "",
						ProxyHost:      "",
						ProxyPort:      0,
						MaxConnections: 0,
						Timeout:        0,
						RetryPolicy:    nil,
						Checksum:       false,
					},
					HTTPClient: &http.Client{
						Transport: RoundTripFunc(func(req *http.Request) *http.Response {
							return &http.Response{
								StatusCode: http.StatusOK,
								Body:       ioutil.NopCloser(strings.NewReader(`{}`)),
							}
						}),
						CheckRedirect: func(*http.Request, []*http.Request) error { panic("not implemented") },
						Jar:           nil,
						Timeout:       0,
					},
				},
			},
			args: args{
				ctx:        context.TODO(),
				instanceID: "xx",
				option:     nil,
			},
			want: &Instance{
				InstanceID:      "",
				InstanceName:    "",
				Status:          "",
				PaymentTiming:   "",
				AutoRenew:       false,
				Description:     "",
				InternalIP:      "",
				PublicIP:        "",
				BandwidthInMbps: 0,
				ImageID:         "",
				FlavorID:        "",
				AvailableZone:   "",
				Region:          "",
				CreateTime:      "",
				ExpireTime:      "",
				RackID:          "",
				HostID:          "",
				SwitchID:        "",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.c.GetInstance(tt.args.ctx, tt.args.instanceID, tt.args.option)
			if (err != nil) != tt.wantErr {
				t.Errorf("Client.GetInstance() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Client.GetInstance() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestClient_GetInstanceWithDeploySet(t *testing.T) {
	type args struct {
		ctx         context.Context
		instanceID  string
		isDeploySet bool
		option      *bce.SignOption
	}
	tests := []struct {
		name    string
		c       *Client
		args    args
		want    *Instance
		wantErr bool
	}{
		{
			name: "",
			c: &Client{
				Client: &bce.Client{
					Config: &bce.Config{
						Credentials: &bce.Credentials{
							AccessKeyID:     "",
							SecretAccessKey: "",
						},
						Region:         "",
						Endpoint:       "",
						APIVersion:     "",
						Protocol:       "",
						UserAgent:      "",
						ProxyHost:      "",
						ProxyPort:      0,
						MaxConnections: 0,
						Timeout:        0,
						RetryPolicy:    nil,
						Checksum:       false,
					},
					HTTPClient: &http.Client{
						Transport: RoundTripFunc(func(req *http.Request) *http.Response {
							return &http.Response{
								StatusCode: http.StatusOK,
								Body:       ioutil.NopCloser(strings.NewReader(`{}`)),
							}
						}),
						CheckRedirect: func(*http.Request, []*http.Request) error { panic("not implemented") },
						Jar:           nil,
						Timeout:       0,
					},
				},
			},
			args: args{
				ctx:         context.TODO(),
				instanceID:  "xx",
				isDeploySet: false,
				option:      nil,
			},
			want: &Instance{
				InstanceID:      "",
				InstanceName:    "",
				Status:          "",
				PaymentTiming:   "",
				AutoRenew:       false,
				Description:     "",
				InternalIP:      "",
				PublicIP:        "",
				BandwidthInMbps: 0,
				ImageID:         "",
				FlavorID:        "",
				AvailableZone:   "",
				Region:          "",
				CreateTime:      "",
				ExpireTime:      "",
				RackID:          "",
				HostID:          "",
				SwitchID:        "",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.c.GetInstanceWithDeploySet(tt.args.ctx, tt.args.instanceID, tt.args.isDeploySet, tt.args.option)
			if (err != nil) != tt.wantErr {
				t.Errorf("Client.GetInstanceWithDeploySet() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Client.GetInstanceWithDeploySet() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestClient_QueryPrice(t *testing.T) {
	type args struct {
		ctx     context.Context
		args    *PriceRequest
		signOpt *bce.SignOption
	}
	tests := []struct {
		name    string
		c       *Client
		args    args
		want    *PriceResponse
		wantErr bool
	}{
		{
			name: "",
			c: &Client{
				Client: &bce.Client{
					Config: &bce.Config{
						Credentials: &bce.Credentials{
							AccessKeyID:     "",
							SecretAccessKey: "",
						},
						Region:         "",
						Endpoint:       "",
						APIVersion:     "",
						Protocol:       "",
						UserAgent:      "",
						ProxyHost:      "",
						ProxyPort:      0,
						MaxConnections: 0,
						Timeout:        0,
						RetryPolicy:    nil,
						Checksum:       false,
					},
					HTTPClient: &http.Client{
						Transport: RoundTripFunc(func(req *http.Request) *http.Response {
							return &http.Response{
								StatusCode: http.StatusOK,
								Body:       ioutil.NopCloser(strings.NewReader(`{}`)),
							}
						}),
						CheckRedirect: func(*http.Request, []*http.Request) error { panic("not implemented") },
						Jar:           nil,
						Timeout:       0,
					},
				},
			},
			args: args{
				ctx: context.Background(),
				args: &PriceRequest{
					FlavorID:      "",
					PurchaseCount: 0,
					Billing: Billing{
						PaymentTiming: "",
						Reservation: Reservation{
							ReservationLength: 0,
						},
					},
				},
				signOpt: nil,
			},
			want: &PriceResponse{
				Price: "",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.c.QueryPrice(tt.args.ctx, tt.args.args, tt.args.signOpt)
			if (err != nil) != tt.wantErr {
				t.Errorf("Client.QueryPrice() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Client.QueryPrice() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestClient_BindTags(t *testing.T) {
	type args struct {
		ctx        context.Context
		instanceID string
		tags       []tag.Tag
		option     *bce.SignOption
	}
	tests := []struct {
		name    string
		c       *Client
		args    args
		wantErr bool
	}{
		{
			name: "",
			c: &Client{
				Client: &bce.Client{
					Config: &bce.Config{
						Credentials: &bce.Credentials{
							AccessKeyID:     "",
							SecretAccessKey: "",
						},
						Region:         "",
						Endpoint:       "",
						APIVersion:     "",
						Protocol:       "",
						UserAgent:      "",
						ProxyHost:      "",
						ProxyPort:      0,
						MaxConnections: 0,
						Timeout:        0,
						RetryPolicy:    nil,
						Checksum:       false,
					},
					HTTPClient: &http.Client{
						Transport: RoundTripFunc(func(req *http.Request) *http.Response {
							return &http.Response{
								StatusCode: http.StatusOK,
								Body:       ioutil.NopCloser(strings.NewReader(`{}`)),
							}
						}),
						CheckRedirect: func(*http.Request, []*http.Request) error { panic("not implemented") },
						Jar:           nil,
						Timeout:       0,
					},
				},
			},
			args: args{
				ctx:        context.Background(),
				instanceID: "xx",
				tags:       []tag.Tag{},
				option:     nil,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := tt.c.BindTags(tt.args.ctx, tt.args.instanceID, tt.args.tags, tt.args.option); (err != nil) != tt.wantErr {
				t.Errorf("Client.BindTags() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestClient_UnbindTags(t *testing.T) {
	type args struct {
		ctx        context.Context
		instanceID string
		tags       []tag.Tag
		option     *bce.SignOption
	}
	tests := []struct {
		name    string
		c       *Client
		args    args
		wantErr bool
	}{
		{
			name: "",
			c: &Client{
				Client: &bce.Client{
					Config: &bce.Config{
						Credentials: &bce.Credentials{
							AccessKeyID:     "",
							SecretAccessKey: "",
						},
						Region:         "",
						Endpoint:       "",
						APIVersion:     "",
						Protocol:       "",
						UserAgent:      "",
						ProxyHost:      "",
						ProxyPort:      0,
						MaxConnections: 0,
						Timeout:        0,
						RetryPolicy:    nil,
						Checksum:       false,
					},
					HTTPClient: &http.Client{
						Transport: RoundTripFunc(func(req *http.Request) *http.Response {
							return &http.Response{
								StatusCode: http.StatusOK,
								Body:       ioutil.NopCloser(strings.NewReader(`{}`)),
							}
						}),
						CheckRedirect: func(*http.Request, []*http.Request) error { panic("not implemented") },
						Jar:           nil,
						Timeout:       0,
					},
				},
			},
			args: args{
				ctx:        context.Background(),
				instanceID: "xx",
				tags:       []tag.Tag{},
				option:     nil,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := tt.c.UnbindTags(tt.args.ctx, tt.args.instanceID, tt.args.tags, tt.args.option); (err != nil) != tt.wantErr {
				t.Errorf("Client.UnbindTags() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestClient_GetFlavors(t *testing.T) {
	type args struct {
		ctx    context.Context
		option *bce.SignOption
	}
	tests := []struct {
		name    string
		c       *Client
		args    args
		want    *bbcapi.ListFlavorsResult
		wantErr bool
	}{
		{
			name: "",
			c: &Client{
				Client: &bce.Client{
					Config: &bce.Config{
						Credentials: &bce.Credentials{
							AccessKeyID:     "",
							SecretAccessKey: "",
						},
						Region:         "",
						Endpoint:       "",
						APIVersion:     "",
						Protocol:       "",
						UserAgent:      "",
						ProxyHost:      "",
						ProxyPort:      0,
						MaxConnections: 0,
						Timeout:        0,
						RetryPolicy:    nil,
						Checksum:       false,
					},
					HTTPClient: &http.Client{
						Transport: RoundTripFunc(func(req *http.Request) *http.Response {
							return &http.Response{
								StatusCode: http.StatusOK,
								Body:       ioutil.NopCloser(strings.NewReader(`{}`)),
							}
						}),
						CheckRedirect: func(*http.Request, []*http.Request) error { panic("not implemented") },
						Jar:           nil,
						Timeout:       0,
					},
				},
			},
			args: args{
				ctx:    context.Background(),
				option: nil,
			},
			want: &bbcapi.ListFlavorsResult{
				Flavors: nil,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.c.GetFlavors(tt.args.ctx, tt.args.option)
			if (err != nil) != tt.wantErr {
				t.Errorf("Client.GetFlavors() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Client.GetFlavors() = %v, want %v", got, tt.want)
			}
		})
	}
}
