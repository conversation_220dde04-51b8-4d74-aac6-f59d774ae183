/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package bbc

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"

	bbcapi "github.com/baidubce/bce-sdk-go/services/bbc"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/tag"
)

func (c *Client) CreateInstance(
	ctx context.Context,
	args *CreateInstanceArgsShell,
	clientToken string,
	option *bce.SignOption) (*bbcapi.CreateInstanceResult, error) {
	if args == nil {
		return nil, fmt.Errorf("args is empty")
	}

	params := map[string]string{}
	params["clientToken"] = clientToken

	url := fmt.Sprintf("/v1/instance")

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL(url, params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	result := new(bbcapi.CreateInstanceResult)
	err = json.Unmarshal(bodyContent, result)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (c *Client) GetInstance(ctx context.Context, instanceID string, option *bce.SignOption) (*Instance, error) {
	return c.GetInstanceWithDeploySet(ctx, instanceID, false, option)
}

func (c *Client) GetInstanceWithDeploySet(ctx context.Context, instanceID string, isDeploySet bool, option *bce.SignOption) (*Instance, error) {
	params := map[string]string{}
	if isDeploySet {
		params = map[string]string{"isDeploySet": "true"}
	}
	if instanceID == "" {
		return nil, fmt.Errorf("GetInstance failed: instanceID is empty")
	}

	url := fmt.Sprintf("/v1/instance/%s", instanceID)
	req, err := bce.NewRequest("GET", c.GetURL(url, params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	instance := new(Instance)
	err = json.Unmarshal(bodyContent, instance)
	if err != nil {
		return nil, err
	}

	return instance, nil
}

func (c *Client) QueryPrice(ctx context.Context, args *PriceRequest, signOpt *bce.SignOption) (*PriceResponse, error) {
	if args == nil {
		return nil, fmt.Errorf("QueryPrice failed: PriceRequest is empty")
	}

	params := map[string]string{}

	postContent, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}

	req, err := bce.NewRequest("POST", c.GetURL("/v1/instance/price", params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, signOpt)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	response := new(PriceResponse)
	err = json.Unmarshal(bodyContent, response)
	if err != nil {
		return nil, err
	}

	return response, nil
}

func (c *Client) BindTags(ctx context.Context, instanceID string, tags []tag.Tag, option *bce.SignOption) error {
	params := map[string]string{
		"bind": "",
	}

	changeTags := map[string][]tag.Tag{
		"changeTags": tags,
	}
	putContent, err := json.Marshal(changeTags)
	if err != nil {
		return err
	}

	path := fmt.Sprintf("/v1/instance/%s/tag", instanceID)
	req, err := bce.NewRequest("PUT", c.GetURL(path, params), bytes.NewBuffer(putContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	return err
}

func (c *Client) UnbindTags(ctx context.Context, instanceID string, tags []tag.Tag, option *bce.SignOption) error {
	params := map[string]string{
		"unbind": "",
	}

	changeTags := map[string][]tag.Tag{
		"changeTags": tags,
	}
	putContent, err := json.Marshal(changeTags)
	if err != nil {
		return err
	}

	path := fmt.Sprintf("/v1/instance/%s/tag", instanceID)
	req, err := bce.NewRequest("PUT", c.GetURL(path, params), bytes.NewBuffer(putContent))
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, option)
	return err
}

func (c *Client) GetFlavors(ctx context.Context, option *bce.SignOption) (*bbcapi.ListFlavorsResult, error) {
	req, err := bce.NewRequest("GET", c.GetURL("/v1/flavor", nil), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, option)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	flavors := new(bbcapi.ListFlavorsResult)
	err = json.Unmarshal(bodyContent, flavors)
	if err != nil {
		return nil, err
	}

	return flavors, nil
}
