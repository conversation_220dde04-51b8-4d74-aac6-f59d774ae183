/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package bbc

import (
	"context"
	"testing"
	"time"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
)

var sgclient Interface

func init() {
	//accessKeyID     := "5fded23b03594981872fbfadaad70ef6"
	//secretAccessKey := "6b109261727a4fee8fc5db4bf5ec6c51"

	accessKeyID := "e0c68be8495540f280b3e9ef03ec25d2"
	secretAccessKey := "b599d5f4f30e41bcb0e1482c9de65bd6"

	sgclient = NewClient(&bce.Config{
		Credentials: bce.NewCredentials(accessKeyID, secretAccessKey),
		Checksum:    true,
		Timeout:     30 * time.Second,
		Endpoint:    Endpoints["bj"],
	})
	sgclient.SetDebug(true)
}

func testBindSecurityGroup(t *testing.T) {
	instanceID := []string{
		"i-BA00YPnT",
	}
	sgID := []string{
		"g-sy41188hccug",
		"g-qrzxv5kczzmu",
	}
	err := sgclient.BindSecurityGroup(context.Background(), instanceID, sgID, NewSignOption())
	if err != nil {
		t.Errorf("BindSecurityGroup failed: %v", err)
		return
	}
	t.Logf("BindSecurityGroup succeeded")
}

func testUnbindSecurityGroup(t *testing.T) {
	instanceID := "i-BA00YPnT"
	sgID := "g-qrzxv5kczzmu"
	err := sgclient.UnbindSecurityGroup(context.Background(), instanceID, sgID, NewSignOption())
	if err != nil {
		t.Errorf("BindSecurityGroup failed: %v", err)
		return
	}
	t.Logf("BindSecurityGroup succeeded")
}
