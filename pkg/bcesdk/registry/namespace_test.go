package registry

import (
	"context"
	"testing"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
)

func TestClient_DescribeNamespace(t *testing.T) {
	c := registryClient
	type args struct {
		ctx  context.Context
		name string
		opt  *bce.SignOption
	}
	tests := []struct {
		name    string
		args    args
		want    *Namespace
		wantErr bool
	}{
		// All test cases.
		{
			name: "normal case",
			args: args{
				ctx:  context.TODO(),
				name: "test-ns",
			},
			want:    &Namespace{Namespace: "test-ns"},
			wantErr: false,
		},
		{
			name: "not exist case",
			args: args{
				ctx:  context.TODO(),
				name: "test-ns-not-exist",
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := c.DescribeNamespace(tt.args.ctx, tt.args.name, tt.args.opt)
			if (err != nil) != tt.wantErr {
				t.<PERSON>rf("Client.DescribeNamespace() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil && got.Namespace != tt.want.Namespace {
				t.Errorf("Client.DescribeNamespace() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestClient_DeleteNamespace(t *testing.T) {
	c := registryClient
	type args struct {
		ctx       context.Context
		namespace string
		opt       *bce.SignOption
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// Add test cases.
		{
			name: "normal case",
			args: args{
				ctx:       context.TODO(),
				namespace: "test-namespace",
			},
			wantErr: false,
		},
		{
			name: "not exist namespace case",
			args: args{
				ctx:       context.TODO(),
				namespace: "test-namespace-not-exist",
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := c.DeleteNamespace(tt.args.ctx, tt.args.namespace, tt.args.opt); (err != nil) != tt.wantErr {
				t.Errorf("Client.DeleteNamespace() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
