package registry

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/http/httptest"
	"strings"

	"github.com/gorilla/mux"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
)

var (
	testHTTPServer *httptest.Server
	registryClient *Client
)

func init() {
	credentials := &bce.Credentials{
		AccessKeyID:     "b275875821484bc7a3ee0bdae74c3ef1",
		SecretAccessKey: "9b12b3bf48ee421a949511c4c77b6db9",
	}

	//var bceConfig = bce.NewConfig(credentials)
	var bceConfig = &bce.Config{
		Credentials: credentials,
		Checksum:    true,
	}
	var registryConfig = NewConfig(bceConfig)
	registryClient = NewClient(registryConfig)

	r := mux.NewRouter()
	r.<PERSON>le<PERSON>unc("/v1/namespaces", handleCreateNamespace).Methods("POST")
	r.HandleFunc("/v1/namespaces/exist", handleDescribeNamespace).Methods("GET")
	r.Handle<PERSON>unc("/v1/namespaces", handleDeleteNamespace).Methods("DELETE")

	testHTTPServer = httptest.NewServer(r)
	registryClient.Endpoint = testHTTPServer.URL
}

func handleCreateNamespace(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	body, err := ioutil.ReadAll(r.Body)
	if err != nil {
		w.WriteHeader(http.StatusBadRequest)
		return
	}
	args := new(CreateNamespaceArgs)
	err = json.Unmarshal(body, args)
	if err != nil {
		w.WriteHeader(http.StatusBadRequest)
		return
	}
	if args.validate() != nil {
		w.WriteHeader(http.StatusBadRequest)
		return
	}
	if strings.Contains(args.Namespace, "duplicate") {
		w.WriteHeader(http.StatusBadRequest)
		w.Write([]byte(fmt.Sprintf(`{"code":"RegistryAlreadyExist","message":"Namespace: %s Already exist.","requestId":null}`, args.Namespace)))
		return
	}
	w.WriteHeader(http.StatusCreated)
	w.Write([]byte(`{"message":"Namespace created successfully."}`))
}

func handleDescribeNamespace(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	query := r.URL.Query()
	if names, ok := query["name"]; ok && len(names) == 1 {
		name := names[0]
		if strings.Contains(name, "not") {
			w.WriteHeader(http.StatusNotFound)
			w.Write([]byte(fmt.Sprintf(`{"code":"RegistryNamespaceNotExist","message":"Namespace %s is not exists.","requestId":null}`, name)))
			return
		}
		w.Write([]byte(fmt.Sprintf(`{"namespace":"%s","user":"test-user","public":true}`, name)))
		return
	}
	w.WriteHeader(http.StatusBadRequest)
}

func handleDeleteNamespace(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	query := r.URL.Query()
	if namespaces, ok := query["namespace"]; ok && len(namespaces) == 1 {
		namespace := namespaces[0]
		if strings.Contains(namespace, "not") {
			w.WriteHeader(http.StatusNotFound)
			w.Write([]byte(fmt.Sprintf(`{"code":"RegistryNamespaceNotExist","message":"Cannot found Namespace: %s","requestId":null}`, namespace)))
			return
		}
		w.Write([]byte(fmt.Sprintf(`{"message":"Namespace deleted successfully."`)))
		return
	}
	w.WriteHeader(http.StatusBadRequest)
}
