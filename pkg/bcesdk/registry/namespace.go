package registry

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
)

type Namespace struct {
	Namespace string `json:"namespace"`
	Public    bool   `json:"public"`
	User      string `json:"user"`
}

// DescribeNamespace describe detail of a namespace
func (c *Client) DescribeNamespace(ctx context.Context, name string, opt *bce.SignOption) (
	*Namespace, error) {
	if len(name) == 0 {
		return nil, fmt.Errorf("name cannot be empty")
	}
	params := map[string]string{
		"name": name,
	}
	req, err := bce.NewRequest("GET", c.GetURL("/v1/namespaces/exist", params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, opt)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	namespace := new(Namespace)
	err = json.Unmarshal(bodyContent, namespace)
	if err != nil {
		return nil, err
	}

	return namespace, nil
}

type CreateNamespaceArgs struct {
	User            string `json:"user"`
	Namespace       string `json:"namespace"`
	Public          bool   `json:"public"`
	NotificationUrl string `json:"notification_url,omitempty"`
}

func (v *CreateNamespaceArgs) validate() error {
	if len(v.User) == 0 || len(v.Namespace) == 0 {
		return fmt.Errorf("user and namespace cannnot be empty")
	}
	return nil
}

// CreateNamespace creates a new namespace
func (c *Client) CreateNamespace(
	ctx context.Context, args *CreateNamespaceArgs, opt *bce.SignOption) error {
	err := args.validate()
	if err != nil {
		return err
	}
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}
	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	req, err := bce.NewRequest(
		"POST", c.GetURL("/v1/namespaces", params), bytes.NewBuffer(postContent))
	if err != nil {
		return err
	}
	_, err = c.SendRequest(ctx, req, opt)
	if err != nil {
		return err
	}

	return nil
}

// DeleteNamespace delete a namespace by name
func (c *Client) DeleteNamespace(ctx context.Context, namespace string, opt *bce.SignOption) error {
	if len(namespace) == 0 {
		return fmt.Errorf("namespace cannot be empty")
	}
	params := map[string]string{
		"namespace":   namespace,
		"clientToken": c.GenerateClientToken(),
	}

	req, err := bce.NewRequest("DELETE", c.GetURL("/v1/namespaces", params), nil)
	if err != nil {
		return nil
	}
	_, err = c.SendRequest(ctx, req, opt)
	if err != nil {
		return err
	}
	return nil
}

type BatchDeleteNamespaceArgs struct {
	Namespaces []string `json:"namespaces"`
}

func (v *BatchDeleteNamespaceArgs) validate() error {
	if len(v.Namespaces) == 0 {
		return fmt.Errorf("namespaces to delete cannot be empty")
	}
	return nil
}

// BatchDeleteNamespace delete namespace in batch by name
func (c *Client) BatchDeleteNamespace(ctx context.Context, args *BatchDeleteNamespaceArgs, opt *bce.SignOption) error {
	if err := args.validate(); err != nil {
		return err
	}
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}

	postContent, err := json.Marshal(args)
	if err != nil {
		return err
	}

	req, err := bce.NewRequest("POST", c.GetURL("/v1/namespaces/batch/delete", params), bytes.NewBuffer(postContent))
	if err != nil {
		return nil
	}
	_, err = c.SendRequest(ctx, req, opt)
	if err != nil {
		return err
	}
	return nil
}
