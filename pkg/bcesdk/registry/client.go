package registry

import (
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
)

// Endpoint contains all endpoints of Baidu Cloud BCC.
var Endpoint = map[string]string{
	"bj":   "hub-auth.baidubce.com",
	"gz":   "hub-auth.baidubce.com",
	"su":   "hub-auth.baidubce.com",
	"hkg":  "hub-auth.baidubce.com",
	"bd":   "hub-auth.baidubce.com",
	"qa00": "cp01-sys-rpm-rdqa206.cp01.baidu.com:8555",
	"qa01": "yq01-caas-registry-qa01.epc.baidu.com:8555",
}

// Config contains all options for bos.Client.
type Config struct {
	*bce.Config
}

func NewConfig(config *bce.Config) *Config {
	return &Config{config}
}

// Client is the bos client implemention for Baidu Cloud BOS API.
type Client struct {
	*bce.Client
}

func NewClient(config *Config) *Client {
	bceClient := bce.NewClient(config.Config)
	return &Client{bceClient}
}

// GetURL generates the full URL of http request for Baidu Cloud BOS API.
func (c *Client) GetURL(objectKey string, params map[string]string) string {
	host := c.Endpoint

	if host == "" {
		host = Endpoint[c.GetRegion()]
	}

	uriPath := objectKey

	return c.Client.GetURL(host, uriPath, params)
}
