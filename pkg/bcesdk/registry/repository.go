package registry

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/bcesdk/bce"
)

// DeleteImage delete a single image (a tag of a repository)
func (c *Client) DeleteImage(
	ctx context.Context, namespace, repository, tag string, opt *bce.SignOption) error {
	if len(namespace) == 0 || len(repository) == 0 || len(tag) == 0 {
		return fmt.Errorf("namespace, name and tag cannot be empty")
	}
	params := map[string]string{
		"namespace":  namespace,
		"repository": repository,
		"tag":        tag,
	}
	req, err := bce.NewRequest("DELETE", c.GetURL("/v1/images", params), nil)
	if err != nil {
		return err
	}

	_, err = c.SendRequest(ctx, req, opt)
	if err != nil {
		return err
	}

	return nil
}

type Repository struct {
	ID        int64  `description:"Repository id" json:"id"`
	Name      string `description:"Repository名称" json:"name"`
	Namespace string `description:"Repository命名空间名称" json:"namespace"`
	Public    bool   `description:"是否公有" json:"public"`
	CreatedAt string `description:"创建时间" json:"created_at"`
	UpdatedAt string `description:"tag最后更新时间" json:"updated_at"`
}

type ListRepositoriesResponse struct {
	Repositories []*Repository `json:"repositories"`
}

// ListRepositories list all repositories of current user
func (c *Client) ListRepositories(ctx context.Context, opt *bce.SignOption) ([]*Repository, error) {
	req, err := bce.NewRequest("GET", c.GetURL("/v1/images/repositories", nil), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, opt)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	repositoryList := new(ListRepositoriesResponse)
	err = json.Unmarshal(bodyContent, repositoryList)
	if err != nil {
		return nil, err
	}

	return repositoryList.Repositories, nil
}

// DescribeRepository describe a repository detail
func (c *Client) DescribeRepository(
	ctx context.Context, namespace, name string, opt *bce.SignOption) (
	*Repository, error) {
	if len(namespace) == 0 || len(name) == 0 {
		return nil, fmt.Errorf("namespace and name cannot be empty")
	}
	params := map[string]string{
		"namespace":  namespace,
		"repository": name,
	}
	req, err := bce.NewRequest("GET", c.GetURL("/v1/images/repository", params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, opt)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	repository := new(Repository)
	err = json.Unmarshal(bodyContent, repository)
	if err != nil {
		return nil, err
	}

	return repository, nil
}

type CreateRepositoryArgs struct {
	Namespace  string `json:"namespace"`
	Repository string `json:"repository"`
}

func (args *CreateRepositoryArgs) validate() error {
	if len(args.Namespace) == 0 || len(args.Repository) == 0 {
		return fmt.Errorf("namespace and repository cannot be empty")
	}
	return nil
}

type CreateRepositoryResponse struct {
	Repository *Repository
}

// CreateRepository creates an empty repository (create metadata) and
// returns repository id on success
func (c *Client) CreateRepository(
	ctx context.Context, args *CreateRepositoryArgs, opt *bce.SignOption) (
	int64, error) {
	err := args.validate()
	if err != nil {
		return 0, err
	}
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}
	postContent, err := json.Marshal(args)
	if err != nil {
		return 0, err
	}

	req, err := bce.NewRequest(
		"POST", c.GetURL("/v1/images/repositories", params), bytes.NewBuffer(postContent))
	if err != nil {
		return 0, err
	}
	resp, err := c.SendRequest(ctx, req, opt)
	if err != nil {
		return 0, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return 0, err
	}

	cr := new(CreateRepositoryResponse)
	err = json.Unmarshal(bodyContent, cr)
	if err != nil {
		return 0, err
	}

	// insanity check
	if cr.Repository.Name != args.Repository || cr.Repository.Namespace != args.Namespace {
		return 0, fmt.Errorf("expect namespace %s name %s but get namespace %s name %s",
			args.Namespace, args.Repository, cr.Repository.Namespace, cr.Repository.Name)
	}

	return cr.Repository.ID, nil
}

type DeleteRepositoryResponse struct {
	Result struct {
		Status    string `json:"status"`
		Namespace string `json:"namespace"`
		Name      string `json:"name"`
	} `json:"result"`
}

// DeleteRepository delete an empty repository (repository with no tag)
func (c *Client) DeleteRepository(
	ctx context.Context, namespace, name string, opt *bce.SignOption) error {
	if len(namespace) == 0 || len(name) == 0 {
		return fmt.Errorf("namespace and name cannot be empty")
	}
	params := map[string]string{
		"namespace":  namespace,
		"repository": name,
	}
	req, err := bce.NewRequest("DELETE", c.GetURL("/v1/images/repositories", params), nil)
	if err != nil {
		return err
	}

	resp, err := c.SendRequest(ctx, req, opt)
	if err != nil {
		return err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return err
	}

	deleteResp := new(DeleteRepositoryResponse)
	err = json.Unmarshal(bodyContent, deleteResp)
	if err != nil {
		return err
	}

	// insanity check
	if deleteResp.Result.Name != name || deleteResp.Result.Namespace != namespace {
		return fmt.Errorf("expect namespace %s name %s but get namespace %s name %s",
			namespace, name, deleteResp.Result.Namespace, deleteResp.Result.Name)
	}

	return nil
}

type ListTagsOfRepositoryResponse struct {
	Namespace  string `json:"namespace"`
	Repository string `json:"repository"`
	Tags       []*Tag `json:"tags"`
}

type Tag struct {
	Name        string `json:"name"`
	Digest      string `json:"digest"`
	Description string `json:"description"`
	CreatedAt   string `json:"created_at"`
	UpdatedAt   string `json:"updated_at"`
}

// ListTagsOfRepository list all tags of a single repository
func (c *Client) ListTagsOfRepository(
	ctx context.Context, namespace, name string, opt *bce.SignOption) ([]*Tag, error) {
	if len(namespace) == 0 || len(name) == 0 {
		return nil, fmt.Errorf("namespace and name cannot be empty")
	}
	params := map[string]string{
		"namespace":  namespace,
		"repository": name,
	}

	req, err := bce.NewRequest("GET", c.GetURL("/v1/images/tags", params), nil)
	if err != nil {
		return nil, err
	}

	resp, err := c.SendRequest(ctx, req, opt)
	if err != nil {
		return nil, err
	}

	bodyContent, err := resp.GetBodyContent()
	if err != nil {
		return nil, err
	}

	tagList := new(ListTagsOfRepositoryResponse)
	err = json.Unmarshal(bodyContent, tagList)
	if err != nil {
		return nil, err
	}

	// insanity check
	if tagList.Repository != name || tagList.Namespace != namespace {
		return nil, fmt.Errorf("expect namespace %s name %s but get namespace %s name %s",
			namespace, name, tagList.Namespace, tagList.Repository)
	}

	return tagList.Tags, nil
}
