# 功能6.1-CNI双栈申请-端到端测试清单

## 功能概述
CNI插件双栈申请功能的端到端测试，验证在真实Kubernetes环境中双栈Pod的网络配置和连通性。

## 测试范围
本测试清单覆盖CNI插件双栈申请功能在完整环境中的集成测试。

## 端到端测试清单

### 1. 环境准备测试

#### 1.1 双栈节点准备验证
**测试目标**: 验证节点支持双栈网络
- **前置条件**:
  - 节点支持IPv6
  - BCINode CRD包含IPv6配置
  - ENI支持IPv6地址分配
- **验证步骤**:
  1. 检查节点IPv6配置
  2. 验证BCINode状态包含IPv6地址池
  3. 确认ENI IPv6地址可用
- **预期结果**:
  - 节点IPv6功能正常
  - BCINode包含IPv6地址池信息
  - ENI IPv6地址分配正常

#### 1.2 CNI配置验证
**测试目标**: 验证CNI插件配置正确
- **前置条件**:
  - bci-cni插件正确安装
  - node-agent服务运行正常
  - gRPC通信正常
- **验证步骤**:
  1. 检查CNI配置文件
  2. 验证bci-cni二进制版本
  3. 测试与node-agent的gRPC连接
- **预期结果**:
  - CNI配置正确
  - 插件版本支持IPv6
  - gRPC通信正常

### 2. 单栈Pod测试（基线验证）

#### 2.1 IPv4单栈Pod测试
**测试目标**: 验证IPv4单栈Pod正常工作
- **测试步骤**:
  1. 创建IPv4单栈Pod
  2. 验证Pod获得IPv4地址
  3. 测试IPv4网络连通性
- **预期结果**:
  - Pod获得正确IPv4地址
  - IPv4网络连通性正常
  - 不分配IPv6地址

#### 2.2 IPv4多IP Pod测试
**测试目标**: 验证IPv4多IP Pod正常工作
- **测试步骤**:
  1. 创建带多EIP注解的Pod
  2. 验证Pod获得多个IPv4地址
  3. 测试所有IPv4地址连通性
- **预期结果**:
  - Pod获得多个IPv4地址
  - 所有IPv4地址网络连通性正常
  - 不分配IPv6地址

### 3. 双栈Pod申请测试

#### 3.1 基础双栈Pod测试
**测试目标**: 验证基本双栈Pod创建和配置
- **测试配置**:
```yaml
apiVersion: v1
kind: Pod
metadata:
  name: dual-stack-test-pod
  annotations:
    bci.baidu.com/bci-enable-ipv6: "true"
spec:
  containers:
  - name: test-container
    image: nginx:latest
```
- **测试步骤**:
  1. 创建双栈Pod
  2. 验证Pod状态为Running
  3. 检查Pod IP分配
  4. 验证annotation设置
- **预期结果**:
  - Pod成功创建并运行
  - Pod获得IPv4和IPv6地址
  - annotation包含`bci_internal_PodIPv6`

#### 3.2 双栈地址分配验证测试
**测试目标**: 验证双栈地址正确分配
- **测试步骤**:
  1. 创建双栈Pod
  2. 检查Pod annotations
  3. 进入Pod网络命名空间检查网络配置
  4. 验证地址配置正确性
- **验证要点**:
  - `bci_internal_PodIP`包含IPv4地址
  - `bci_internal_PodIPv6`包含IPv6地址
  - Pod内网络接口配置正确
  - IPv4和IPv6地址在同一ENI上

#### 3.3 双栈多IP Pod测试
**测试目标**: 验证双栈多IP Pod的地址分配
- **测试配置**:
```yaml
apiVersion: v1
kind: Pod
metadata:
  name: dual-stack-multi-ip-pod
  annotations:
    bci.baidu.com/bci-enable-ipv6: "true"
    bci.virtual-kubelet.io/bci-multi-eip-ips: "2"
spec:
  containers:
  - name: test-container
    image: nginx:latest
```
- **测试步骤**:
  1. 创建双栈多IP Pod
  2. 验证多个IPv4地址分配
  3. 验证单个IPv6地址分配
  4. 检查网络配置
- **预期结果**:
  - Pod获得2个IPv4地址和1个IPv6地址
  - 所有地址正确配置
  - 网络连通性正常

### 4. 网络连通性测试

#### 4.1 IPv4连通性测试
**测试目标**: 验证双栈Pod的IPv4网络连通性
- **测试步骤**:
  1. 从双栈Pod ping IPv4地址
  2. 测试IPv4 HTTP连接
  3. 验证IPv4路由配置
- **测试命令**:
```bash
kubectl exec dual-stack-test-pod -- ping -4 -c 3 *******
kubectl exec dual-stack-test-pod -- curl -4 http://www.baidu.com
```
- **预期结果**:
  - IPv4 ping成功
  - IPv4 HTTP连接正常
  - IPv4路由配置正确

#### 4.2 IPv6连通性测试
**测试目标**: 验证双栈Pod的IPv6网络连通性
- **测试步骤**:
  1. 从双栈Pod ping IPv6地址
  2. 测试IPv6 HTTP连接
  3. 验证IPv6路由配置
- **测试命令**:
```bash
kubectl exec dual-stack-test-pod -- ping -6 -c 3 2001:4860:4860::8888
kubectl exec dual-stack-test-pod -- curl -6 http://ipv6.google.com
```
- **预期结果**:
  - IPv6 ping成功
  - IPv6 HTTP连接正常
  - IPv6路由配置正确

#### 4.3 Pod间双栈通信测试
**测试目标**: 验证双栈Pod之间的通信
- **测试步骤**:
  1. 创建两个双栈Pod
  2. 测试IPv4 Pod间通信
  3. 测试IPv6 Pod间通信
  4. 测试双向连通性
- **预期结果**:
  - IPv4 Pod间通信正常
  - IPv6 Pod间通信正常
  - 双向连通性良好

### 5. 网络接口配置验证

#### 5.1 Pod内网络接口验证
**测试目标**: 验证Pod内网络接口正确配置
- **测试步骤**:
  1. 检查网络接口列表
  2. 验证IP地址配置
  3. 检查路由表配置
  4. 验证网关配置
- **验证命令**:
```bash
kubectl exec dual-stack-test-pod -- ip addr show
kubectl exec dual-stack-test-pod -- ip -6 addr show
kubectl exec dual-stack-test-pod -- ip route show
kubectl exec dual-stack-test-pod -- ip -6 route show
```
- **预期结果**:
  - 网络接口包含IPv4和IPv6地址
  - 路由表配置正确
  - 网关配置正确

#### 5.2 Host网络配置验证
**测试目标**: 验证Host侧网络配置
- **测试步骤**:
  1. 检查veth pair配置
  2. 验证ENI命名空间配置
  3. 检查路由规则配置
  4. 验证iptables规则
- **预期结果**:
  - veth pair正确创建
  - ENI命名空间配置正确
  - IPv4和IPv6路由规则都正确

### 6. 错误场景测试

#### 6.1 IPv6分配失败测试
**测试目标**: 验证IPv6分配失败时的处理
- **测试场景**:
  - IPv6地址池耗尽
  - ENI不支持IPv6
  - IPv6网络配置错误
- **测试步骤**:
  1. 模拟IPv6分配失败场景
  2. 创建双栈Pod
  3. 验证Pod创建失败
  4. 检查错误信息
- **预期结果**:
  - Pod创建失败
  - 错误信息明确
  - 不会部分分配成功

#### 6.2 网络配置失败测试
**测试目标**: 验证网络配置失败时的处理
- **测试场景**:
  - IPv6路由配置失败
  - 网络接口配置失败
  - 网络命名空间异常
- **测试步骤**:
  1. 模拟网络配置失败
  2. 创建双栈Pod
  3. 验证错误处理
  4. 检查资源清理
- **预期结果**:
  - 错误正确处理
  - 资源正确清理
  - 不会留下残留配置

### 7. 兼容性测试

#### 7.1 节点类型兼容性测试
**测试目标**: 验证不同节点类型的兼容性
- **测试场景**:
  - IPv6支持节点 vs 仅IPv4节点
  - 双栈Pod调度验证
  - 单栈Pod在双栈节点运行
- **测试步骤**:
  1. 在不同类型节点创建Pod
  2. 验证调度结果
  3. 检查网络配置
- **预期结果**:
  - 双栈Pod只调度到支持IPv6节点
  - 单栈Pod可调度到任意节点
  - 网络配置正确

#### 7.2 现有工作负载兼容性测试
**测试目标**: 验证与现有工作负载的兼容性
- **测试步骤**:
  1. 运行现有单栈工作负载
  2. 添加双栈工作负载
  3. 验证互不影响
- **预期结果**:
  - 现有工作负载不受影响
  - 双栈功能正常工作
  - 系统稳定性保持

### 8. 性能测试

#### 8.1 地址分配性能测试
**测试目标**: 验证双栈地址分配性能
- **测试步骤**:
  1. 批量创建双栈Pod
  2. 测量分配时间
  3. 监控资源使用
- **性能指标**:
  - Pod创建时间 < 10秒
  - 内存使用增长 < 10%
  - CPU使用稳定

#### 8.2 网络性能测试
**测试目标**: 验证双栈网络性能
- **测试步骤**:
  1. 测试IPv4网络带宽
  2. 测试IPv6网络带宽
  3. 对比单栈性能
- **性能指标**:
  - IPv4性能不下降
  - IPv6性能达到预期
  - 延迟增加 < 5%

### 9. 稳定性测试

#### 9.1 长期运行测试
**测试目标**: 验证长期运行稳定性
- **测试步骤**:
  1. 创建持续运行的双栈Pod
  2. 监控24小时运行状态
  3. 检查内存泄露
- **预期结果**:
  - Pod稳定运行
  - 无内存泄露
  - 网络连通性保持

#### 9.2 大规模测试
**测试目标**: 验证大规模场景下的稳定性
- **测试步骤**:
  1. 创建100+双栈Pod
  2. 监控系统性能
  3. 测试网络连通性
- **预期结果**:
  - 系统稳定运行
  - 性能在可接受范围
  - 所有Pod网络正常

## 测试环境要求
- Kubernetes集群（支持IPv6）
- 支持IPv6的Worker节点
- BCI节点标签和配置
- ENI IPv6地址池
- 网络连通性（IPv4/IPv6）

## 测试工具
- kubectl
- ping/ping6
- curl
- netstat/ss
- tcpdump
- iperf3

## 测试数据收集
- Pod创建时间
- 网络配置时间
- 错误日志
- 性能指标
- 资源使用情况 