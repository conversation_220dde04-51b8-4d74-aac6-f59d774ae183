# 功能6.2-CNI网络接口配置-端到端测试清单

## 功能概述
CNI插件网络接口配置功能的端到端测试，验证在真实Kubernetes环境中双栈Pod的网络接口正确配置和网络连通性。

## 测试范围
本测试清单覆盖CNI插件网络接口配置功能在完整环境中的集成测试，包括容器网络接口配置、ENI网络命名空间配置和网络连通性验证。

## 端到端测试清单

### 1. 容器网络接口配置测试

#### 1.1 IPv4单栈接口配置验证
**测试目标**: 验证IPv4单栈Pod的网络接口配置
- **测试步骤**:
  1. 创建IPv4单栈Pod
  2. 检查Pod内网络接口配置
  3. 验证IPv4地址和路由配置
- **验证命令**:
```bash
kubectl exec ipv4-pod -- ip addr show eth0
kubectl exec ipv4-pod -- ip route show
kubectl exec ipv4-pod -- ip route show table main
```
- **预期结果**:
  - eth0接口配置IPv4地址
  - 默认路由指向IPv4网关
  - 网关路由配置正确
  - 接口状态为UP

#### 1.2 IPv6单栈接口配置验证
**测试目标**: 验证IPv6单栈Pod的网络接口配置
- **测试步骤**:
  1. 创建IPv6单栈Pod
  2. 检查Pod内网络接口配置
  3. 验证IPv6地址和路由配置
- **验证命令**:
```bash
kubectl exec ipv6-pod -- ip -6 addr show eth0
kubectl exec ipv6-pod -- ip -6 route show
kubectl exec ipv6-pod -- ip -6 route show table main
```
- **预期结果**:
  - eth0接口配置IPv6地址(/128)
  - 默认路由指向IPv6网关
  - 网关路由配置正确
  - 接口状态为UP

#### 1.3 双栈接口配置验证
**测试目标**: 验证双栈Pod的网络接口配置
- **测试配置**:
```yaml
apiVersion: v1
kind: Pod
metadata:
  name: dual-stack-interface-test
  annotations:
    bci.baidu.com/bci-enable-ipv6: "true"
spec:
  containers:
  - name: test-container
    image: nginx:latest
```
- **测试步骤**:
  1. 创建双栈Pod
  2. 检查Pod内网络接口配置
  3. 验证双栈地址和路由配置
- **验证命令**:
```bash
kubectl exec dual-stack-interface-test -- ip addr show
kubectl exec dual-stack-interface-test -- ip route show
kubectl exec dual-stack-interface-test -- ip -6 route show
```
- **预期结果**:
  - eth0接口同时配置IPv4和IPv6地址
  - IPv4默认路由使用IPv4网关
  - IPv6默认路由使用IPv6网关
  - 双栈网关路由都正确配置

#### 1.4 多IP双栈接口配置验证
**测试目标**: 验证多IP双栈Pod的网络接口配置
- **测试配置**:
```yaml
apiVersion: v1
kind: Pod
metadata:
  name: multi-ip-dual-stack-test
  annotations:
    bci.baidu.com/bci-enable-ipv6: "true"
    bci.virtual-kubelet.io/bci-multi-eip-ips: "2"
spec:
  containers:
  - name: test-container
    image: nginx:latest
```
- **测试步骤**:
  1. 创建多IP双栈Pod
  2. 检查Pod内所有网络接口
  3. 验证多IP和双栈配置
- **验证命令**:
```bash
kubectl exec multi-ip-dual-stack-test -- ip addr show
kubectl exec multi-ip-dual-stack-test -- ip route show table all
kubectl exec multi-ip-dual-stack-test -- ip -6 route show table all
```
- **预期结果**:
  - eth0接口配置主IPv4地址和IPv6地址
  - eth1接口配置次IPv4地址
  - 多个路由表正确配置
  - 路由规则正确设置

### 2. ENI网络命名空间配置测试

#### 2.1 IPv4 ENI命名空间配置验证
**测试目标**: 验证IPv4地址在ENI命名空间中的配置
- **测试步骤**:
  1. 创建IPv4 Pod并获取ENI命名空间
  2. 进入ENI命名空间检查配置
  3. 验证网关地址和路由配置
- **验证命令**:
```bash
# 获取ENI命名空间
ENI_NS=$(kubectl get pod ipv4-pod -o jsonpath='{.metadata.annotations.bci_internal_PodEniId}')
# 进入ENI命名空间检查
sudo ip netns exec enins-${ENI_NS} ip addr show
sudo ip netns exec enins-${ENI_NS} ip route show
```
- **预期结果**:
  - veth接口移动到ENI命名空间
  - 网关地址配置到veth接口
  - 主机路由正确配置
  - IP转发功能启用

#### 2.2 IPv6 ENI命名空间配置验证
**测试目标**: 验证IPv6地址在ENI命名空间中的配置
- **测试步骤**:
  1. 创建IPv6 Pod并获取ENI命名空间
  2. 进入ENI命名空间检查配置
  3. 验证IPv6网关地址和路由配置
- **验证命令**:
```bash
# 获取ENI命名空间
ENI_NS=$(kubectl get pod ipv6-pod -o jsonpath='{.metadata.annotations.bci_internal_PodEniId}')
# 进入ENI命名空间检查
sudo ip netns exec enins-${ENI_NS} ip -6 addr show
sudo ip netns exec enins-${ENI_NS} ip -6 route show
```
- **预期结果**:
  - veth接口移动到ENI命名空间
  - IPv6网关地址配置到veth接口(/128)
  - IPv6主机路由正确配置
  - IPv6转发功能启用

#### 2.3 双栈ENI命名空间配置验证
**测试目标**: 验证双栈地址在ENI命名空间中的配置
- **测试步骤**:
  1. 创建双栈Pod并获取ENI命名空间
  2. 进入ENI命名空间检查双栈配置
  3. 验证双栈网关和路由配置
- **验证命令**:
```bash
# 获取ENI命名空间
ENI_NS=$(kubectl get pod dual-stack-interface-test -o jsonpath='{.metadata.annotations.bci_internal_PodEniId}')
# 进入ENI命名空间检查
sudo ip netns exec enins-${ENI_NS} ip addr show
sudo ip netns exec enins-${ENI_NS} ip route show
sudo ip netns exec enins-${ENI_NS} ip -6 route show
```
- **预期结果**:
  - veth接口移动到ENI命名空间
  - IPv4和IPv6网关地址都正确配置
  - 双栈主机路由都正确配置
  - IPv4和IPv6转发功能都启用

### 3. 网络连通性测试

#### 3.1 Pod内网络连通性测试
**测试目标**: 验证Pod内部网络连通性
- **测试步骤**:
  1. 测试Pod内到网关的连通性
  2. 测试Pod内到外部网络的连通性
  3. 验证双栈连通性
- **测试命令**:
```bash
# IPv4连通性测试
kubectl exec dual-stack-interface-test -- ping -c 3 $(kubectl get pod dual-stack-interface-test -o jsonpath='{.metadata.annotations.bci_internal_PodGateway}')
kubectl exec dual-stack-interface-test -- ping -c 3 *******

# IPv6连通性测试
kubectl exec dual-stack-interface-test -- ping6 -c 3 2001:4860:4860::8888
```
- **预期结果**:
  - 到网关的ping成功
  - 到外部网络的ping成功
  - IPv6连通性正常

#### 3.2 Host到Pod网络连通性测试
**测试目标**: 验证Host到Pod的网络连通性
- **测试步骤**:
  1. 从Host ping Pod IP
  2. 从Host访问Pod服务
  3. 验证双栈访问
- **测试命令**:
```bash
# 获取Pod IP
POD_IPV4=$(kubectl get pod dual-stack-interface-test -o jsonpath='{.status.podIP}')
POD_IPV6=$(kubectl get pod dual-stack-interface-test -o jsonpath='{.metadata.annotations.bci_internal_PodIPv6}')

# 从Host测试连通性
ping -c 3 $POD_IPV4
ping6 -c 3 $POD_IPV6
```
- **预期结果**:
  - Host到Pod IPv4连通性正常
  - Host到Pod IPv6连通性正常
  - 双栈访问都正常

#### 3.3 Pod间网络连通性测试
**测试目标**: 验证Pod间网络连通性
- **测试步骤**:
  1. 创建两个双栈Pod
  2. 测试Pod间IPv4连通性
  3. 测试Pod间IPv6连通性
- **测试命令**:
```bash
# 获取目标Pod IP
TARGET_IPV4=$(kubectl get pod dual-stack-interface-test -o jsonpath='{.status.podIP}')
TARGET_IPV6=$(kubectl get pod dual-stack-interface-test -o jsonpath='{.metadata.annotations.bci_internal_PodIPv6}')

# 从另一个Pod测试连通性
kubectl exec dual-stack-test-2 -- ping -c 3 $TARGET_IPV4
kubectl exec dual-stack-test-2 -- ping6 -c 3 $TARGET_IPV6
```
- **预期结果**:
  - Pod间IPv4连通性正常
  - Pod间IPv6连通性正常

### 4. 路由表配置测试

#### 4.1 主路由表配置验证
**测试目标**: 验证主路由表的配置
- **测试步骤**:
  1. 检查Pod内主路由表
  2. 验证默认路由配置
  3. 验证网关路由配置
- **验证命令**:
```bash
kubectl exec dual-stack-interface-test -- ip route show table main
kubectl exec dual-stack-interface-test -- ip -6 route show table main
```
- **预期结果**:
  - 主路由表包含默认路由
  - 网关路由配置正确
  - 双栈路由都正确配置

#### 4.2 自定义路由表配置验证
**测试目标**: 验证多IP场景的自定义路由表配置
- **测试步骤**:
  1. 检查Pod内所有路由表
  2. 验证自定义路由表配置
  3. 验证路由规则配置
- **验证命令**:
```bash
kubectl exec multi-ip-dual-stack-test -- ip route show table all
kubectl exec multi-ip-dual-stack-test -- ip rule show
kubectl exec multi-ip-dual-stack-test -- ip -6 rule show
```
- **预期结果**:
  - 自定义路由表正确创建
  - 路由规则正确配置
  - 每个IP对应正确的路由表

#### 4.3 路由规则优先级测试
**测试目标**: 验证路由规则的优先级配置
- **测试步骤**:
  1. 检查路由规则优先级
  2. 验证路由选择逻辑
  3. 测试流量转发路径
- **验证命令**:
```bash
kubectl exec multi-ip-dual-stack-test -- ip rule show
kubectl exec multi-ip-dual-stack-test -- ip route get *******
kubectl exec multi-ip-dual-stack-test -- ip -6 route get 2001:4860:4860::8888
```
- **预期结果**:
  - 路由规则优先级正确
  - 路由选择逻辑正确
  - 流量转发路径正确

### 5. 网络接口属性测试

#### 5.1 MTU配置验证
**测试目标**: 验证网络接口MTU配置
- **测试步骤**:
  1. 检查Pod内接口MTU
  2. 验证MTU一致性
  3. 测试大包传输
- **验证命令**:
```bash
kubectl exec dual-stack-interface-test -- ip link show eth0
kubectl exec dual-stack-interface-test -- ping -c 3 -s 1400 *******
```
- **预期结果**:
  - MTU配置正确
  - 双栈接口MTU一致
  - 大包传输正常

#### 5.2 MAC地址配置验证
**测试目标**: 验证网络接口MAC地址配置
- **测试步骤**:
  1. 检查Pod内接口MAC地址
  2. 验证MAC地址唯一性
  3. 验证ARP表项
- **验证命令**:
```bash
kubectl exec dual-stack-interface-test -- ip link show eth0
kubectl exec dual-stack-interface-test -- arp -a
```
- **预期结果**:
  - MAC地址配置正确
  - MAC地址唯一性保证
  - ARP表项正确

#### 5.3 接口状态监控
**测试目标**: 验证网络接口状态监控
- **测试步骤**:
  1. 检查接口状态
  2. 监控流量统计
  3. 验证接口健康状态
- **验证命令**:
```bash
kubectl exec dual-stack-interface-test -- ip link show
kubectl exec dual-stack-interface-test -- cat /sys/class/net/eth0/statistics/rx_bytes
kubectl exec dual-stack-interface-test -- cat /sys/class/net/eth0/statistics/tx_bytes
```
- **预期结果**:
  - 接口状态正常
  - 流量统计准确
  - 健康状态良好

### 6. 错误场景测试

#### 6.1 网络接口配置失败测试
**测试目标**: 验证网络接口配置失败时的处理
- **测试场景**:
  - 网络命名空间异常
  - 接口配置冲突
  - 资源不足
- **测试步骤**:
  1. 模拟配置失败场景
  2. 验证错误处理机制
  3. 检查资源清理
- **预期结果**:
  - 错误正确处理
  - 资源正确清理
  - 不影响其他Pod

#### 6.2 路由配置失败测试
**测试目标**: 验证路由配置失败时的处理
- **测试场景**:
  - 路由表创建失败
  - 路由规则配置失败
  - 网关不可达
- **测试步骤**:
  1. 模拟路由配置失败
  2. 验证错误恢复机制
  3. 检查网络状态
- **预期结果**:
  - 路由错误正确处理
  - 错误恢复机制生效
  - 网络状态一致

#### 6.3 ENI命名空间配置失败测试
**测试目标**: 验证ENI命名空间配置失败时的处理
- **测试场景**:
  - 命名空间创建失败
  - veth移动失败
  - 权限不足
- **测试步骤**:
  1. 模拟ENI配置失败
  2. 验证错误处理
  3. 检查资源状态
- **预期结果**:
  - 错误正确处理
  - 资源状态一致
  - 系统稳定运行

### 7. 性能测试

#### 7.1 网络接口配置性能测试
**测试目标**: 验证网络接口配置性能
- **测试步骤**:
  1. 批量创建Pod
  2. 测量配置时间
  3. 监控资源使用
- **性能指标**:
  - 接口配置时间 < 5秒
  - 内存使用合理
  - CPU使用稳定

#### 7.2 网络吞吐量测试
**测试目标**: 验证网络吞吐量性能
- **测试步骤**:
  1. 使用iperf3测试吞吐量
  2. 分别测试IPv4和IPv6
  3. 对比单栈和双栈性能
- **测试命令**:
```bash
# IPv4吞吐量测试
kubectl exec dual-stack-interface-test -- iperf3 -c target-server -t 30

# IPv6吞吐量测试
kubectl exec dual-stack-interface-test -- iperf3 -c target-server -6 -t 30
```
- **性能指标**:
  - IPv4吞吐量 >= 1Gbps
  - IPv6吞吐量 >= 1Gbps
  - 双栈性能无明显下降

#### 7.3 网络延迟测试
**测试目标**: 验证网络延迟性能
- **测试步骤**:
  1. 测试Pod间延迟
  2. 测试到外部网络延迟
  3. 对比IPv4和IPv6延迟
- **测试命令**:
```bash
# 延迟测试
kubectl exec dual-stack-interface-test -- ping -c 100 target-ip
kubectl exec dual-stack-interface-test -- ping6 -c 100 target-ipv6
```
- **性能指标**:
  - Pod间延迟 < 1ms
  - 到外部网络延迟 < 50ms
  - IPv6延迟增加 < 5%

### 8. 稳定性测试

#### 8.1 长期运行稳定性测试
**测试目标**: 验证长期运行稳定性
- **测试步骤**:
  1. 创建持续运行的双栈Pod
  2. 持续监控网络状态
  3. 检查接口配置稳定性
- **预期结果**:
  - 长期运行稳定
  - 网络配置不变
  - 无内存泄露

#### 8.2 大规模部署稳定性测试
**测试目标**: 验证大规模部署稳定性
- **测试步骤**:
  1. 创建大量双栈Pod
  2. 监控系统资源使用
  3. 验证网络配置正确性
- **预期结果**:
  - 大规模部署稳定
  - 系统资源使用合理
  - 网络配置正确性保持

## 测试环境要求
- Kubernetes集群（支持IPv6）
- 支持双栈的Worker节点
- ENI网络配置
- 网络连通性测试环境
- 性能测试工具

## 测试工具
- kubectl
- ping/ping6
- iperf3
- tcpdump
- netstat/ss
- ip命令

## 测试数据收集
- 接口配置时间
- 路由配置时间
- 网络性能指标
- 错误日志
- 系统资源使用情况 