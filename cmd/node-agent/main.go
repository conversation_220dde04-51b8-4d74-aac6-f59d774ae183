/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package main

import (
	// +kubebuilder:scaffold:imports
	"fmt"
	"os"
	"time"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/fields"
	"k8s.io/apimachinery/pkg/runtime"
	utilruntime "k8s.io/apimachinery/pkg/util/runtime"
	clientgoscheme "k8s.io/client-go/kubernetes/scheme"
	"k8s.io/klog/v2/klogr"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/cache"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/healthz"

	networkingv1 "icode.baidu.com/baidu/bci2/bci-cni-driver/apis/networking/v1"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/logger"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/nodeagent/controllers"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/nodeagent/ipam"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/nodeagent/options"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/nodeagent/server"
	utilenv "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/nodeagent/utils/env"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/nodeagent/utils/trigger"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/queue"
	netlinkwrapper "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/netlink"
)

var (
	scheme   = runtime.NewScheme()
	setupLog = ctrl.Log.WithName("setup")
)

func init() {
	utilruntime.Must(clientgoscheme.AddToScheme(scheme))

	utilruntime.Must(networkingv1.AddToScheme(scheme))
	// +kubebuilder:scaffold:scheme
}

func main() {
	var (
		opts = options.ParseArgs()
		ctx  = logger.NewContext()
	)

	ctrl.SetLogger(klogr.New())

	nodeName, err := utilenv.GetNodeName(ctx)
	if err != nil {
		setupLog.Error(err, "unable to get node name")
		os.Exit(1)
	}
	logger.Infof(ctx, "node name: %v", nodeName)
	var (
		nodeNameFieldSelector = fmt.Sprintf("metadata.name=%s", nodeName)
	)

	unCachedClient, err := client.New(ctrl.GetConfigOrDie(), client.Options{Scheme: scheme})
	if err != nil {
		setupLog.Error(err, "unable to new no-cache client")
		os.Exit(1)
	}

	// 1. setup bcinode crd controller
	// Compared to common controllers, cni node agent performs as a per-node daemon.
	// Thus, leader election is disabled and only watch local node CRD.
	mgr, err := ctrl.NewManager(ctrl.GetConfigOrDie(), ctrl.Options{
		Scheme:                 scheme,
		MetricsBindAddress:     opts.MetricsAddr,
		Port:                   9443,
		HealthProbeBindAddress: opts.ProbeAddr,
		LeaderElection:         false,
		NewCache: cache.New,
		Cache: cache.Options{
			ByObject: map[client.Object]cache.ByObject{
				&networkingv1.BciNode{}: {
					Field: fields.ParseSelectorOrDie(nodeNameFieldSelector),
				},
			},
		},
	})
	if err != nil {
		setupLog.Error(err, "unable to start manager")
		os.Exit(1)
	}
	ipamCtrl := ipam.IPAM{
		Client:       mgr.GetClient(),
		Cache:        mgr.GetCache(),
		UserIdlePool: make(map[string]map[string]*queue.Queue),
		Lookup:       make(map[string]bool),
		BciNode:      nil,
		WaitGCPod:    make(map[string]time.Time),
		Nlink:        netlinkwrapper.New(),
	}

	t, err := trigger.NewTrigger(trigger.Parameters{
		MinInterval:  opts.CustomResourceUpdateRate,
		TriggerFunc:  func(reasons []string) { ipamCtrl.UpdateStatus(ctx, reasons) },
		ShutdownFunc: nil,
		Name:         "bci-ipam-refresher",
	})
	if err != nil {
		setupLog.Error(err, "unable to start ipam refresh trigger")
		os.Exit(1)
	}

	ipamCtrl.RefreshTrigger = t

	someIndexer := mgr.GetFieldIndexer()
	_ = someIndexer.IndexField(ctx, &corev1.Pod{}, "spec.nodeName", selector)

	bciNodeCtrl := controllers.NewBciNodeController(
		mgr.GetClient(),
		unCachedClient,
		mgr.GetScheme(),
		&ipamCtrl,
		nodeName,
	)

	if err := bciNodeCtrl.CheckRequireUniqueRouteTable(ctx, nodeName); err != nil {
		setupLog.Error(err, "unable to check unique route table requirement")
		os.Exit(1)
	}

	if err := bciNodeCtrl.SetupWithManager(mgr); err != nil {
		setupLog.Error(err, "unable to create controller", "controller", "BciNode")
		os.Exit(1)
	}
	// +kubebuilder:scaffold:builder

	if err := mgr.AddHealthzCheck("healthz", healthz.Ping); err != nil {
		setupLog.Error(err, "unable to set up health check")
		os.Exit(1)
	}
	if err := mgr.AddReadyzCheck("readyz", healthz.Ping); err != nil {
		setupLog.Error(err, "unable to set up ready check")
		os.Exit(1)
	}

	// 2. start rpc server, client is bci-cni
	err = mgr.Add(server.NewGrpcServer(mgr, &ipamCtrl))
	if err != nil {
		setupLog.Error(err, "failed to add grpc server to runnables")
		os.Exit(1)
	}

	ipamCtrl.StartGCIPPeriod(ctx)
	ipamCtrl.StartUpdateStatusPeriod(ctx)

	// 3. start manager to watch apiserver
	setupLog.Info("starting manager")
	if err := mgr.Start(ctrl.SetupSignalHandler()); err != nil {
		setupLog.Error(err, "problem running manager")
		os.Exit(1)
	}

	// save meta before dead
	err = ipamCtrl.SaveMetaToDisk(ctx)
	if err != nil {
		setupLog.Error(err, "error writing meta to disk")
	}
}

func selector(o client.Object) []string {
	var res []string
	res = append(res, o.(*corev1.Pod).Spec.NodeName)
	return res
}
