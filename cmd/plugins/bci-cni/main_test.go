/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package main

import (
	"context"
	"flag"
	"net"
	"reflect"
	"syscall"
	"testing"

	"github.com/containernetworking/cni/pkg/skel"
	current "github.com/containernetworking/cni/pkg/types/100"
	cnins "github.com/containernetworking/plugins/pkg/ns"
	"github.com/golang/mock/gomock"
	"github.com/vishvananda/netlink"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/cmd/plugins/bci-cni/ipam"
	mockipamc "icode.baidu.com/baidu/bci2/bci-cni-driver/cmd/plugins/bci-cni/ipam/mock"
	rpcdef "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/rpc"
	typeswrapper "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/cnitypes"
	mocktypes "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/cnitypes/mock"
	ipwrapper "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/ip"
	mockip "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/ip/mock"
	ipamwrapper "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/ipam"
	mockipam "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/ipam/mock"
	netlinkwrapper "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/netlink"
	mocknetlink "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/netlink/mock"
	mocknetns "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/netns/mock"
	netutilwrapper "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/netutil"
	mocknetutil "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/netutil/mock"
	nswrapper "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/ns"
	mockns "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/ns/mock"
	oswrapper "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/os"
	mockos "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/os/mock"
)

var (
	envArgs = `IgnoreUnknown=1;K8S_POD_NAMESPACE=kube-system;K8S_POD_NAME=pod3;
	K8S_POD_INFRA_CONTAINER_ID=03090e4b6b1745a85c6513ec3a5c0039695658e8dec7adc221b6d2bc9c46a019`

	stdinData = `
	{
		"cniVersion": "0.4.0",
		"name": "bci",
		"type": "bci-cni"
	}`

	stdinDataWithPrevResult = `
{
    "cniVersion":"0.4.0",
    "name":"bci",
    "prevResult":{
        "cniVersion":"0.4.0",
        "interfaces":[
            {
                "name":"vethcc21c2d7785",
                "mac":"ba:93:2d:63:cf:22"
            },
            {
                "name":"eth0",
                "mac":"12:a6:63:62:fc:77",
                "sandbox":"/proc/1417189/ns/net"
            }
        ],
        "ips":[
            {
                "version":"4",
                "interface":1,
                "address":"***********/24",
                "gateway":"**********"
            }
        ],
        "routes":[
            {
                "dst":"0.0.0.0/0"
            }
        ],
        "dns":{

        }
    },
    "type":"bci-cni"
}
	`

	stdinDataWithPrevResultWithMTU = `
	{
			"cniVersion":"0.4.0",
			"name":"bci",
			"mtu": 1400,
			"prevResult":{
					"cniVersion":"0.4.0",
					"interfaces":[
							{
									"name":"vethcc21c2d7785",
									"mac":"ba:93:2d:63:cf:22"
							},
							{
									"name":"eth0",
									"mac":"12:a6:63:62:fc:77",
									"sandbox":"/proc/1417189/ns/net"
							}
					],
					"ips":[
							{
									"version":"4",
									"interface":1,
									"address":"***********/24",
									"gateway":"**********"
							}
					],
					"routes":[
							{
									"dst":"0.0.0.0/0"
							}
					],
					"dns":{

					}
			},
			"type":"bci-cni"
	}
		`
)

func setupEnv(t *testing.T) (
	*gomock.Controller,
	*mockos.MockInterface,
	*mocknetlink.MockInterface,
	*mocktypes.MockInterface,
	*mockip.MockInterface,
	*mockns.MockInterface,
	*mockipam.MockInterface,
	*mocknetutil.MockInterface,
	*mockipamc.MockInterface,
) {
	ctrl := gomock.NewController(t)

	os := mockos.NewMockInterface(ctrl)
	nlink := mocknetlink.NewMockInterface(ctrl)
	types := mocktypes.NewMockInterface(ctrl)
	ip := mockip.NewMockInterface(ctrl)
	ns := mockns.NewMockInterface(ctrl)
	ipam := mockipam.NewMockInterface(ctrl)
	netutil := mocknetutil.NewMockInterface(ctrl)
	ipamc := mockipamc.NewMockInterface(ctrl)

	return ctrl, os, nlink, types, ip, ns, ipam, netutil, ipamc
}

func Test_newBciCniPlugin(t *testing.T) {
	tests := []struct {
		name string
		want *bciCniPlugin
	}{
		{
			want: &bciCniPlugin{
				os:      oswrapper.New(),
				nlink:   netlinkwrapper.New(),
				types:   typeswrapper.New(),
				ip:      ipwrapper.New(),
				ns:      nswrapper.New(),
				ipam:    ipamwrapper.New(),
				netutil: netutilwrapper.New(),
				ipamc:   ipam.NewBciCniIpam(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := newBciCniPlugin(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("newBciCniPlugin() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_bciCniPlugin_cmdCheck(t *testing.T) {
	type fields struct {
		ctrl    *gomock.Controller
		os      oswrapper.Interface
		nlink   netlinkwrapper.Interface
		types   typeswrapper.Interface
		ip      ipwrapper.Interface
		ns      nswrapper.Interface
		ipam    ipamwrapper.Interface
		netutil netutilwrapper.Interface
		ipamc   ipam.Interface
	}
	type args struct {
		args *skel.CmdArgs
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "normal process",
			fields: func() fields {
				ctrl, os, nlink, types, ip, ns, ipam, netutil, ipamc := setupEnv(t)

				return fields{
					ctrl:    ctrl,
					os:      os,
					nlink:   nlink,
					types:   types,
					ip:      ip,
					ns:      ns,
					ipam:    ipam,
					netutil: netutil,
					ipamc:   ipamc,
				}
			}(),
			args: args{
				args: &skel.CmdArgs{
					ContainerID: "03090e4b6b1745a85c6513ec3a5c0039695658e8dec7adc221b6d2bc9c46a019",
					Netns:       "/proc/1417189/ns/net",
					IfName:      "eth0",
					Args:        envArgs,
					Path:        "/opt/cni/bin",
					StdinData:   []byte(stdinDataWithPrevResult),
				},
			},
			wantErr: false,
		},
		{
			name: "missing prevResult",
			fields: func() fields {
				ctrl, os, nlink, types, ip, ns, ipam, netutil, ipamc := setupEnv(t)

				return fields{
					ctrl:    ctrl,
					os:      os,
					nlink:   nlink,
					types:   types,
					ip:      ip,
					ns:      ns,
					ipam:    ipam,
					netutil: netutil,
					ipamc:   ipamc,
				}
			}(),
			args: args{
				args: &skel.CmdArgs{
					ContainerID: "03090e4b6b1745a85c6513ec3a5c0039695658e8dec7adc221b6d2bc9c46a019",
					Netns:       "/proc/394/ns/net",
					IfName:      "eth0",
					Args:        envArgs,
					Path:        "/opt/cni/bin",
					StdinData:   []byte(stdinData),
				},
			},
			wantErr: true,
		},
		{
			name: "sandbox in prevResult not matched",
			fields: func() fields {
				ctrl, os, nlink, types, ip, ns, ipam, netutil, ipamc := setupEnv(t)

				return fields{
					ctrl:    ctrl,
					os:      os,
					nlink:   nlink,
					types:   types,
					ip:      ip,
					ns:      ns,
					ipam:    ipam,
					netutil: netutil,
					ipamc:   ipamc,
				}
			}(),
			args: args{
				args: &skel.CmdArgs{
					ContainerID: "03090e4b6b1745a85c6513ec3a5c0039695658e8dec7adc221b6d2bc9c46a019",
					Netns:       "/proc/394/ns/net",
					IfName:      "eth0",
					Args:        envArgs,
					Path:        "/opt/cni/bin",
					StdinData:   []byte(stdinDataWithPrevResult),
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				tt.fields.ctrl.Finish()
			}

			p := &bciCniPlugin{
				os:      tt.fields.os,
				nlink:   tt.fields.nlink,
				types:   tt.fields.types,
				ip:      tt.fields.ip,
				ns:      tt.fields.ns,
				ipam:    tt.fields.ipam,
				netutil: tt.fields.netutil,
				ipamc:   tt.fields.ipamc,
			}

			if err := p.cmdCheck(tt.args.args); (err != nil) != tt.wantErr {
				t.Errorf("bciCniPlugin.cmdCheck() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_bciCniPlugin_cmdAdd(t *testing.T) {
	type fields struct {
		ctrl    *gomock.Controller
		os      oswrapper.Interface
		nlink   netlinkwrapper.Interface
		types   typeswrapper.Interface
		ip      ipwrapper.Interface
		ns      nswrapper.Interface
		ipam    ipamwrapper.Interface
		netutil netutilwrapper.Interface
		ipamc   ipam.Interface
	}
	type args struct {
		args *skel.CmdArgs
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "open container ns error",
			fields: func() fields {
				ctrl, os, nlink, types, ip, ns, ipam, netutil, ipamc := setupEnv(t)

				gomock.InOrder(
					ns.EXPECT().GetNS(gomock.Any()).Return(nil, syscall.ENOENT),
				)

				return fields{
					ctrl:    ctrl,
					os:      os,
					nlink:   nlink,
					types:   types,
					ip:      ip,
					ns:      ns,
					ipam:    ipam,
					netutil: netutil,
					ipamc:   ipamc,
				}
			}(),
			args: args{
				args: &skel.CmdArgs{
					ContainerID: "03090e4b6b1745a85c6513ec3a5c0039695658e8dec7adc221b6d2bc9c46a019",
					Netns:       "/proc/394/ns/net",
					IfName:      "eth0",
					Args:        envArgs,
					Path:        "/opt/cni/bin",
					StdinData:   []byte(stdinData),
				},
			},
			wantErr: true,
		},
		{
			name: "ipam allocates ip failed",
			fields: func() fields {
				ctrl, os, nlink, types, ip, ns, ipam, netutil, ipamc := setupEnv(t)

				netns := mocknetns.NewMockNetNS(ctrl)
				resp := &rpcdef.Response{
					IsSuccess: false,
					ErrMsg:    "ip out of range",
				}

				gomock.InOrder(
					ns.EXPECT().GetNS(gomock.Any()).Return(netns, nil),
					ipamc.EXPECT().CmdAdd(gomock.Any(), gomock.Any(), gomock.Any()).Return(resp, nil),
					ipamc.EXPECT().CmdDel(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
					netns.EXPECT().Close().Return(nil),
				)

				return fields{
					ctrl:    ctrl,
					os:      os,
					nlink:   nlink,
					types:   types,
					ip:      ip,
					ns:      ns,
					ipam:    ipam,
					netutil: netutil,
					ipamc:   ipamc,
				}
			}(),
			args: args{
				args: &skel.CmdArgs{
					ContainerID: "03090e4b6b1745a85c6513ec3a5c0039695658e8dec7adc221b6d2bc9c46a019",
					Netns:       "/proc/394/ns/net",
					IfName:      "eth0",
					Args:        envArgs,
					Path:        "/opt/cni/bin",
					StdinData:   []byte(stdinDataWithPrevResult),
				},
			},
			wantErr: true,
		},
		{
			name: "ipam returns empty response",
			fields: func() fields {
				ctrl, os, nlink, types, ip, ns, ipam, netutil, ipamc := setupEnv(t)

				netns := mocknetns.NewMockNetNS(ctrl)
				resp := &rpcdef.Response{
					IsSuccess:   true,
					NetworkInfo: nil,
				}

				gomock.InOrder(
					ns.EXPECT().GetNS(gomock.Any()).Return(netns, nil),
					ipamc.EXPECT().CmdAdd(gomock.Any(), gomock.Any(), gomock.Any()).Return(resp, nil),
					// ipamc.EXPECT().CmdDel(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
					netns.EXPECT().Close().Return(nil),
				)

				return fields{
					ctrl:    ctrl,
					os:      os,
					nlink:   nlink,
					types:   types,
					ip:      ip,
					ns:      ns,
					ipam:    ipam,
					netutil: netutil,
					ipamc:   ipamc,
				}
			}(),
			args: args{
				args: &skel.CmdArgs{
					ContainerID: "03090e4b6b1745a85c6513ec3a5c0039695658e8dec7adc221b6d2bc9c46a019",
					Netns:       "/proc/394/ns/net",
					IfName:      "eth0",
					Args:        envArgs,
					Path:        "/opt/cni/bin",
					StdinData:   []byte(stdinDataWithPrevResult),
				},
			},
			wantErr: true,
		},
		{
			name: "normal process",
			fields: func() fields {
				ctrl, os, nlink, types, ip, ns, ipam, netutil, ipamc := setupEnv(t)

				netns := mocknetns.NewMockNetNS(ctrl)

				resp := &rpcdef.Response{
					IsSuccess: true,
					NetworkInfo: &rpcdef.Response_ENIMultiIP{
						ENIMultiIP: &rpcdef.ENIMultiIPReply{
							IP:      "***********",
							Gateway: "***************",
						},
					},
				}

				enins := mocknetns.NewMockNetNS(ctrl)

				gomock.InOrder(
					ns.EXPECT().GetNS(gomock.Any()).Return(netns, nil),
					ipamc.EXPECT().CmdAdd(gomock.Any(), gomock.Any(), gomock.Any()).Return(resp, nil),
					ns.EXPECT().GetNS(gomock.Any()).Return(enins, nil),

					// detect eni mtu
					enins.EXPECT().Do(gomock.Any()).Return(nil),

					// setupContainerVeth
					netns.EXPECT().Do(gomock.Any()).Return(nil),
					// setupEniNsVeth
					nlink.EXPECT().LinkByName(gomock.Any()).Return(&netlink.Veth{}, nil),
					enins.EXPECT().Fd().Return(uintptr(1)),
					nlink.EXPECT().LinkSetNsFd(gomock.Any(), gomock.Any()).Return(nil),
					enins.EXPECT().Do(gomock.Any()).Return(nil),

					types.EXPECT().PrintResult(gomock.Any(), gomock.Any()).Return(nil),

					// defer
					enins.EXPECT().Close().Return(nil),
					netns.EXPECT().Close().Return(nil),
				)

				return fields{
					ctrl:    ctrl,
					os:      os,
					nlink:   nlink,
					types:   types,
					ip:      ip,
					ns:      ns,
					ipam:    ipam,
					netutil: netutil,
					ipamc:   ipamc,
				}
			}(),
			args: args{
				args: &skel.CmdArgs{
					ContainerID: "03090e4b6b1745a85c6513ec3a5c0039695658e8dec7adc221b6d2bc9c46a019",
					Netns:       "/proc/394/ns/net",
					IfName:      "eth0",
					Args:        envArgs,
					Path:        "/opt/cni/bin",
					StdinData:   []byte(stdinDataWithPrevResult),
				},
			},
			wantErr: false,
		},
		{
			name: "normal process with mtu specified",
			fields: func() fields {
				ctrl, os, nlink, types, ip, ns, ipam, netutil, ipamc := setupEnv(t)

				netns := mocknetns.NewMockNetNS(ctrl)

				resp := &rpcdef.Response{
					IsSuccess: true,
					NetworkInfo: &rpcdef.Response_ENIMultiIP{
						ENIMultiIP: &rpcdef.ENIMultiIPReply{
							IP:      "***********",
							Gateway: "***************",
						},
					},
				}

				enins := mocknetns.NewMockNetNS(ctrl)

				gomock.InOrder(
					ns.EXPECT().GetNS(gomock.Any()).Return(netns, nil),
					ipamc.EXPECT().CmdAdd(gomock.Any(), gomock.Any(), gomock.Any()).Return(resp, nil),
					ns.EXPECT().GetNS(gomock.Any()).Return(enins, nil),

					// setupContainerVeth
					netns.EXPECT().Do(gomock.Any()).Return(nil),
					// setupEniNsVeth
					nlink.EXPECT().LinkByName(gomock.Any()).Return(&netlink.Veth{}, nil),
					enins.EXPECT().Fd().Return(uintptr(1)),
					nlink.EXPECT().LinkSetNsFd(gomock.Any(), gomock.Any()).Return(nil),
					enins.EXPECT().Do(gomock.Any()).Return(nil),

					types.EXPECT().PrintResult(gomock.Any(), gomock.Any()).Return(nil),

					// defer
					enins.EXPECT().Close().Return(nil),
					netns.EXPECT().Close().Return(nil),
				)

				return fields{
					ctrl:    ctrl,
					os:      os,
					nlink:   nlink,
					types:   types,
					ip:      ip,
					ns:      ns,
					ipam:    ipam,
					netutil: netutil,
					ipamc:   ipamc,
				}
			}(),
			args: args{
				args: &skel.CmdArgs{
					ContainerID: "03090e4b6b1745a85c6513ec3a5c0039695658e8dec7adc221b6d2bc9c46a019",
					Netns:       "/proc/394/ns/net",
					IfName:      "eth0",
					Args:        envArgs,
					Path:        "/opt/cni/bin",
					StdinData:   []byte(stdinDataWithPrevResultWithMTU),
				},
			},
			wantErr: false,
		},
		{
			name: "normal process with pfs node",
			fields: func() fields {
				ctrl, os, nlink, types, ip, ns, ipam, netutil, ipamc := setupEnv(t)

				netns := mocknetns.NewMockNetNS(ctrl)

				resp := &rpcdef.Response{
					IsSuccess: true,
					NetworkInfo: &rpcdef.Response_ENIMultiIP{
						ENIMultiIP: &rpcdef.ENIMultiIPReply{
							IP:                         "***********",
							Gateway:                    "***************",
							EniRequireUniqueRouteTable: true,
						},
					},
				}

				enins := mocknetns.NewMockNetNS(ctrl)

				gomock.InOrder(
					ns.EXPECT().GetNS(gomock.Any()).Return(netns, nil),
					ipamc.EXPECT().CmdAdd(gomock.Any(), gomock.Any(), gomock.Any()).Return(resp, nil),
					ns.EXPECT().GetNS(gomock.Any()).Return(enins, nil),

					// detect eni mtu
					enins.EXPECT().Do(gomock.Any()).Return(nil),

					// setupContainerVeth
					netns.EXPECT().Do(gomock.Any()).Return(nil),
					// setupEniNsVeth
					nlink.EXPECT().LinkByName(gomock.Any()).Return(&netlink.Veth{}, nil),
					enins.EXPECT().Fd().Return(uintptr(1)),
					nlink.EXPECT().LinkSetNsFd(gomock.Any(), gomock.Any()).Return(nil),
					enins.EXPECT().Do(gomock.Any()).Return(nil),

					// clean up rule
					nlink.EXPECT().RuleDel(gomock.Any()).Return(nil),
					nlink.EXPECT().RuleDel(gomock.Any()).Return(nil),

					// add to container rule
					nlink.EXPECT().RuleDel(gomock.Any()).Return(nil),
					nlink.EXPECT().RuleAdd(gomock.Any()).Return(nil),
					// add from container rule
					nlink.EXPECT().RuleDel(gomock.Any()).Return(nil),
					nlink.EXPECT().RuleAdd(gomock.Any()).Return(nil),

					types.EXPECT().PrintResult(gomock.Any(), gomock.Any()).Return(nil),

					// defer
					enins.EXPECT().Close().Return(nil),
					netns.EXPECT().Close().Return(nil),
				)

				return fields{
					ctrl:    ctrl,
					os:      os,
					nlink:   nlink,
					types:   types,
					ip:      ip,
					ns:      ns,
					ipam:    ipam,
					netutil: netutil,
					ipamc:   ipamc,
				}
			}(),
			args: args{
				args: &skel.CmdArgs{
					ContainerID: "03090e4b6b1745a85c6513ec3a5c0039695658e8dec7adc221b6d2bc9c46a019",
					Netns:       "/proc/394/ns/net",
					IfName:      "eth0",
					Args:        envArgs,
					Path:        "/opt/cni/bin",
					StdinData:   []byte(stdinDataWithPrevResult),
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}

			p := &bciCniPlugin{
				os:      tt.fields.os,
				nlink:   tt.fields.nlink,
				types:   tt.fields.types,
				ip:      tt.fields.ip,
				ns:      tt.fields.ns,
				ipam:    tt.fields.ipam,
				netutil: tt.fields.netutil,
				ipamc:   tt.fields.ipamc,
			}

			if err := p.cmdAdd(tt.args.args); (err != nil) != tt.wantErr {
				t.Errorf("bciCniPlugin.cmdAdd() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_bciCniPlugin_cmdDel(t *testing.T) {
	type fields struct {
		ctrl    *gomock.Controller
		os      oswrapper.Interface
		nlink   netlinkwrapper.Interface
		types   typeswrapper.Interface
		ip      ipwrapper.Interface
		ns      nswrapper.Interface
		ipam    ipamwrapper.Interface
		netutil netutilwrapper.Interface
		ipamc   ipam.Interface
	}
	type args struct {
		args *skel.CmdArgs
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "normal process",
			fields: func() fields {
				ctrl, os, nlink, types, ip, ns, ipam, netutil, ipamc := setupEnv(t)

				resp := &rpcdef.Response{
					IsSuccess: true,
					NetworkInfo: &rpcdef.Response_ENIMultiIP{
						ENIMultiIP: &rpcdef.ENIMultiIPReply{
							IP:      "***********",
							Gateway: "***************",
						},
					},
				}

				gomock.InOrder(
					ipamc.EXPECT().CmdDel(gomock.Any(), gomock.Any(), gomock.Any()).Return(resp, nil),
					ns.EXPECT().WithNetNSPath(gomock.Any(), gomock.Any()).Return(nil),
				)

				return fields{
					ctrl:    ctrl,
					os:      os,
					nlink:   nlink,
					types:   types,
					ip:      ip,
					ns:      ns,
					ipam:    ipam,
					netutil: netutil,
					ipamc:   ipamc,
				}
			}(),
			args: args{
				args: &skel.CmdArgs{
					ContainerID: "03090e4b6b1745a85c6513ec3a5c0039695658e8dec7adc221b6d2bc9c46a019",
					Netns:       "/proc/394/ns/net",
					IfName:      "eth0",
					Args:        envArgs,
					Path:        "/opt/cni/bin",
					StdinData:   []byte(stdinDataWithPrevResult),
				},
			},
			wantErr: false,
		},
		{
			name: " delete link failed",
			fields: func() fields {
				ctrl, os, nlink, types, ip, ns, ipam, netutil, ipamc := setupEnv(t)

				resp := &rpcdef.Response{
					IsSuccess: true,
					NetworkInfo: &rpcdef.Response_ENIMultiIP{
						ENIMultiIP: &rpcdef.ENIMultiIPReply{
							IP:      "***********",
							Gateway: "***************",
						},
					},
				}

				gomock.InOrder(
					ipamc.EXPECT().CmdDel(gomock.Any(), gomock.Any(), gomock.Any()).Return(resp, nil),
					ns.EXPECT().WithNetNSPath(gomock.Any(), gomock.Any()).Return(syscall.ENODEV),
				)

				return fields{
					ctrl:    ctrl,
					os:      os,
					nlink:   nlink,
					types:   types,
					ip:      ip,
					ns:      ns,
					ipam:    ipam,
					netutil: netutil,
					ipamc:   ipamc,
				}
			}(),
			args: args{
				args: &skel.CmdArgs{
					ContainerID: "03090e4b6b1745a85c6513ec3a5c0039695658e8dec7adc221b6d2bc9c46a019",
					Netns:       "/proc/394/ns/net",
					IfName:      "eth0",
					Args:        envArgs,
					Path:        "/opt/cni/bin",
					StdinData:   []byte(stdinDataWithPrevResult),
				},
			},
			wantErr: true,
		},
		{
			name: " ipam release ip failed",
			fields: func() fields {
				ctrl, os, nlink, types, ip, ns, ipam, netutil, ipamc := setupEnv(t)

				resp := &rpcdef.Response{
					IsSuccess: false,
					ErrMsg:    "internal error",
				}

				gomock.InOrder(
					ipamc.EXPECT().CmdDel(gomock.Any(), gomock.Any(), gomock.Any()).Return(resp, nil),
				)

				return fields{
					ctrl:    ctrl,
					os:      os,
					nlink:   nlink,
					types:   types,
					ip:      ip,
					ns:      ns,
					ipam:    ipam,
					netutil: netutil,
					ipamc:   ipamc,
				}
			}(),
			args: args{
				args: &skel.CmdArgs{
					ContainerID: "03090e4b6b1745a85c6513ec3a5c0039695658e8dec7adc221b6d2bc9c46a019",
					Netns:       "/proc/394/ns/net",
					IfName:      "eth0",
					Args:        envArgs,
					Path:        "/opt/cni/bin",
					StdinData:   []byte(stdinDataWithPrevResult),
				},
			},
			wantErr: true,
		},
		{
			name: "ns not exited",
			fields: func() fields {
				ctrl, os, nlink, types, ip, ns, ipam, netutil, ipamc := setupEnv(t)

				resp := &rpcdef.Response{
					IsSuccess: true,
					NetworkInfo: &rpcdef.Response_ENIMultiIP{
						ENIMultiIP: &rpcdef.ENIMultiIPReply{
							IP:      "***********",
							Gateway: "***************",
						},
					},
				}

				gomock.InOrder(
					ipamc.EXPECT().CmdDel(gomock.Any(), gomock.Any(), gomock.Any()).Return(resp, nil),
					ns.EXPECT().WithNetNSPath(gomock.Any(), gomock.Any()).Return(cnins.NSPathNotExistErr{}),
				)

				return fields{
					ctrl:    ctrl,
					os:      os,
					nlink:   nlink,
					types:   types,
					ip:      ip,
					ns:      ns,
					ipam:    ipam,
					netutil: netutil,
					ipamc:   ipamc,
				}
			}(),
			args: args{
				args: &skel.CmdArgs{
					ContainerID: "03090e4b6b1745a85c6513ec3a5c0039695658e8dec7adc221b6d2bc9c46a019",
					Netns:       "/proc/394/ns/net",
					IfName:      "eth0",
					Args:        envArgs,
					Path:        "/opt/cni/bin",
					StdinData:   []byte(stdinDataWithPrevResult),
				},
			},
			wantErr: false,
		},
		{
			name: "normal process with pfs node",
			fields: func() fields {
				ctrl, os, nlink, types, ip, ns, ipam, netutil, ipamc := setupEnv(t)

				resp := &rpcdef.Response{
					IsSuccess: true,
					NetworkInfo: &rpcdef.Response_ENIMultiIP{
						ENIMultiIP: &rpcdef.ENIMultiIPReply{
							IP:                         "***********",
							Gateway:                    "***************",
							EniRequireUniqueRouteTable: true,
						},
					},
				}

				gomock.InOrder(
					ipamc.EXPECT().CmdDel(gomock.Any(), gomock.Any(), gomock.Any()).Return(resp, nil),
					ns.EXPECT().WithNetNSPath(gomock.Any(), gomock.Any()).Return(nil),

					nlink.EXPECT().RuleDel(gomock.Any()).Return(nil),
					nlink.EXPECT().RuleDel(gomock.Any()).Return(nil),
				)

				return fields{
					ctrl:    ctrl,
					os:      os,
					nlink:   nlink,
					types:   types,
					ip:      ip,
					ns:      ns,
					ipam:    ipam,
					netutil: netutil,
					ipamc:   ipamc,
				}
			}(),
			args: args{
				args: &skel.CmdArgs{
					ContainerID: "03090e4b6b1745a85c6513ec3a5c0039695658e8dec7adc221b6d2bc9c46a019",
					Netns:       "/proc/394/ns/net",
					IfName:      "eth0",
					Args:        envArgs,
					Path:        "/opt/cni/bin",
					StdinData:   []byte(stdinDataWithPrevResult),
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}

			p := &bciCniPlugin{
				os:      tt.fields.os,
				nlink:   tt.fields.nlink,
				types:   tt.fields.types,
				ip:      tt.fields.ip,
				ns:      tt.fields.ns,
				ipam:    tt.fields.ipam,
				netutil: tt.fields.netutil,
				ipamc:   tt.fields.ipamc,
			}

			if err := p.cmdDel(tt.args.args); (err != nil) != tt.wantErr {
				t.Errorf("bciCniPlugin.cmdDel() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_buildString(t *testing.T) {
	type args struct {
		pluginName string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "normal process",
			args: args{
				pluginName: PluginName,
			},
			want: "CNI bci-cni plugin ",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := buildString(tt.args.pluginName); got != tt.want {
				t.Errorf("buildString() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_bciCniPlugin_detectEniMtu(t *testing.T) {
	type fields struct {
		ctrl    *gomock.Controller
		os      oswrapper.Interface
		nlink   netlinkwrapper.Interface
		types   typeswrapper.Interface
		ip      ipwrapper.Interface
		ns      nswrapper.Interface
		ipam    ipamwrapper.Interface
		netutil netutilwrapper.Interface
		ipamc   ipam.Interface
	}
	tests := []struct {
		name    string
		fields  fields
		want    int
		wantErr bool
	}{
		{
			name: "route failed",
			fields: func() fields {
				ctrl, os, nlink, types, ip, ns, ipam, netutil, ipamc := setupEnv(t)

				gomock.InOrder(
					netutil.EXPECT().DetectDefaultRouteInterfaceName().Return("", netlink.ErrNotImplemented),
				)

				return fields{
					ctrl:    ctrl,
					os:      os,
					nlink:   nlink,
					types:   types,
					ip:      ip,
					ns:      ns,
					ipam:    ipam,
					netutil: netutil,
					ipamc:   ipamc,
				}
			}(),
			want:    0,
			wantErr: true,
		},
		{
			name: "link failed",
			fields: func() fields {
				ctrl, os, nlink, types, ip, ns, ipam, netutil, ipamc := setupEnv(t)

				gomock.InOrder(
					netutil.EXPECT().DetectDefaultRouteInterfaceName().Return("eth0", nil),
					netutil.EXPECT().DetectInterfaceMTU("eth0").Return(0, netlink.ErrNotImplemented),
				)

				return fields{
					ctrl:    ctrl,
					os:      os,
					nlink:   nlink,
					types:   types,
					ip:      ip,
					ns:      ns,
					ipam:    ipam,
					netutil: netutil,
					ipamc:   ipamc,
				}
			}(),
			want:    0,
			wantErr: true,
		},
		{
			name: "normal process",
			fields: func() fields {
				ctrl, os, nlink, types, ip, ns, ipam, netutil, ipamc := setupEnv(t)

				gomock.InOrder(
					netutil.EXPECT().DetectDefaultRouteInterfaceName().Return("eth0", nil),
					netutil.EXPECT().DetectInterfaceMTU("eth0").Return(1600, nil),
				)

				return fields{
					ctrl:    ctrl,
					os:      os,
					nlink:   nlink,
					types:   types,
					ip:      ip,
					ns:      ns,
					ipam:    ipam,
					netutil: netutil,
					ipamc:   ipamc,
				}
			}(),
			want:    1600,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}

			p := &bciCniPlugin{
				os:      tt.fields.os,
				nlink:   tt.fields.nlink,
				types:   tt.fields.types,
				ip:      tt.fields.ip,
				ns:      tt.fields.ns,
				ipam:    tt.fields.ipam,
				netutil: tt.fields.netutil,
				ipamc:   tt.fields.ipamc,
			}

			got, err := p.detectEniMtu()
			if (err != nil) != tt.wantErr {
				t.Errorf("bciCniPlugin.detectEniMtu() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("bciCniPlugin.detectEniMtu() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_initFlags(t *testing.T) {
	tests := []struct {
		name string
	}{
		{
			name: "正常流程",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			initFlags()
		})
	}
}

func Test_errorWithLog(t *testing.T) {
	_ = flag.Set("log_file", "./bci-cni.log")
	flag.Parse()

	type args struct {
		ctx context.Context
		err error
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "正常流程",
			args: args{
				ctx: context.TODO(),
				err: net.ErrClosed,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := errorWithLog(tt.args.ctx, tt.args.err); (err != nil) != tt.wantErr {
				t.Errorf("errorWithLog() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_bciCniPlugin_addToContainerRule(t *testing.T) {
	type fields struct {
		ctrl    *gomock.Controller
		os      oswrapper.Interface
		nlink   netlinkwrapper.Interface
		types   typeswrapper.Interface
		ip      ipwrapper.Interface
		ns      nswrapper.Interface
		ipam    ipamwrapper.Interface
		netutil netutilwrapper.Interface
		ipamc   ipam.Interface
	}
	type args struct {
		addr net.IP
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "normal process",
			fields: func() fields {
				ctrl, os, nlink, types, ip, ns, ipam, netutil, ipamc := setupEnv(t)

				gomock.InOrder(
					nlink.EXPECT().RuleDel(gomock.Any()).Return(nil),
					nlink.EXPECT().RuleAdd(gomock.Any()).Return(nil),
				)

				return fields{
					ctrl:    ctrl,
					os:      os,
					nlink:   nlink,
					types:   types,
					ip:      ip,
					ns:      ns,
					ipam:    ipam,
					netutil: netutil,
					ipamc:   ipamc,
				}
			}(),
			args: args{
				addr: []byte{},
			},
			wantErr: false,
		},
		{
			name: "rule del error process",
			fields: func() fields {
				ctrl, os, nlink, types, ip, ns, ipam, netutil, ipamc := setupEnv(t)

				gomock.InOrder(
					nlink.EXPECT().RuleDel(gomock.Any()).Return(netlink.ErrNotImplemented),
				)

				return fields{
					ctrl:    ctrl,
					os:      os,
					nlink:   nlink,
					types:   types,
					ip:      ip,
					ns:      ns,
					ipam:    ipam,
					netutil: netutil,
					ipamc:   ipamc,
				}
			}(),
			args: args{
				addr: []byte{},
			},
			wantErr: true,
		},
		{
			name: "rule add error process",
			fields: func() fields {
				ctrl, os, nlink, types, ip, ns, ipam, netutil, ipamc := setupEnv(t)

				gomock.InOrder(
					nlink.EXPECT().RuleDel(gomock.Any()).Return(nil),
					nlink.EXPECT().RuleAdd(gomock.Any()).Return(netlink.ErrNotImplemented),
				)

				return fields{
					ctrl:    ctrl,
					os:      os,
					nlink:   nlink,
					types:   types,
					ip:      ip,
					ns:      ns,
					ipam:    ipam,
					netutil: netutil,
					ipamc:   ipamc,
				}
			}(),
			args: args{
				addr: []byte{},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}

			p := &bciCniPlugin{
				os:      tt.fields.os,
				nlink:   tt.fields.nlink,
				types:   tt.fields.types,
				ip:      tt.fields.ip,
				ns:      tt.fields.ns,
				ipam:    tt.fields.ipam,
				netutil: tt.fields.netutil,
				ipamc:   tt.fields.ipamc,
			}
			if err := p.addToContainerRule(tt.args.addr); (err != nil) != tt.wantErr {
				t.Errorf("bciCniPlugin.addToContainerRule() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_bciCniPlugin_addFromContainerRule(t *testing.T) {
	type fields struct {
		ctrl    *gomock.Controller
		os      oswrapper.Interface
		nlink   netlinkwrapper.Interface
		types   typeswrapper.Interface
		ip      ipwrapper.Interface
		ns      nswrapper.Interface
		ipam    ipamwrapper.Interface
		netutil netutilwrapper.Interface
		ipamc   ipam.Interface
	}
	type args struct {
		addr          net.IP
		hostInterface *current.Interface
		rtTable       int
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "normal process",
			fields: func() fields {
				ctrl, os, nlink, types, ip, ns, ipam, netutil, ipamc := setupEnv(t)

				gomock.InOrder(
					nlink.EXPECT().RuleDel(gomock.Any()).Return(nil),
					nlink.EXPECT().RuleAdd(gomock.Any()).Return(nil),
				)

				return fields{
					ctrl:    ctrl,
					os:      os,
					nlink:   nlink,
					types:   types,
					ip:      ip,
					ns:      ns,
					ipam:    ipam,
					netutil: netutil,
					ipamc:   ipamc,
				}
			}(),
			args: args{
				addr: []byte{},
				hostInterface: &current.Interface{
					Name:    "",
					Mac:     "",
					Sandbox: "",
				},
				rtTable: 0,
			},
			wantErr: false,
		},
		{
			name: "rule del error process",
			fields: func() fields {
				ctrl, os, nlink, types, ip, ns, ipam, netutil, ipamc := setupEnv(t)

				gomock.InOrder(
					nlink.EXPECT().RuleDel(gomock.Any()).Return(netlink.ErrNotImplemented),
				)

				return fields{
					ctrl:    ctrl,
					os:      os,
					nlink:   nlink,
					types:   types,
					ip:      ip,
					ns:      ns,
					ipam:    ipam,
					netutil: netutil,
					ipamc:   ipamc,
				}
			}(),
			args: args{
				addr: []byte{},
				hostInterface: &current.Interface{
					Name:    "",
					Mac:     "",
					Sandbox: "",
				},
				rtTable: 0,
			},
			wantErr: true,
		},
		{
			name: "rule add error process",
			fields: func() fields {
				ctrl, os, nlink, types, ip, ns, ipam, netutil, ipamc := setupEnv(t)

				gomock.InOrder(
					nlink.EXPECT().RuleDel(gomock.Any()).Return(nil),
					nlink.EXPECT().RuleAdd(gomock.Any()).Return(netlink.ErrNotImplemented),
				)

				return fields{
					ctrl:    ctrl,
					os:      os,
					nlink:   nlink,
					types:   types,
					ip:      ip,
					ns:      ns,
					ipam:    ipam,
					netutil: netutil,
					ipamc:   ipamc,
				}
			}(),
			args: args{
				addr: []byte{},
				hostInterface: &current.Interface{
					Name:    "",
					Mac:     "",
					Sandbox: "",
				},
				rtTable: 0,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}

			p := &bciCniPlugin{
				os:      tt.fields.os,
				nlink:   tt.fields.nlink,
				types:   tt.fields.types,
				ip:      tt.fields.ip,
				ns:      tt.fields.ns,
				ipam:    tt.fields.ipam,
				netutil: tt.fields.netutil,
				ipamc:   tt.fields.ipamc,
			}
			if err := p.addFromContainerRule(tt.args.addr, tt.args.hostInterface, tt.args.rtTable); (err != nil) != tt.wantErr {
				t.Errorf("bciCniPlugin.addFromContainerRule() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_bciCniPlugin_delToOrFromContainerRule(t *testing.T) {
	type fields struct {
		ctrl    *gomock.Controller
		os      oswrapper.Interface
		nlink   netlinkwrapper.Interface
		types   typeswrapper.Interface
		ip      ipwrapper.Interface
		ns      nswrapper.Interface
		ipam    ipamwrapper.Interface
		netutil netutilwrapper.Interface
		ipamc   ipam.Interface
	}
	type args struct {
		isToContainer bool
		addr          net.IP
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "to container normal process",
			fields: func() fields {
				ctrl, os, nlink, types, ip, ns, ipam, netutil, ipamc := setupEnv(t)

				gomock.InOrder(
					nlink.EXPECT().RuleDel(gomock.Any()).Return(nil),
				)

				return fields{
					ctrl:    ctrl,
					os:      os,
					nlink:   nlink,
					types:   types,
					ip:      ip,
					ns:      ns,
					ipam:    ipam,
					netutil: netutil,
					ipamc:   ipamc,
				}
			}(),
			args: args{
				isToContainer: false,
				addr:          net.ParseIP("::0"),
			},
			wantErr: false,
		},
		{
			name: "from container normal process",
			fields: func() fields {
				ctrl, os, nlink, types, ip, ns, ipam, netutil, ipamc := setupEnv(t)

				gomock.InOrder(
					nlink.EXPECT().RuleDel(gomock.Any()).Return(nil),
				)

				return fields{
					ctrl:    ctrl,
					os:      os,
					nlink:   nlink,
					types:   types,
					ip:      ip,
					ns:      ns,
					ipam:    ipam,
					netutil: netutil,
					ipamc:   ipamc,
				}
			}(),
			args: args{
				isToContainer: true,
				addr:          net.ParseIP("::0"),
			},
			wantErr: false,
		},
		{
			name: "from container normal process",
			fields: func() fields {
				ctrl, os, nlink, types, ip, ns, ipam, netutil, ipamc := setupEnv(t)

				gomock.InOrder(
					nlink.EXPECT().RuleDel(gomock.Any()).Return(netlink.ErrNotImplemented),
				)

				return fields{
					ctrl:    ctrl,
					os:      os,
					nlink:   nlink,
					types:   types,
					ip:      ip,
					ns:      ns,
					ipam:    ipam,
					netutil: netutil,
					ipamc:   ipamc,
				}
			}(),
			args: args{
				isToContainer: true,
				addr:          net.ParseIP("::0"),
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}

			p := &bciCniPlugin{
				os:      tt.fields.os,
				nlink:   tt.fields.nlink,
				types:   tt.fields.types,
				ip:      tt.fields.ip,
				ns:      tt.fields.ns,
				ipam:    tt.fields.ipam,
				netutil: tt.fields.netutil,
				ipamc:   tt.fields.ipamc,
			}
			if err := p.delToOrFromContainerRule(tt.args.isToContainer, tt.args.addr); (err != nil) != tt.wantErr {
				t.Errorf("bciCniPlugin.delToOrFromContainerRule() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_bciCniPlugin_getPodIPFromNetNS(t *testing.T) {
	type fields struct {
		ctrl    *gomock.Controller
		os      oswrapper.Interface
		nlink   netlinkwrapper.Interface
		types   typeswrapper.Interface
		ip      ipwrapper.Interface
		ns      nswrapper.Interface
		ipam    ipamwrapper.Interface
		netutil netutilwrapper.Interface
		ipamc   ipam.Interface
	}
	type args struct {
		ifName   string
		ipFamily int
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    net.IP
		wantErr bool
	}{
		{
			name: "normal process",
			fields: func() fields {
				ctrl, os, nlink, types, ip, ns, ipam, netutil, ipamc := setupEnv(t)

				gomock.InOrder(
					nlink.EXPECT().LinkByName(gomock.Any()).Return(&netlink.Veth{}, nil),
					nlink.EXPECT().AddrList(gomock.Any(), gomock.Any()).Return([]netlink.Addr{
						{
							IPNet: &net.IPNet{
								IP:   net.ParseIP("*******"),
								Mask: []byte{},
							},
						},
					}, nil),
				)

				return fields{
					ctrl:    ctrl,
					os:      os,
					nlink:   nlink,
					types:   types,
					ip:      ip,
					ns:      ns,
					ipam:    ipam,
					netutil: netutil,
					ipamc:   ipamc,
				}
			}(),
			args: args{
				ifName:   "",
				ipFamily: 0,
			},
			want:    net.ParseIP("*******"),
			wantErr: false,
		},
		{
			name: "link error process",
			fields: func() fields {
				ctrl, os, nlink, types, ip, ns, ipam, netutil, ipamc := setupEnv(t)

				gomock.InOrder(
					nlink.EXPECT().LinkByName(gomock.Any()).Return(&netlink.Veth{}, netlink.ErrNotImplemented),
				)

				return fields{
					ctrl:    ctrl,
					os:      os,
					nlink:   nlink,
					types:   types,
					ip:      ip,
					ns:      ns,
					ipam:    ipam,
					netutil: netutil,
					ipamc:   ipamc,
				}
			}(),
			args: args{
				ifName:   "",
				ipFamily: 0,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "addr error process",
			fields: func() fields {
				ctrl, os, nlink, types, ip, ns, ipam, netutil, ipamc := setupEnv(t)

				gomock.InOrder(
					nlink.EXPECT().LinkByName(gomock.Any()).Return(&netlink.Veth{}, nil),
					nlink.EXPECT().AddrList(gomock.Any(), gomock.Any()).Return(nil, netlink.ErrNotImplemented),
				)

				return fields{
					ctrl:    ctrl,
					os:      os,
					nlink:   nlink,
					types:   types,
					ip:      ip,
					ns:      ns,
					ipam:    ipam,
					netutil: netutil,
					ipamc:   ipamc,
				}
			}(),
			args: args{
				ifName:   "",
				ipFamily: 0,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "no ip addr",
			fields: func() fields {
				ctrl, os, nlink, types, ip, ns, ipam, netutil, ipamc := setupEnv(t)

				gomock.InOrder(
					nlink.EXPECT().LinkByName(gomock.Any()).Return(&netlink.Veth{}, nil),
					nlink.EXPECT().AddrList(gomock.Any(), gomock.Any()).Return(nil, nil),
				)

				return fields{
					ctrl:    ctrl,
					os:      os,
					nlink:   nlink,
					types:   types,
					ip:      ip,
					ns:      ns,
					ipam:    ipam,
					netutil: netutil,
					ipamc:   ipamc,
				}
			}(),
			args: args{
				ifName:   "",
				ipFamily: 0,
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}

			p := &bciCniPlugin{
				os:      tt.fields.os,
				nlink:   tt.fields.nlink,
				types:   tt.fields.types,
				ip:      tt.fields.ip,
				ns:      tt.fields.ns,
				ipam:    tt.fields.ipam,
				netutil: tt.fields.netutil,
				ipamc:   tt.fields.ipamc,
			}
			got, err := p.getPodIPFromNetNS(tt.args.ifName, tt.args.ipFamily)
			if (err != nil) != tt.wantErr {
				t.Errorf("bciCniPlugin.getPodIPFromNetNS() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("bciCniPlugin.getPodIPFromNetNS() = %v, want %v", got, tt.want)
			}
		})
	}
}
