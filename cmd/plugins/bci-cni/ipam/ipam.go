/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package ipam

import (
	"context"
	"fmt"
	"net"
	"time"

	"github.com/containernetworking/cni/pkg/skel"
	"google.golang.org/grpc"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/cmd/plugins/bci-cni/types"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/logger"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/nodeagent/server"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/rpc"
	grpcwrapper "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/grpc"
	rpcwrapper "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/rpc"
)

var (
	rpcTimeout = 40 * time.Second
	endpoint   = server.GrpcSocketFilePath
)

var (
	dialer = func(ctx context.Context, s string) (net.Conn, error) {
		unixAddr, err := net.ResolveUnixAddr("unix", endpoint)
		if err != nil {
			return nil, fmt.Errorf("error while resolve unix addr: %w", err)
		}
		d := net.Dialer{}
		return d.DialContext(ctx, "unix", unixAddr.String())
	}
)

// go:generate mockgen -copyright_file=${GOPATH}/src/icode.baidu.com/baidu/bci2/bci-cni-driver/hack/boilerplate.go.txt -destination=./mock/mock.go -package=mock icode.baidu.com/baidu/bci2/bci-cni-driver/cmd/plugins/bci-cni/ipam Interface
type Interface interface {
	CmdAdd(ctx context.Context, args *skel.CmdArgs, K8sArgs *types.K8sArgs) (*rpc.Response, error)
	CmdCheck(ctx context.Context, args *skel.CmdArgs, K8sArgs *types.K8sArgs) (*rpc.Response, error)
	CmdDel(ctx context.Context, args *skel.CmdArgs, K8sArgs *types.K8sArgs) (*rpc.Response, error)
}

type bciCniIpam struct {
	grpc grpcwrapper.Interface
	rpc  rpcwrapper.Interface
}

func NewBciCniIpam() *bciCniIpam {
	return &bciCniIpam{
		grpc: grpcwrapper.New(),
		rpc:  rpcwrapper.New(),
	}
}

var _ Interface = &bciCniIpam{}

func (ipam *bciCniIpam) CmdAdd(ctx context.Context, args *skel.CmdArgs, K8sArgs *types.K8sArgs) (*rpc.Response, error) {
	ctx, cancel := context.WithTimeout(ctx, rpcTimeout)
	defer cancel()

	conn, err := ipam.grpc.DialContext(ctx, endpoint, grpc.WithInsecure(), grpc.WithContextDialer(dialer))
	if err != nil {
		logger.Errorf(ctx, "failed to connect to ipam server on %v: %v", endpoint, err)
		return nil, err
	}
	defer func() {
		if conn != nil {
			conn.Close()
		}
	}()

	c := ipam.rpc.NewCniBackendClient(conn)

	resp, err := c.AllocateIP(ctx, &rpc.Request{
		K8SPodName:             string(K8sArgs.K8S_POD_NAME),
		K8SPodNamespace:        string(K8sArgs.K8S_POD_NAMESPACE),
		K8SPodInfraContainerID: string(K8sArgs.K8S_POD_INFRA_CONTAINER_ID),
		IfName:                 args.IfName,
		Netns:                  args.Netns,
	})

	if err != nil {
		logger.Errorf(ctx, "failed to allocate ip from cni backend: %v", err)
		return nil, err
	}
	logger.Infof(ctx, "allocate ip response body: %v", resp.String())
	return resp, nil

}

func (ipam *bciCniIpam) CmdDel(ctx context.Context, args *skel.CmdArgs, K8sArgs *types.K8sArgs) (*rpc.Response, error) {
	ctx, cancel := context.WithTimeout(ctx, rpcTimeout)
	defer cancel()

	conn, err := ipam.grpc.DialContext(ctx, endpoint, grpc.WithInsecure(), grpc.WithContextDialer(dialer))
	if err != nil {
		logger.Errorf(ctx, "failed to connect to ipam server on %v: %v", endpoint, err)
		return nil, err
	}
	defer func() {
		if conn != nil {
			conn.Close()
		}
	}()

	c := ipam.rpc.NewCniBackendClient(conn)

	resp, err := c.ReleaseIP(ctx, &rpc.Request{
		K8SPodName:             string(K8sArgs.K8S_POD_NAME),
		K8SPodNamespace:        string(K8sArgs.K8S_POD_NAMESPACE),
		K8SPodInfraContainerID: string(K8sArgs.K8S_POD_INFRA_CONTAINER_ID),
		IfName:                 args.IfName,
		Netns:                  args.Netns,
	})

	if err != nil {
		logger.Errorf(ctx, "failed to release ip from cni backend: %v", err)
		return nil, err
	}
	logger.Infof(ctx, "release ip response body: %v", resp.String())
	return resp, nil
}

func (ipam *bciCniIpam) CmdCheck(ctx context.Context, args *skel.CmdArgs, K8sArgs *types.K8sArgs) (*rpc.Response, error) {
	ctx, cancel := context.WithTimeout(ctx, rpcTimeout)
	defer cancel()

	conn, err := ipam.grpc.DialContext(ctx, endpoint, grpc.WithInsecure(), grpc.WithContextDialer(dialer))
	if err != nil {
		logger.Errorf(ctx, "failed to connect to ipam server on %v: %v", endpoint, err)
		return nil, err
	}
	defer func() {
		if conn != nil {
			conn.Close()
		}
	}()

	c := ipam.rpc.NewCniBackendClient(conn)

	resp, err := c.CheckIP(ctx, &rpc.Request{
		K8SPodName:             string(K8sArgs.K8S_POD_NAME),
		K8SPodNamespace:        string(K8sArgs.K8S_POD_NAMESPACE),
		K8SPodInfraContainerID: string(K8sArgs.K8S_POD_INFRA_CONTAINER_ID),
		IfName:                 args.IfName,
		Netns:                  args.Netns,
	})

	if err != nil {
		logger.Errorf(ctx, "failed to check ip from cni backend: %v", err)
		return nil, err
	}
	logger.Infof(ctx, "check ip response body: %v", resp.String())
	return resp, nil
}
