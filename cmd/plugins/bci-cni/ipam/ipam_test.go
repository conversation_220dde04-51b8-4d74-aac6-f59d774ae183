/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package ipam

import (
	"context"
	"net"
	"reflect"
	"testing"

	"github.com/containernetworking/cni/pkg/skel"
	"github.com/golang/mock/gomock"
	googlerpc "google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/cmd/plugins/bci-cni/types"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/rpc"
	rpcdef "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/rpc"
	mockcb "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/rpc/mock"
	grpcwrapper "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/grpc"
	mockgrpc "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/grpc/mock"
	rpcwrapper "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/rpc"
	mockrpc "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/wrapper/rpc/mock"
)

func setupEnv(t *testing.T) (
	*gomock.Controller,
	*mockgrpc.MockInterface,
	*mockrpc.MockInterface,
) {
	ctrl := gomock.NewController(t)
	grpc := mockgrpc.NewMockInterface(ctrl)
	rpc := mockrpc.NewMockInterface(ctrl)

	return ctrl, grpc, rpc
}

func TestNewBciCniIpam(t *testing.T) {
	tests := []struct {
		name string
		want *bciCniIpam
	}{
		{
			name: "normal",
			want: &bciCniIpam{
				grpc: grpcwrapper.New(),
				rpc:  rpcwrapper.New(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewBciCniIpam(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewBciCniIpam() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_bciCniIpam_CmdAdd(t *testing.T) {
	var (
		resp = &rpcdef.Response{
			IsSuccess: true,
			NetworkInfo: &rpcdef.Response_ENIMultiIP{
				ENIMultiIP: &rpcdef.ENIMultiIPReply{
					IP:       "***********",
					Gateway:  "***************",
					EniNetns: "/proc/1000/ns/net",
					Mac:      "ee:ee:ee:ee:ee:ee",
				},
			},
		}
	)

	type fields struct {
		ctrl *gomock.Controller
		grpc grpcwrapper.Interface
		rpc  rpcwrapper.Interface
	}
	type args struct {
		ctx     context.Context
		args    *skel.CmdArgs
		K8sArgs *types.K8sArgs
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *rpc.Response
		wantErr bool
	}{
		{
			name: "normal process",
			fields: func() fields {
				ctrl, grpc, rpc := setupEnv(t)

				lis, err := net.Listen("tcp", "localhost:0")
				if err != nil {
					t.Fatalf("Error while listening. Err: %v", err)
				}
				cc, err := googlerpc.Dial(lis.Addr().String(), googlerpc.WithTransportCredentials(insecure.NewCredentials()))
				if err != nil {
					t.Fatalf("Error while dialing. Err: %v", err)
				}

				cniBackendClient := mockcb.NewMockCniBackendClient(ctrl)

				gomock.InOrder(
					grpc.EXPECT().DialContext(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(cc, nil),
					rpc.EXPECT().NewCniBackendClient(gomock.Any()).Return(cniBackendClient),
					cniBackendClient.EXPECT().AllocateIP(gomock.Any(), gomock.Any()).Return(resp, nil),
				)

				return fields{
					ctrl: ctrl,
					grpc: grpc,
					rpc:  rpc,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				args: &skel.CmdArgs{
					ContainerID: "a94ab0bf5ca5d352fe5f5cf490bea1e42f560ad742a0d92d0aca0c7e33bdf076",
					Netns:       "/proc/5897/ns/net",
					IfName:      "eth0",
					Path:        "/opt/cni/bin",
				},
				K8sArgs: &types.K8sArgs{
					K8S_POD_NAME:               "default",
					K8S_POD_NAMESPACE:          "busybox",
					K8S_POD_INFRA_CONTAINER_ID: "a94ab0bf5ca5d352fe5f5cf490bea1e42f560ad742a0d92d0aca0c7e33bdf076",
				},
			},
			want:    resp,
			wantErr: false,
		},
		{
			name: "dial server error",
			fields: func() fields {
				ctrl, grpc, rpc := setupEnv(t)

				gomock.InOrder(
					grpc.EXPECT().DialContext(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, googlerpc.ErrClientConnTimeout),
				)

				return fields{
					ctrl: ctrl,
					grpc: grpc,
					rpc:  rpc,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				args: &skel.CmdArgs{
					ContainerID: "a94ab0bf5ca5d352fe5f5cf490bea1e42f560ad742a0d92d0aca0c7e33bdf076",
					Netns:       "/proc/5897/ns/net",
					IfName:      "eth0",
					Path:        "/opt/cni/bin",
				},
				K8sArgs: &types.K8sArgs{
					K8S_POD_NAME:               "default",
					K8S_POD_NAMESPACE:          "busybox",
					K8S_POD_INFRA_CONTAINER_ID: "a94ab0bf5ca5d352fe5f5cf490bea1e42f560ad742a0d92d0aca0c7e33bdf076",
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "rpc server returns error",
			fields: func() fields {
				ctrl, grpc, rpc := setupEnv(t)

				lis, err := net.Listen("tcp", "localhost:0")
				if err != nil {
					t.Fatalf("Error while listening. Err: %v", err)
				}
				cc, err := googlerpc.Dial(lis.Addr().String(), googlerpc.WithTransportCredentials(insecure.NewCredentials()))
				if err != nil {
					t.Fatalf("Error while dialing. Err: %v", err)
				}

				cniBackendClient := mockcb.NewMockCniBackendClient(ctrl)

				gomock.InOrder(
					grpc.EXPECT().DialContext(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(cc, nil),
					rpc.EXPECT().NewCniBackendClient(gomock.Any()).Return(cniBackendClient),
					cniBackendClient.EXPECT().AllocateIP(gomock.Any(), gomock.Any()).Return(nil, googlerpc.ErrServerStopped),
				)

				return fields{
					ctrl: ctrl,
					grpc: grpc,
					rpc:  rpc,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				args: &skel.CmdArgs{
					ContainerID: "a94ab0bf5ca5d352fe5f5cf490bea1e42f560ad742a0d92d0aca0c7e33bdf076",
					Netns:       "/proc/5897/ns/net",
					IfName:      "eth0",
					Path:        "/opt/cni/bin",
				},
				K8sArgs: &types.K8sArgs{
					K8S_POD_NAME:               "default",
					K8S_POD_NAMESPACE:          "busybox",
					K8S_POD_INFRA_CONTAINER_ID: "a94ab0bf5ca5d352fe5f5cf490bea1e42f560ad742a0d92d0aca0c7e33bdf076",
				},
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}

			ipam := &bciCniIpam{
				grpc: tt.fields.grpc,
				rpc:  tt.fields.rpc,
			}

			got, err := ipam.CmdAdd(tt.args.ctx, tt.args.args, tt.args.K8sArgs)
			if (err != nil) != tt.wantErr {
				t.Errorf("bciCniIpam.CmdAdd() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("bciCniIpam.CmdAdd() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_bciCniIpam_CmdDel(t *testing.T) {
	var (
		resp = &rpcdef.Response{
			IsSuccess: true,
			NetworkInfo: &rpcdef.Response_ENIMultiIP{
				ENIMultiIP: &rpcdef.ENIMultiIPReply{
					IP:       "***********",
					Gateway:  "***************",
					EniNetns: "/proc/1000/ns/net",
					Mac:      "ee:ee:ee:ee:ee:ee",
				},
			},
		}
	)

	type fields struct {
		ctrl *gomock.Controller
		grpc grpcwrapper.Interface
		rpc  rpcwrapper.Interface
	}
	type args struct {
		ctx     context.Context
		args    *skel.CmdArgs
		K8sArgs *types.K8sArgs
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *rpc.Response
		wantErr bool
	}{
		{
			name: "normal process",
			fields: func() fields {
				ctrl, grpc, rpc := setupEnv(t)

				lis, err := net.Listen("tcp", "localhost:0")
				if err != nil {
					t.Fatalf("Error while listening. Err: %v", err)
				}
				cc, err := googlerpc.Dial(lis.Addr().String(), googlerpc.WithTransportCredentials(insecure.NewCredentials()))
				if err != nil {
					t.Fatalf("Error while dialing. Err: %v", err)
				}

				cniBackendClient := mockcb.NewMockCniBackendClient(ctrl)

				gomock.InOrder(
					grpc.EXPECT().DialContext(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(cc, nil),
					rpc.EXPECT().NewCniBackendClient(gomock.Any()).Return(cniBackendClient),
					cniBackendClient.EXPECT().ReleaseIP(gomock.Any(), gomock.Any()).Return(resp, nil),
				)

				return fields{
					ctrl: ctrl,
					grpc: grpc,
					rpc:  rpc,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				args: &skel.CmdArgs{
					ContainerID: "a94ab0bf5ca5d352fe5f5cf490bea1e42f560ad742a0d92d0aca0c7e33bdf076",
					Netns:       "/proc/5897/ns/net",
					IfName:      "eth0",
					Path:        "/opt/cni/bin",
				},
				K8sArgs: &types.K8sArgs{
					K8S_POD_NAME:               "default",
					K8S_POD_NAMESPACE:          "busybox",
					K8S_POD_INFRA_CONTAINER_ID: "a94ab0bf5ca5d352fe5f5cf490bea1e42f560ad742a0d92d0aca0c7e33bdf076",
				},
			},
			want:    resp,
			wantErr: false,
		},
		{
			name: "dial server error",
			fields: func() fields {
				ctrl, grpc, rpc := setupEnv(t)

				gomock.InOrder(
					grpc.EXPECT().DialContext(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, googlerpc.ErrClientConnTimeout),
				)

				return fields{
					ctrl: ctrl,
					grpc: grpc,
					rpc:  rpc,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				args: &skel.CmdArgs{
					ContainerID: "a94ab0bf5ca5d352fe5f5cf490bea1e42f560ad742a0d92d0aca0c7e33bdf076",
					Netns:       "/proc/5897/ns/net",
					IfName:      "eth0",
					Path:        "/opt/cni/bin",
				},
				K8sArgs: &types.K8sArgs{
					K8S_POD_NAME:               "default",
					K8S_POD_NAMESPACE:          "busybox",
					K8S_POD_INFRA_CONTAINER_ID: "a94ab0bf5ca5d352fe5f5cf490bea1e42f560ad742a0d92d0aca0c7e33bdf076",
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "rpc server returns error",
			fields: func() fields {
				ctrl, grpc, rpc := setupEnv(t)

				lis, err := net.Listen("tcp", "localhost:0")
				if err != nil {
					t.Fatalf("Error while listening. Err: %v", err)
				}
				cc, err := googlerpc.Dial(lis.Addr().String(), googlerpc.WithTransportCredentials(insecure.NewCredentials()))
				if err != nil {
					t.Fatalf("Error while dialing. Err: %v", err)
				}

				cniBackendClient := mockcb.NewMockCniBackendClient(ctrl)

				gomock.InOrder(
					grpc.EXPECT().DialContext(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(cc, nil),
					rpc.EXPECT().NewCniBackendClient(gomock.Any()).Return(cniBackendClient),
					cniBackendClient.EXPECT().ReleaseIP(gomock.Any(), gomock.Any()).Return(nil, googlerpc.ErrServerStopped),
				)

				return fields{
					ctrl: ctrl,
					grpc: grpc,
					rpc:  rpc,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				args: &skel.CmdArgs{
					ContainerID: "a94ab0bf5ca5d352fe5f5cf490bea1e42f560ad742a0d92d0aca0c7e33bdf076",
					Netns:       "/proc/5897/ns/net",
					IfName:      "eth0",
					Path:        "/opt/cni/bin",
				},
				K8sArgs: &types.K8sArgs{
					K8S_POD_NAME:               "default",
					K8S_POD_NAMESPACE:          "busybox",
					K8S_POD_INFRA_CONTAINER_ID: "a94ab0bf5ca5d352fe5f5cf490bea1e42f560ad742a0d92d0aca0c7e33bdf076",
				},
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}

			ipam := &bciCniIpam{
				grpc: tt.fields.grpc,
				rpc:  tt.fields.rpc,
			}

			got, err := ipam.CmdDel(tt.args.ctx, tt.args.args, tt.args.K8sArgs)
			if (err != nil) != tt.wantErr {
				t.Errorf("bciCniIpam.CmdDel() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("bciCniIpam.CmdDel() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_bciCniIpam_CmdCheck(t *testing.T) {
	var (
		resp = &rpcdef.Response{
			IsSuccess: true,
			NetworkInfo: &rpcdef.Response_ENIMultiIP{
				ENIMultiIP: &rpcdef.ENIMultiIPReply{
					IP:       "***********",
					Gateway:  "***************",
					EniNetns: "/proc/1000/ns/net",
					Mac:      "ee:ee:ee:ee:ee:ee",
				},
			},
		}
	)

	type fields struct {
		ctrl *gomock.Controller
		grpc grpcwrapper.Interface
		rpc  rpcwrapper.Interface
	}
	type args struct {
		ctx     context.Context
		args    *skel.CmdArgs
		K8sArgs *types.K8sArgs
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *rpc.Response
		wantErr bool
	}{
		{
			name: "normal process",
			fields: func() fields {
				ctrl, grpc, rpc := setupEnv(t)

				lis, err := net.Listen("tcp", "localhost:0")
				if err != nil {
					t.Fatalf("Error while listening. Err: %v", err)
				}
				cc, err := googlerpc.Dial(lis.Addr().String(), googlerpc.WithTransportCredentials(insecure.NewCredentials()))
				if err != nil {
					t.Fatalf("Error while dialing. Err: %v", err)
				}

				cniBackendClient := mockcb.NewMockCniBackendClient(ctrl)

				gomock.InOrder(
					grpc.EXPECT().DialContext(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(cc, nil),
					rpc.EXPECT().NewCniBackendClient(gomock.Any()).Return(cniBackendClient),
					cniBackendClient.EXPECT().CheckIP(gomock.Any(), gomock.Any()).Return(resp, nil),
				)

				return fields{
					ctrl: ctrl,
					grpc: grpc,
					rpc:  rpc,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				args: &skel.CmdArgs{
					ContainerID: "a94ab0bf5ca5d352fe5f5cf490bea1e42f560ad742a0d92d0aca0c7e33bdf076",
					Netns:       "/proc/5897/ns/net",
					IfName:      "eth0",
					Path:        "/opt/cni/bin",
				},
				K8sArgs: &types.K8sArgs{
					K8S_POD_NAME:               "default",
					K8S_POD_NAMESPACE:          "busybox",
					K8S_POD_INFRA_CONTAINER_ID: "a94ab0bf5ca5d352fe5f5cf490bea1e42f560ad742a0d92d0aca0c7e33bdf076",
				},
			},
			want:    resp,
			wantErr: false,
		},
		{
			name: "dial server error",
			fields: func() fields {
				ctrl, grpc, rpc := setupEnv(t)

				gomock.InOrder(
					grpc.EXPECT().DialContext(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, googlerpc.ErrClientConnTimeout),
				)

				return fields{
					ctrl: ctrl,
					grpc: grpc,
					rpc:  rpc,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				args: &skel.CmdArgs{
					ContainerID: "a94ab0bf5ca5d352fe5f5cf490bea1e42f560ad742a0d92d0aca0c7e33bdf076",
					Netns:       "/proc/5897/ns/net",
					IfName:      "eth0",
					Path:        "/opt/cni/bin",
				},
				K8sArgs: &types.K8sArgs{
					K8S_POD_NAME:               "default",
					K8S_POD_NAMESPACE:          "busybox",
					K8S_POD_INFRA_CONTAINER_ID: "a94ab0bf5ca5d352fe5f5cf490bea1e42f560ad742a0d92d0aca0c7e33bdf076",
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "rpc server returns error",
			fields: func() fields {
				ctrl, grpc, rpc := setupEnv(t)

				lis, err := net.Listen("tcp", "localhost:0")
				if err != nil {
					t.Fatalf("Error while listening. Err: %v", err)
				}
				cc, err := googlerpc.Dial(lis.Addr().String(), googlerpc.WithTransportCredentials(insecure.NewCredentials()))
				if err != nil {
					t.Fatalf("Error while dialing. Err: %v", err)
				}

				cniBackendClient := mockcb.NewMockCniBackendClient(ctrl)

				gomock.InOrder(
					grpc.EXPECT().DialContext(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(cc, nil),
					rpc.EXPECT().NewCniBackendClient(gomock.Any()).Return(cniBackendClient),
					cniBackendClient.EXPECT().CheckIP(gomock.Any(), gomock.Any()).Return(nil, googlerpc.ErrServerStopped),
				)

				return fields{
					ctrl: ctrl,
					grpc: grpc,
					rpc:  rpc,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				args: &skel.CmdArgs{
					ContainerID: "a94ab0bf5ca5d352fe5f5cf490bea1e42f560ad742a0d92d0aca0c7e33bdf076",
					Netns:       "/proc/5897/ns/net",
					IfName:      "eth0",
					Path:        "/opt/cni/bin",
				},
				K8sArgs: &types.K8sArgs{
					K8S_POD_NAME:               "default",
					K8S_POD_NAMESPACE:          "busybox",
					K8S_POD_INFRA_CONTAINER_ID: "a94ab0bf5ca5d352fe5f5cf490bea1e42f560ad742a0d92d0aca0c7e33bdf076",
				},
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}

			ipam := &bciCniIpam{
				grpc: tt.fields.grpc,
				rpc:  tt.fields.rpc,
			}

			got, err := ipam.CmdCheck(tt.args.ctx, tt.args.args, tt.args.K8sArgs)
			if (err != nil) != tt.wantErr {
				t.Errorf("bciCniIpam.CmdCheck() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("bciCniIpam.CmdCheck() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_dialer(t *testing.T) {
	type args struct {
		ctx context.Context
		s   string
	}
	tests := []struct {
		name    string
		args    args
		want    net.Conn
		wantErr bool
	}{
		{
			args: args{
				ctx: context.TODO(),
				s:   endpoint,
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := dialer(tt.args.ctx, tt.args.s)
			if (err != nil) != tt.wantErr {
				t.Errorf("dialer() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("dialer() = %v, want %v", got, tt.want)
			}
		})
	}
}
