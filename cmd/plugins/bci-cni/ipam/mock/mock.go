// /*
// Copyright 2022 Baidu, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
// */

// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/bci2/bci-cni-driver/cmd/plugins/bci-cni/ipam (interfaces: Interface)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	skel "github.com/containernetworking/cni/pkg/skel"
	gomock "github.com/golang/mock/gomock"
	types "icode.baidu.com/baidu/bci2/bci-cni-driver/cmd/plugins/bci-cni/types"
	rpc "icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/rpc"
)

// MockInterface is a mock of Interface interface.
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface.
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance.
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// CmdAdd mocks base method.
func (m *MockInterface) CmdAdd(arg0 context.Context, arg1 *skel.CmdArgs, arg2 *types.K8sArgs) (*rpc.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CmdAdd", arg0, arg1, arg2)
	ret0, _ := ret[0].(*rpc.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CmdAdd indicates an expected call of CmdAdd.
func (mr *MockInterfaceMockRecorder) CmdAdd(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CmdAdd", reflect.TypeOf((*MockInterface)(nil).CmdAdd), arg0, arg1, arg2)
}

// CmdCheck mocks base method.
func (m *MockInterface) CmdCheck(arg0 context.Context, arg1 *skel.CmdArgs, arg2 *types.K8sArgs) (*rpc.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CmdCheck", arg0, arg1, arg2)
	ret0, _ := ret[0].(*rpc.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CmdCheck indicates an expected call of CmdCheck.
func (mr *MockInterfaceMockRecorder) CmdCheck(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CmdCheck", reflect.TypeOf((*MockInterface)(nil).CmdCheck), arg0, arg1, arg2)
}

// CmdDel mocks base method.
func (m *MockInterface) CmdDel(arg0 context.Context, arg1 *skel.CmdArgs, arg2 *types.K8sArgs) (*rpc.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CmdDel", arg0, arg1, arg2)
	ret0, _ := ret[0].(*rpc.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CmdDel indicates an expected call of CmdDel.
func (mr *MockInterfaceMockRecorder) CmdDel(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CmdDel", reflect.TypeOf((*MockInterface)(nil).CmdDel), arg0, arg1, arg2)
}
