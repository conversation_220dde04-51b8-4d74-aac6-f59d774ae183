/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package ipam

import (
	"context"

	"github.com/containernetworking/cni/pkg/skel"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/cmd/plugins/bci-cni/types"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/rpc"
)

var (
	IPAddress  = "***********"
	MacAddress = "ea:ea:ea:ea:ea:ea"
	Gateway    = "***************"
	// EniNetns   = "/var/run/netns/bci-cni"
	EniNetns = "/proc/1/ns/net"
)

// fakeIpam is used for test
type fakeIpam struct {
}

func NewFakeIpam() Interface {
	return &fakeIpam{}
}

var _ Interface = &fakeIpam{}

func (ipam *fakeIpam) CmdAdd(ctx context.Context, args *skel.CmdArgs, K8sArgs *types.K8sArgs) (*rpc.Response, error) {
	return &rpc.Response{
		IsSuccess: true,
		NetworkInfo: &rpc.Response_ENIMultiIP{
			ENIMultiIP: &rpc.ENIMultiIPReply{
				IP:       IPAddress,
				Mac:      MacAddress,
				Gateway:  Gateway,
				EniNetns: EniNetns,
			},
		},
	}, nil
}

func (ipam *fakeIpam) CmdDel(ctx context.Context, args *skel.CmdArgs, K8sArgs *types.K8sArgs) (*rpc.Response, error) {
	return &rpc.Response{
		IsSuccess: true,
		NetworkInfo: &rpc.Response_ENIMultiIP{
			ENIMultiIP: &rpc.ENIMultiIPReply{
				IP:       IPAddress,
				Mac:      MacAddress,
				Gateway:  Gateway,
				EniNetns: EniNetns,
			},
		},
	}, nil
}

func (ipam *fakeIpam) CmdCheck(ctx context.Context, args *skel.CmdArgs, K8sArgs *types.K8sArgs) (*rpc.Response, error) {
	return &rpc.Response{
		IsSuccess: true,
		NetworkInfo: &rpc.Response_ENIMultiIP{
			ENIMultiIP: &rpc.ENIMultiIPReply{
				IP:       IPAddress,
				Mac:      MacAddress,
				Gateway:  Gateway,
				EniNetns: EniNetns,
			},
		},
	}, nil
}
