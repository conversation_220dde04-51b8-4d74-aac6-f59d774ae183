/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package ipam

import (
	"context"
	"reflect"
	"testing"

	"github.com/containernetworking/cni/pkg/skel"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/cmd/plugins/bci-cni/types"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/rpc"
)

func Test_fakeIpam_CmdAdd(t *testing.T) {
	type args struct {
		ctx     context.Context
		args    *skel.CmdArgs
		K8sArgs *types.K8sArgs
	}
	tests := []struct {
		name    string
		ipam    *fakeIpam
		args    args
		want    *rpc.Response
		wantErr bool
	}{
		{
			name: "normal",
			ipam: &fakeIpam{},
			args: args{
				ctx: context.TODO(),
				args: &skel.CmdArgs{
					ContainerID: "a94ab0bf5ca5d352fe5f5cf490bea1e42f560ad742a0d92d0aca0c7e33bdf076",
					Netns:       "/proc/5897/ns/net",
					IfName:      "eth0",
					Path:        "/opt/cni/bin",
				},
				K8sArgs: &types.K8sArgs{
					K8S_POD_NAME:               "default",
					K8S_POD_NAMESPACE:          "busybox",
					K8S_POD_INFRA_CONTAINER_ID: "a94ab0bf5ca5d352fe5f5cf490bea1e42f560ad742a0d92d0aca0c7e33bdf076",
				},
			},
			want: &rpc.Response{
				IsSuccess: true,
				NetworkInfo: &rpc.Response_ENIMultiIP{
					ENIMultiIP: &rpc.ENIMultiIPReply{
						IP:       IPAddress,
						Mac:      MacAddress,
						Gateway:  Gateway,
						EniNetns: EniNetns,
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ipam := &fakeIpam{}
			got, err := ipam.CmdAdd(tt.args.ctx, tt.args.args, tt.args.K8sArgs)
			if (err != nil) != tt.wantErr {
				t.Errorf("fakeIpam.CmdAdd() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("fakeIpam.CmdAdd() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_fakeIpam_CmdDel(t *testing.T) {
	type args struct {
		ctx     context.Context
		args    *skel.CmdArgs
		K8sArgs *types.K8sArgs
	}
	tests := []struct {
		name    string
		ipam    *fakeIpam
		args    args
		want    *rpc.Response
		wantErr bool
	}{
		{
			name: "normal",
			ipam: &fakeIpam{},
			args: args{
				ctx: context.TODO(),
				args: &skel.CmdArgs{
					ContainerID: "a94ab0bf5ca5d352fe5f5cf490bea1e42f560ad742a0d92d0aca0c7e33bdf076",
					Netns:       "/proc/5897/ns/net",
					IfName:      "eth0",
					Path:        "/opt/cni/bin",
				},
				K8sArgs: &types.K8sArgs{
					K8S_POD_NAME:               "default",
					K8S_POD_NAMESPACE:          "busybox",
					K8S_POD_INFRA_CONTAINER_ID: "a94ab0bf5ca5d352fe5f5cf490bea1e42f560ad742a0d92d0aca0c7e33bdf076",
				},
			},
			want: &rpc.Response{
				IsSuccess: true,
				NetworkInfo: &rpc.Response_ENIMultiIP{
					ENIMultiIP: &rpc.ENIMultiIPReply{
						IP:       IPAddress,
						Mac:      MacAddress,
						Gateway:  Gateway,
						EniNetns: EniNetns,
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ipam := &fakeIpam{}
			got, err := ipam.CmdDel(tt.args.ctx, tt.args.args, tt.args.K8sArgs)
			if (err != nil) != tt.wantErr {
				t.Errorf("fakeIpam.CmdDel() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("fakeIpam.CmdDel() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_fakeIpam_CmdCheck(t *testing.T) {
	type args struct {
		ctx     context.Context
		args    *skel.CmdArgs
		K8sArgs *types.K8sArgs
	}
	tests := []struct {
		name    string
		ipam    *fakeIpam
		args    args
		want    *rpc.Response
		wantErr bool
	}{
		{
			name: "normal",
			ipam: &fakeIpam{},
			args: args{
				ctx: context.TODO(),
				args: &skel.CmdArgs{
					ContainerID: "a94ab0bf5ca5d352fe5f5cf490bea1e42f560ad742a0d92d0aca0c7e33bdf076",
					Netns:       "/proc/5897/ns/net",
					IfName:      "eth0",
					Path:        "/opt/cni/bin",
				},
				K8sArgs: &types.K8sArgs{
					K8S_POD_NAME:               "default",
					K8S_POD_NAMESPACE:          "busybox",
					K8S_POD_INFRA_CONTAINER_ID: "a94ab0bf5ca5d352fe5f5cf490bea1e42f560ad742a0d92d0aca0c7e33bdf076",
				},
			},
			want: &rpc.Response{
				IsSuccess: true,
				NetworkInfo: &rpc.Response_ENIMultiIP{
					ENIMultiIP: &rpc.ENIMultiIPReply{
						IP:       IPAddress,
						Mac:      MacAddress,
						Gateway:  Gateway,
						EniNetns: EniNetns,
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ipam := &fakeIpam{}
			got, err := ipam.CmdCheck(tt.args.ctx, tt.args.args, tt.args.K8sArgs)
			if (err != nil) != tt.wantErr {
				t.Errorf("fakeIpam.CmdCheck() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("fakeIpam.CmdCheck() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestNewFakeIpam(t *testing.T) {
	tests := []struct {
		name string
		want Interface
	}{
		{
			name: "normal",
			want: &fakeIpam{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewFakeIpam(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewFakeIpam() = %v, want %v", got, tt.want)
			}
		})
	}
}
