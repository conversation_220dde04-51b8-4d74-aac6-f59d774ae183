/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package main

import (
	"context"
	"flag"
	"fmt"
	"log"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/clientcmd"
)

var (
	kubeconfig *string
	nodeName   *string
)

func parse() {
	kubeconfig = flag.String("kubeconfig", "/root/.kube/config", "location of your kubeconfig file")
	nodeName = flag.String("node", "", "node name")
	flag.Parse()
}

func main() {
	parse()

	clientset, err := createK8sClient()
	if err != nil {
		log.Fatal(err)
	}
	if err := patchNetworkCondition(clientset, *nodeName); err != nil {
		log.Fatal(err)
	}
}

func createK8sClient() (kubernetes.Interface, error) {
	config, err := clientcmd.BuildConfigFromFlags("", *kubeconfig)
	if err != nil {
		return nil, err
	}
	return kubernetes.NewForConfig(config)
}

func patchNetworkCondition(client kubernetes.Interface, nodeName string) error {
	node, err := client.CoreV1().Nodes().Get(context.TODO(), nodeName, metav1.GetOptions{})
	if err != nil {
		return err
	}

	for i := range node.Status.Conditions {
		if node.Status.Conditions[i].Type == corev1.NodeNetworkUnavailable {
			node.Status.Conditions[i].Status = corev1.ConditionFalse
			node.Status.Conditions[i].Reason = "BciCniReady"
			node.Status.Conditions[i].Message = "Bci cni is running on this node"

			_, err := client.CoreV1().Nodes().UpdateStatus(context.TODO(), node, metav1.UpdateOptions{})
			if err != nil {
				return fmt.Errorf("failed to update node %s: %w", node.Name, err)
			}

			log.Printf("Updated node %s\n", node.Name)
		}
	}

	return nil
}
