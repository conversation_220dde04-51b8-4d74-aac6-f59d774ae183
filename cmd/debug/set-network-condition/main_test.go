/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package main

import (
	"context"
	"reflect"
	"testing"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/kubernetes/fake"
)

func Test_parse(t *testing.T) {
	tests := []struct {
		name string
	}{
		{
			name: "normal",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			parse()
		})
	}
}

func Test_patchNetworkCondition(t *testing.T) {
	fakeClient := fake.NewSimpleClientset()
	_, _ = fakeClient.CoreV1().Nodes().Create(context.TODO(), &corev1.Node{
		TypeMeta: metav1.TypeMeta{
			Kind:       "",
			APIVersion: "",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:            "worker",
			Annotations:     map[string]string{},
			OwnerReferences: []metav1.OwnerReference{},
			Finalizers:      []string{},
			ClusterName:     "",
			ManagedFields:   []metav1.ManagedFieldsEntry{},
		},
		Spec: corev1.NodeSpec{
			PodCIDR:            "",
			PodCIDRs:           []string{},
			ProviderID:         "",
			Unschedulable:      false,
			Taints:             []corev1.Taint{},
			ConfigSource:       &corev1.NodeConfigSource{},
			DoNotUseExternalID: "",
		},
		Status: corev1.NodeStatus{
			Capacity:    map[corev1.ResourceName]resource.Quantity{},
			Allocatable: map[corev1.ResourceName]resource.Quantity{},
			Phase:       "",
			Conditions: []corev1.NodeCondition{
				{
					Type:               corev1.NodeNetworkUnavailable,
					Status:             "",
					LastHeartbeatTime:  metav1.Time{},
					LastTransitionTime: metav1.Time{},
					Reason:             "",
					Message:            "",
				},
			},
		},
	}, metav1.CreateOptions{})

	type args struct {
		client   kubernetes.Interface
		nodeName string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "not found",
			args: args{
				client:   fakeClient,
				nodeName: "",
			},
			wantErr: true,
		},
		{
			name: "normal",
			args: args{
				client:   fakeClient,
				nodeName: "worker",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := patchNetworkCondition(tt.args.client, tt.args.nodeName); (err != nil) != tt.wantErr {
				t.Errorf("patchNetworkCondition() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_createK8sClient(t *testing.T) {
	tests := []struct {
		name    string
		want    kubernetes.Interface
		wantErr bool
	}{
		{
			name:    "normal case",
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := createK8sClient()
			if (err != nil) != tt.wantErr {
				t.Errorf("createK8sClient() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("createK8sClient() = %v, want %v", got, tt.want)
			}
		})
	}
}
