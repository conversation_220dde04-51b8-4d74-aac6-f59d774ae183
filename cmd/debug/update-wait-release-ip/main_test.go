/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package main

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	corev1 "k8s.io/api/core/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"

	networkingv1 "icode.baidu.com/baidu/bci2/bci-cni-driver/apis/networking/v1"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/nodeagent/ipam/mock"
)

func setupEnv(t *testing.T) (
	*gomock.Controller,
	*mock.MockRuntimeCache,
	*mock.MockRuntimeClient,
) {
	ctrl := gomock.NewController(t)

	cache := mock.NewMockRuntimeCache(ctrl)
	client := mock.NewMockRuntimeClient(ctrl)

	return ctrl, cache, client
}

func Test_updateWaitReleaseIP(t *testing.T) {
	type args struct {
		ctrl   *gomock.Controller
		ctx    context.Context
		client client.Client
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "normal process",
			args: func() args {
				ctrl, _, client := setupEnv(t)

				statusW := mock.NewMockRuntimeClientStatusWriter(ctrl)

				gomock.InOrder(
					client.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).SetArg(2, corev1.Node{}).Return(nil),
					client.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).SetArg(2, networkingv1.BciNode{}).Return(nil),
					client.EXPECT().Status().Return(statusW),
					statusW.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil),
				)

				return args{
					ctrl:   ctrl,
					ctx:    context.TODO(),
					client: client,
				}
			}(),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := updateWaitReleaseIP(tt.args.ctx, tt.args.client); (err != nil) != tt.wantErr {
				t.Errorf("updateWaitReleaseIP() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_main(t *testing.T) {
	tests := []struct {
		name string
	}{
		{},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			main()
		})
	}
}
