/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package main

import (
	"context"
	"flag"
	"fmt"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	utilruntime "k8s.io/apimachinery/pkg/util/runtime"
	clientgoscheme "k8s.io/client-go/kubernetes/scheme"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"

	networkingv1 "icode.baidu.com/baidu/bci2/bci-cni-driver/apis/networking/v1"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/pkg/logger"
)

var (
	nodeName = "************"
)

var (
	scheme = runtime.NewScheme()
)

func main() {
	var (
		ctx = logger.NewContext()
	)

	logger.InitFlags(nil)
	flag.Parse()

	restConfig := ctrl.GetConfigOrDie()

	utilruntime.Must(clientgoscheme.AddToScheme(scheme))
	utilruntime.Must(networkingv1.AddToScheme(scheme))
	client, err := client.New(restConfig, client.Options{Scheme: scheme})
	if err != nil {
		logger.Errorf(ctx, "create client failed: %v", err)
		return
	}

	err = updateWaitReleaseIP(ctx, client)
	if err != nil {
		logger.Error(ctx, err)
	}
}

func updateWaitReleaseIP(ctx context.Context, client client.Client) error {
	var (
		node    corev1.Node
		bciNode networkingv1.BciNode
	)

	err := client.Get(ctx, types.NamespacedName{
		Name: nodeName,
	}, &node)

	if err != nil {
		return fmt.Errorf("get node failed: %w", err)
	}

	logger.Infof(ctx, "node info: %v", node.Status.NodeInfo)

	err = client.Get(ctx, types.NamespacedName{
		Name: nodeName,
	}, &bciNode)

	if err != nil {
		return fmt.Errorf("get bcinode failed: %w", err)
	}

	bciNode.Status.WaitReleaseIP = map[string]map[string]networkingv1.IPReleaseStatus{
		"cf0d393a25bb428ea346af989ec17239:eni-nua30mjjf4z0": map[string]networkingv1.IPReleaseStatus{
			"**************": "marked-for-release",
			"**************": "marked-for-release",
		},
	}

	err = client.Status().Update(ctx, &bciNode)

	if err != nil {
		return fmt.Errorf("update bcinode failed: %w", err)
	}

	return nil
}
