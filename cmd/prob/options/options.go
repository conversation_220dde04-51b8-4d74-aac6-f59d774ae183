package options

import (
	"flag"
)

type Options struct {
	ProbType string
	Host     string
	Port     int
	Path     string
	Scheme   string
	Timeout  int
	Header   string
}

func ParseArgs() *Options {
	var (
		o Options
	)

	flag.StringVar(&o.ProbType, "probType", "", "httpGet or tcpSocket")
	flag.StringVar(&o.Host, "host", "", "host ip address")
	flag.IntVar(&o.Port, "port", 0, "port address")
	flag.StringVar(&o.Path, "path", "", "http path")
	flag.StringVar(&o.Scheme, "scheme", "", "https or http")
	flag.IntVar(&o.Timeout, "timeout", 1, "prob execute timeout seconds")
	flag.StringVar(&o.Header, "header", "", "http header: name1,value1|name2,value2")

	flag.Parse()

	return &o
}
