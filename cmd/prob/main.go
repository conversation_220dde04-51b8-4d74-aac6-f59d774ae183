/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
/*
code for bci pod prob. This code running in user container, responsible for httpGet and
tcp socket prob.
*/

package main

import (
	"fmt"
	"os"
	"time"

	"icode.baidu.com/baidu/bci2/bci-cni-driver/cmd/prob/http"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/cmd/prob/options"
	"icode.baidu.com/baidu/bci2/bci-cni-driver/cmd/prob/tcp"
)

func main() {
	opts := options.ParseArgs()
	timeout := time.Duration(opts.Timeout) * time.Second
	var result bool
	var output string

	hostip := opts.Host
	if opts.Host == "" {
		hostip = os.Getenv("ENV_BCI_PROB_PODIP")
	}

	if opts.ProbType == "httpGet" {
		httpProb := http.New(false)
		url := http.FormatURL(opts.Scheme, hostip, opts.Port, opts.Path)
		headers := http.BuildHeader(opts.Header)
		result, output, _ = httpProb.Probe(url, headers, timeout)
	}

	if opts.ProbType == "tcpSocket" {
		tcpProb := tcp.New()
		result, output, _ = tcpProb.Probe(hostip, opts.Port, timeout)
	}

	if result {
		os.Exit(0)
	} else {
		fmt.Println(output)
		os.Exit(1)
	}
}
