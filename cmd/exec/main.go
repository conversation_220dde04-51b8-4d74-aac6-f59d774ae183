/*
Copyright 2022 Baidu, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"os/exec"
	"strings"
	"time"
)

var (
	timeout int
	command string
)

func init() {
	flag.IntVar(&timeout, "timeout", 30, "Command execution timeout in seconds")
	flag.StringVar(&command, "command", "", "Command to execute")
}

// 检查是否是nc命令
func isNCCommand(cmd string) bool {
	parts := strings.Fields(cmd)
	return len(parts) > 0 && (parts[0] == "nc" || parts[0] == "netcat")
}

// 检查是否是带 -c 参数的命令（bash -c, python3 -c, sh -c等）
func isInlineCommand(cmd string) bool {
	parts := strings.Fields(cmd)
	if len(parts) < 3 {
		return false
	}

	// 支持的带 -c 参数的命令
	supportedCommands := []string{"bash", "sh", "zsh", "python", "python3", "python2"}

	for _, supportedCmd := range supportedCommands {
		if parts[0] == supportedCmd && parts[1] == "-c" {
			return true
		}
	}

	return false
}

// 执行nc命令
func executeNCCommand(ctx context.Context, cmd string, timeout time.Duration) (bool, string, error) {
	parts := strings.Fields(cmd)
	if len(parts) < 2 {
		return false, "nc command requires at least one argument", fmt.Errorf("invalid nc command")
	}

	// 检查是否是nc -z命令（端口扫描）
	isPortScan := false
	for _, part := range parts[1:] {
		if part == "-z" {
			isPortScan = true
			break
		}
	}

	// 构建nc命令
	ncCmd := exec.CommandContext(ctx, parts[0], parts[1:]...)
	output, err := ncCmd.CombinedOutput()

	// 处理超时
	if ctx.Err() == context.DeadlineExceeded {
		return false, fmt.Sprintf("NC command timed out after %v", timeout), nil
	}

	// 对于nc -z命令的特殊处理
	if isPortScan {
		if err != nil {
			// nc -z 失败时，通常表示端口不可达
			return false, fmt.Sprintf("Port scan failed: %s", string(output)), nil
		}
		// nc -z 成功时，表示端口可达
		if len(output) == 0 {
			return true, "Port is reachable", nil
		}
		return true, string(output), nil
	}

	// 普通nc命令处理
	if err != nil {
		return false, fmt.Sprintf("NC command failed: %v, output: %s", err, output), err
	}

	return true, string(output), nil
}

// parseQuotedString 解析带引号的字符串，支持单引号、双引号和无引号的情况
func parseQuotedString(s string) string {
	s = strings.TrimSpace(s)
	if len(s) == 0 {
		return s
	}

	// 检查是否被双引号包围
	if len(s) >= 2 && s[0] == '"' && s[len(s)-1] == '"' {
		return s[1 : len(s)-1]
	}

	// 检查是否被单引号包围
	if len(s) >= 2 && s[0] == '\'' && s[len(s)-1] == '\'' {
		return s[1 : len(s)-1]
	}

	// 没有引号，直接返回
	return s
}

// 执行内联命令（bash -c, python3 -c等）
func executeInlineCommand(ctx context.Context, cmd string, timeout time.Duration) (bool, string, error) {
	// 使用简单的字符串分割来找到程序和-c参数
	cmd = strings.TrimSpace(cmd)

	// 找到第一个空格（程序名之后）
	firstSpaceIdx := strings.Index(cmd, " ")
	if firstSpaceIdx == -1 {
		return false, "invalid inline command format", fmt.Errorf("invalid inline command")
	}

	program := cmd[:firstSpaceIdx]
	remaining := strings.TrimSpace(cmd[firstSpaceIdx+1:])

	// 检查是否以 -c 开头
	if !strings.HasPrefix(remaining, "-c ") {
		return false, "inline command must have -c flag", fmt.Errorf("invalid inline command format")
	}

	// 提取 -c 后面的所有内容，并去除外层引号
	codepart := strings.TrimSpace(remaining[3:])
	codepart = parseQuotedString(codepart)

	// 直接执行命令，避免shell包装
	execCmd := exec.CommandContext(ctx, program, "-c", codepart)
	output, err := execCmd.CombinedOutput()

	// 处理超时
	if ctx.Err() == context.DeadlineExceeded {
		return false, fmt.Sprintf("Inline command timed out after %v", timeout), nil
	}

	if err != nil {
		return false, fmt.Sprintf("Inline command failed: %v, output: %s", err, output), err
	}

	return true, string(output), nil
}

// 执行普通命令
func executeCommand(ctx context.Context, cmd string, timeout time.Duration) (bool, string, error) {
	execCmd := exec.CommandContext(ctx, "sh", "-c", cmd)
	output, err := execCmd.CombinedOutput()
	if err != nil {
		if ctx.Err() == context.DeadlineExceeded {
			return false, fmt.Sprintf("Command timed out after %v", timeout), nil
		}
		return false, fmt.Sprintf("Command failed: %v, output: %s", err, output), err
	}

	return true, string(output), nil
}

func main() {
	flag.Parse()

	if command == "" {
		fmt.Println("Error: command is required")
		os.Exit(1)
	}

	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(timeout)*time.Second)
	defer cancel()

	var result bool
	var output string
	var err error

	// 根据命令类型选择执行方式
	if isNCCommand(command) {
		result, output, err = executeNCCommand(ctx, command, time.Duration(timeout)*time.Second)
	} else if isInlineCommand(command) {
		result, output, err = executeInlineCommand(ctx, command, time.Duration(timeout)*time.Second)
	} else {
		result, output, err = executeCommand(ctx, command, time.Duration(timeout)*time.Second)
	}

	if err != nil {
		fmt.Printf("Error: %v\n", err)
		os.Exit(1)
	}

	if !result {
		fmt.Println(output)
		os.Exit(1)
	}

	fmt.Print(output)
	os.Exit(0)
}
