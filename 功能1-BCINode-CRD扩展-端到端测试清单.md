# 功能1-BCINode-CRD扩展-端到端测试清单

## 功能概述
BCINode CRD扩展功能为现有的BCINode CRD增加了IPv6地址池支持，通过在AllocationEni结构中新增PrivateIPv6Addresses字段来实现双栈IP地址管理。本文档定义了该功能的端到端测试场景。

## 测试范围
- **修改文件**: `bci-cni-driver/apis/networking/v1/bcinode_types.go`
- **核心变更**: 在AllocationEni结构中增加PrivateIPv6Addresses字段
- **测试目标**: 验证CRD扩展在真实Kubernetes环境中的功能正确性

## 端到端测试清单

### 1. CRD部署和升级测试
**测试类别**: 基础设施验证
**测试目标**: 验证CRD在Kubernetes集群中的正确部署和升级

#### 1.1 CRD新版本部署测试
- **测试场景**: E2E-CRD-Deploy-001
- **测试步骤**:
  1. 在测试集群中部署新版本的BCINode CRD
  2. 验证CRD定义被正确注册到Kubernetes API Server
  3. 验证CRD包含新的PrivateIPv6Addresses字段
  4. 验证CRD的OpenAPI Schema正确生成
- **验证标准**:
  - `kubectl get crd bcinodes.networking.bci.cloud.baidu.com` 成功
  - CRD schema中包含privateIPv6Addresses字段
  - 字段类型为object，additionalProperties为AllocationIP类型
- **期望结果**: CRD部署成功，字段定义正确

#### 1.2 CRD升级兼容性测试
- **测试场景**: E2E-CRD-Upgrade-001
- **测试步骤**:
  1. 在集群中部署旧版本的BCINode CRD
  2. 创建使用旧版本CRD的BciNode对象
  3. 升级到新版本的BCINode CRD
  4. 验证现有对象继续正常工作
  5. 验证新对象可以使用IPv6字段
- **验证标准**:
  - 现有BciNode对象状态保持不变
  - 现有对象的IPv4功能正常
  - 新对象可以包含IPv6地址字段
- **期望结果**: 升级过程平滑，向后兼容性良好

### 2. CRD对象操作测试
**测试类别**: 对象生命周期验证
**测试目标**: 验证BciNode对象的创建、更新、删除操作

#### 2.1 包含IPv6地址的BciNode对象创建测试
- **测试场景**: E2E-CRD-Create-001
- **测试步骤**:
  1. 创建包含IPv6地址池的BciNode对象
  2. 验证对象成功创建到etcd
  3. 验证IPv6地址字段正确存储
  4. 验证对象状态正确反映
- **测试数据**:
  ```yaml
  apiVersion: networking.bci.cloud.baidu.com/v1
  kind: BciNode
  metadata:
    name: test-node-ipv6
  spec:
    instanceId: "i-test-001"
    instanceType: "bcc"
    eniMultiIP:
      pool:
        user-001:
          eni-001:
            privateIPv6Addresses:
              "2001:db8::100":
                userID: "user-001"
                eniID: "eni-001"
  ```
- **验证标准**:
  - `kubectl get bcinode test-node-ipv6` 成功
  - 对象的IPv6地址字段正确存储
  - 对象的spec和status字段结构正确
- **期望结果**: 对象创建成功，数据完整性保持

#### 2.2 BciNode对象更新测试
- **测试场景**: E2E-CRD-Update-001
- **测试步骤**:
  1. 创建包含IPv4地址的BciNode对象
  2. 更新对象添加IPv6地址池
  3. 验证更新操作成功
  4. 验证IPv4和IPv6地址同时存在
- **验证标准**:
  - 对象更新操作成功
  - 原有IPv4地址保持不变
  - 新增IPv6地址正确添加
  - 对象版本号正确递增
- **期望结果**: 更新操作成功，数据一致性保持

#### 2.3 BciNode对象删除测试
- **测试场景**: E2E-CRD-Delete-001
- **测试步骤**:
  1. 创建包含IPv6地址的BciNode对象
  2. 删除BciNode对象
  3. 验证对象被正确删除
  4. 验证相关资源被正确清理
- **验证标准**:
  - 对象删除操作成功
  - `kubectl get bcinode` 中不再显示已删除对象
  - 相关的finalizer被正确处理
- **期望结果**: 对象删除成功，资源清理完整

### 3. 集群集成测试
**测试类别**: 系统集成验证
**测试目标**: 验证CRD与其他组件的集成正确性

#### 3.1 Controller集成测试
- **测试场景**: E2E-Controller-Integration-001
- **测试步骤**:
  1. 部署bci-cni-driver的network-controller和node-agent
  2. 创建包含IPv6地址池的BciNode对象
  3. 验证controller能正确处理IPv6字段
  4. 验证controller的reconcile循环正常工作
- **验证标准**:
  - Controller能正确读取IPv6地址字段
  - Controller日志中没有IPv6相关错误
  - Controller能正确更新BciNode状态
- **期望结果**: Controller与新CRD完全兼容

#### 3.2 IPAM集成测试
- **测试场景**: E2E-IPAM-Integration-001
- **测试步骤**:
  1. 部署完整的bci-cni-driver组件
  2. 创建包含IPv6地址池的BciNode对象
  3. 验证IPAM组件能正确处理IPv6地址
  4. 验证IPv6地址分配逻辑正常工作
- **验证标准**:
  - IPAM能正确读取IPv6地址池
  - IPv6地址分配状态正确更新到CRD
  - 没有IPv6相关的异常或错误
- **期望结果**: IPAM与新CRD完全兼容

### 4. 多集群兼容性测试
**测试类别**: 多环境兼容性验证
**测试目标**: 验证CRD在不同Kubernetes版本中的兼容性

#### 4.1 不同Kubernetes版本测试
- **测试场景**: E2E-K8s-Compatibility-001
- **测试范围**:
  - Kubernetes 1.22+
  - Kubernetes 1.24+
  - Kubernetes 1.26+
- **测试步骤**:
  1. 在不同版本的Kubernetes集群中部署CRD
  2. 验证CRD在各版本中的正确性
  3. 验证API兼容性
  4. 验证功能一致性
- **验证标准**:
  - 所有版本中CRD部署成功
  - 功能表现一致
  - 没有版本相关的错误或警告
- **期望结果**: 跨版本兼容性良好

#### 4.2 不同云环境测试
- **测试场景**: E2E-Cloud-Environment-001
- **测试范围**:
  - 百度智能云BCE环境
  - 测试环境
  - 开发环境
- **测试步骤**:
  1. 在不同云环境中部署CRD
  2. 验证CRD在各环境中的正确性
  3. 验证网络环境的兼容性
  4. 验证权限和访问控制
- **验证标准**:
  - 所有环境中CRD功能正常
  - 网络配置正确
  - 权限控制有效
- **期望结果**: 多环境兼容性良好

### 5. 性能和稳定性测试
**测试类别**: 性能和稳定性验证
**测试目标**: 验证CRD在大规模场景下的性能和稳定性

#### 5.1 大规模BciNode对象测试
- **测试场景**: E2E-Scale-Test-001
- **测试参数**:
  - 创建100个BciNode对象
  - 每个对象包含100个IPv6地址
  - 同时进行CRUD操作
- **测试步骤**:
  1. 批量创建大量BciNode对象
  2. 并发进行对象更新操作
  3. 监控系统性能指标
  4. 验证数据一致性
- **验证标准**:
  - 所有对象创建成功
  - 响应时间在可接受范围内
  - 系统资源使用合理
  - 数据一致性保持
- **期望结果**: 大规模场景下性能稳定

#### 5.2 长时间运行稳定性测试
- **测试场景**: E2E-Stability-Test-001
- **测试参数**:
  - 测试运行时间: 24小时
  - 持续的对象创建、更新、删除操作
  - 监控内存泄漏和资源使用
- **测试步骤**:
  1. 启动长时间运行的测试脚本
  2. 持续监控系统健康状态
  3. 定期验证数据一致性
  4. 记录性能指标变化
- **验证标准**:
  - 系统持续稳定运行
  - 没有内存泄漏或资源泄漏
  - 性能指标保持稳定
  - 数据一致性始终保持
- **期望结果**: 长时间运行稳定可靠

### 6. 错误处理和异常情况测试
**测试类别**: 异常情况验证
**测试目标**: 验证系统在异常情况下的健壮性

#### 6.1 无效数据处理测试
- **测试场景**: E2E-Error-Handling-001
- **测试步骤**:
  1. 尝试创建包含无效IPv6地址的BciNode对象
  2. 验证系统的错误处理
  3. 验证错误信息的准确性
  4. 验证系统的恢复能力
- **测试数据**:
  - 格式错误的IPv6地址
  - 超长的IPv6地址
  - 空的IPv6地址字段
- **验证标准**:
  - 无效数据被正确拒绝
  - 错误信息清晰准确
  - 系统状态保持一致
- **期望结果**: 错误处理健壮可靠

#### 6.2 网络异常处理测试
- **测试场景**: E2E-Network-Exception-001
- **测试步骤**:
  1. 模拟网络中断场景
  2. 验证CRD操作的容错性
  3. 验证系统的自动恢复能力
  4. 验证数据的最终一致性
- **验证标准**:
  - 网络恢复后系统正常工作
  - 数据最终一致性保持
  - 没有数据丢失或损坏
- **期望结果**: 网络异常处理正确

## 测试环境要求

### 基础设施要求
- **Kubernetes集群**: 1.22+版本
- **节点规格**: 至少2个节点，每个节点4核8GB内存
- **网络环境**: 支持IPv6的网络环境
- **存储**: 至少100GB可用存储空间

### 测试工具和框架
- **测试框架**: Ginkgo + Gomega
- **CI/CD**: 支持自动化测试执行
- **监控工具**: Prometheus + Grafana
- **日志收集**: ELK Stack

### 测试数据和配置
- **测试数据**: 包含各种IPv6地址格式的样本数据
- **配置文件**: 各种测试场景的CRD配置文件
- **环境变量**: 测试环境的配置参数

## 测试执行策略

### 执行顺序
1. **前置条件检查**: 验证测试环境准备就绪
2. **基础功能测试**: 执行CRD部署和基本操作测试
3. **集成测试**: 验证与其他组件的集成
4. **性能测试**: 进行大规模和长时间运行测试
5. **异常测试**: 验证错误处理和异常情况
6. **清理工作**: 清理测试环境和数据

### 成功标准
- **功能完整性**: 所有测试用例通过率100%
- **性能指标**: 响应时间和资源使用在可接受范围内
- **稳定性**: 长时间运行无异常
- **兼容性**: 跨版本和多环境兼容性良好

### 失败处理
- **失败分析**: 详细记录失败原因和环境信息
- **问题跟踪**: 建立问题跟踪和修复流程
- **回归测试**: 修复后进行完整的回归测试
- **文档更新**: 及时更新测试文档和已知问题 