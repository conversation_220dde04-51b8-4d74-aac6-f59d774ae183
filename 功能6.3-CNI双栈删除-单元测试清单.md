# 功能6.3-CNI双栈删除-单元测试清单

## 概述
本文档列出了功能6.3-CNI双栈删除的单元测试清单，主要测试CNI插件cmdDel函数在处理双栈网络配置删除时的功能。

## 测试范围
- cmdDel函数的双栈地址清理逻辑
- IPv4和IPv6路由规则删除
- 网络接口删除
- 错误处理和日志记录

## 单元测试清单

### 1. cmdDel函数双栈删除测试

#### 1.1 基础双栈删除测试
- **测试名称**: Test_bciCniPlugin_cmdDel_DualStack_Success
- **测试目标**: 验证双栈Pod删除成功的场景
- **测试步骤**:
  1. Mock IPAM返回成功响应，包含IPv4和IPv6信息
  2. Mock网络命名空间操作
  3. Mock获取IPv4和IPv6地址成功
  4. Mock IPv4和IPv6路由规则列表和删除操作
  5. Mock网络接口删除成功
  6. Mock双栈路由规则删除成功
- **预期结果**: 删除操作成功完成，无错误返回

#### 1.2 仅IPv4删除测试
- **测试名称**: Test_bciCniPlugin_cmdDel_IPv4Only_Success
- **测试目标**: 验证仅有IPv4地址的Pod删除成功
- **测试步骤**:
  1. Mock IPAM返回成功响应，仅包含IPv4信息
  2. Mock获取IPv4地址成功，IPv6地址获取失败
  3. Mock IPv4路由规则删除成功
  4. 验证不尝试删除IPv6路由规则
- **预期结果**: 删除操作成功完成，仅处理IPv4清理

#### 1.3 仅IPv6删除测试
- **测试名称**: Test_bciCniPlugin_cmdDel_IPv6Only_Success
- **测试目标**: 验证仅有IPv6地址的Pod删除成功
- **测试步骤**:
  1. Mock IPAM返回成功响应，仅包含IPv6信息
  2. Mock获取IPv4地址失败，IPv6地址获取成功
  3. Mock IPv6路由规则删除成功
  4. 验证不尝试删除IPv4路由规则
- **预期结果**: 删除操作成功完成，仅处理IPv6清理

#### 1.4 地址获取失败处理测试
- **测试名称**: Test_bciCniPlugin_cmdDel_GetAddressFailed_Continue
- **测试目标**: 验证地址获取失败时的处理逻辑
- **测试步骤**:
  1. Mock IPAM返回成功响应
  2. Mock IPv4和IPv6地址获取都失败
  3. Mock网络接口删除成功
  4. 验证跳过路由规则删除步骤
- **预期结果**: 删除操作继续执行，记录警告日志

### 2. 路由规则删除测试

#### 2.1 IPv4路由规则删除测试
- **测试名称**: Test_bciCniPlugin_cmdDel_IPv4Rules_Success
- **测试目标**: 验证IPv4路由规则正确删除
- **测试步骤**:
  1. Mock IPv4路由规则列表返回多个规则
  2. 验证跳过系统表（main、local、default）
  3. Mock删除自定义路由表规则成功
  4. 验证正确的规则被删除
- **预期结果**: 仅删除自定义路由表规则，系统表规则保持不变

#### 2.2 IPv6路由规则删除测试
- **测试名称**: Test_bciCniPlugin_cmdDel_IPv6Rules_Success
- **测试目标**: 验证IPv6路由规则正确删除
- **测试步骤**:
  1. Mock IPv6路由规则列表返回多个规则
  2. 验证跳过系统表（main、local、default）
  3. Mock删除自定义路由表规则成功
  4. 验证正确的规则被删除
- **预期结果**: 仅删除自定义路由表规则，系统表规则保持不变

#### 2.3 IPv6路由规则不支持测试
- **测试名称**: Test_bciCniPlugin_cmdDel_IPv6Rules_NotSupported
- **测试目标**: 验证IPv6路由规则不支持时的处理
- **测试步骤**:
  1. Mock IPv6路由规则列表返回错误
  2. 验证记录警告日志但不中断执行
  3. 验证继续执行后续清理步骤
- **预期结果**: 记录警告日志，继续执行其他清理操作

### 3. 容器路由规则删除测试

#### 3.1 双栈容器路由规则删除测试
- **测试名称**: Test_bciCniPlugin_cmdDel_ContainerRules_DualStack
- **测试目标**: 验证双栈容器路由规则正确删除
- **测试步骤**:
  1. Mock delToOrFromContainerRule调用成功
  2. 验证IPv4的to-container和from-container规则被删除
  3. 验证IPv6的to-container和from-container规则被删除
  4. 验证调用顺序和参数正确性
- **预期结果**: 所有容器路由规则被正确删除

#### 3.2 容器路由规则删除失败测试
- **测试名称**: Test_bciCniPlugin_cmdDel_ContainerRules_Failed
- **测试目标**: 验证容器路由规则删除失败的错误处理
- **测试步骤**:
  1. Mock delToOrFromContainerRule返回错误
  2. 验证错误被正确传播
  3. 验证错误消息包含协议版本信息
- **预期结果**: 返回描述性错误信息，指明失败的协议版本

### 4. 网络命名空间处理测试

#### 4.1 网络命名空间不存在测试
- **测试名称**: Test_bciCniPlugin_cmdDel_NetnsNotExist_Success
- **测试目标**: 验证网络命名空间不存在时的处理
- **测试步骤**:
  1. Mock WithNetNSPath返回NSPathNotExistErr
  2. 验证不返回错误
  3. 验证跳过后续清理步骤
- **预期结果**: 操作成功完成，不报告错误

#### 4.2 网络命名空间操作失败测试
- **测试名称**: Test_bciCniPlugin_cmdDel_NetnsError_Failed
- **测试目标**: 验证网络命名空间操作失败的错误处理
- **测试步骤**:
  1. Mock WithNetNSPath返回其他类型错误
  2. 验证错误被正确传播
- **预期结果**: 返回相应的错误信息

### 5. 日志记录测试

#### 5.1 双栈删除日志测试
- **测试名称**: Test_bciCniPlugin_cmdDel_DualStack_Logging
- **测试目标**: 验证双栈删除过程的日志记录
- **测试步骤**:
  1. 执行双栈删除操作
  2. 验证IPv4和IPv6地址发现日志
  3. 验证路由规则删除日志
  4. 验证成功完成日志
- **预期结果**: 记录详细的操作日志，便于问题排查

#### 5.2 警告日志测试
- **测试名称**: Test_bciCniPlugin_cmdDel_Warning_Logging
- **测试目标**: 验证异常情况的警告日志记录
- **测试步骤**:
  1. Mock地址获取失败
  2. Mock IPv6路由规则不支持
  3. 验证相应的警告日志被记录
- **预期结果**: 记录适当的警告信息，不中断操作流程

## 测试环境要求
- Go测试环境
- Gomock框架
- 网络相关的mock接口
- 日志系统测试支持

## 执行方式
```bash
# 运行CNI插件相关测试
cd bci-cni-driver/cmd/plugins/bci-cni
go test -v -run "Test.*cmdDel.*"

# 运行所有相关单元测试
go test -v ./...
```

## 注意事项
1. 测试需要充分覆盖IPv4和IPv6的各种组合场景
2. 需要验证向后兼容性，确保现有IPv4场景不受影响
3. 错误处理测试需要覆盖各种异常情况
4. 日志记录测试有助于生产环境问题排查
5. Mock接口调用需要验证参数的正确性和调用顺序 