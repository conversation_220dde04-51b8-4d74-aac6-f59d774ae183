# 功能5-Node agent改造-端到端测试清单

## 1. 测试覆盖目标
- 验证Node agent IPAM双栈IP分配功能在真实环境中的正确性
- 测试IPv6地址patch到Pod annotation的完整流程
- 验证双栈IP分配与释放的端到端功能
- 测试IPv6功能与现有IPv4功能的兼容性
- 验证多节点环境下的IPv6地址管理

## 2. 测试环境准备

### 2.1 集群环境要求
- Kubernetes 1.20+
- 支持IPv6的网络环境
- 配置双栈网络的BCI集群
- 启用bci-cni-driver的IPv6功能

### 2.2 节点配置要求
```yaml
# 支持IPv6的节点标签
metadata:
  labels:
    bci-ipv6-enabled: "true"
    
# 节点网络配置
spec:
  podCIDR: "**********/16"
  podCIDRs:
    - "**********/16"
    - "2001:db8::/64"
```

### 2.3 测试资源准备
- 测试用的VPC和子网（支持IPv6）
- 测试用的安全组配置
- 测试用的ENI配置
- 测试用的Pod模板

## 3. 端到端测试场景

### 3.1 基本功能测试

#### 3.1.1 IPv6地址分配测试
```yaml
# 测试场景：Pod请求IPv6地址
apiVersion: v1
kind: Pod
metadata:
  name: test-ipv6-pod
  annotations:
    bci.baidu.com/bci-enable-ipv6: "true"
spec:
  containers:
  - name: test-container
    image: nginx:alpine
```

**测试步骤：**
1. 创建带有IPv6 annotation的Pod
2. 验证Pod成功调度到支持IPv6的节点
3. 检查Pod获得IPv4和IPv6地址
4. 验证Pod annotation包含IPv6地址信息
5. 测试Pod内部IPv6网络连接

**预期结果：**
```bash
# 检查Pod annotation
kubectl get pod test-ipv6-pod -o jsonpath='{.metadata.annotations.bci_internal_PodIP}'
# 输出: ***********

kubectl get pod test-ipv6-pod -o jsonpath='{.metadata.annotations.bci_internal_PodIPv6}'
# 输出: 2001:db8::100

# 检查Pod内部网络
kubectl exec test-ipv6-pod -- ip addr show
# 应该同时显示IPv4和IPv6地址
```

#### 3.1.2 仅IPv4地址分配测试
```yaml
# 测试场景：Pod不请求IPv6地址
apiVersion: v1
kind: Pod
metadata:
  name: test-ipv4-only-pod
spec:
  containers:
  - name: test-container
    image: nginx:alpine
```

**测试步骤：**
1. 创建不带IPv6 annotation的Pod
2. 验证Pod成功调度
3. 检查Pod仅获得IPv4地址
4. 验证Pod annotation不包含IPv6地址信息

**预期结果：**
```bash
# 检查Pod annotation
kubectl get pod test-ipv4-only-pod -o jsonpath='{.metadata.annotations.bci_internal_PodIP}'
# 输出: ***********

kubectl get pod test-ipv4-only-pod -o jsonpath='{.metadata.annotations.bci_internal_PodIPv6}'
# 输出: (空)
```

### 3.2 IPv6地址池管理测试

#### 3.2.1 IPv6地址池消耗测试
```yaml
# 测试场景：消耗所有IPv6地址
apiVersion: v1
kind: Deployment
metadata:
  name: ipv6-consumer
spec:
  replicas: 10
  selector:
    matchLabels:
      app: ipv6-consumer
  template:
    metadata:
      labels:
        app: ipv6-consumer
      annotations:
        bci.baidu.com/bci-enable-ipv6: "true"
    spec:
      containers:
      - name: consumer
        image: nginx:alpine
```

**测试步骤：**
1. 创建大量需要IPv6地址的Pod
2. 观察IPv6地址池消耗情况
3. 验证地址池耗尽后的行为
4. 检查node agent日志
5. 验证地址池自动补充

**预期结果：**
```bash
# 检查BciNode IPv6地址使用情况
kubectl get bcinode -o yaml | grep -A 20 "privateIPv6Addresses"

# 检查node agent日志
kubectl logs -n kube-system node-agent-xxx | grep IPv6
```

#### 3.2.2 IPv6地址释放测试
```bash
# 测试场景：大量Pod删除后IPv6地址回收
kubectl delete deployment ipv6-consumer
```

**测试步骤：**
1. 删除大量使用IPv6地址的Pod
2. 观察IPv6地址回收情况
3. 验证地址池恢复正常
4. 检查Pod annotation清理
5. 验证地址池可重新分配

### 3.3 双栈网络连接测试

#### 3.3.1 Pod间IPv6通信测试
```yaml
# 测试场景：Pod间IPv6通信
apiVersion: v1
kind: Pod
metadata:
  name: ipv6-client
  annotations:
    bci.baidu.com/bci-enable-ipv6: "true"
spec:
  containers:
  - name: client
    image: busybox
    command: ['sleep', '3600']
---
apiVersion: v1
kind: Pod
metadata:
  name: ipv6-server
  annotations:
    bci.baidu.com/bci-enable-ipv6: "true"
spec:
  containers:
  - name: server
    image: nginx:alpine
```

**测试步骤：**
1. 创建IPv6客户端和服务器Pod
2. 获取服务器Pod的IPv6地址
3. 从客户端Pod测试IPv6连接
4. 验证IPv6网络通信正常
5. 测试IPv4网络通信不受影响

**预期结果：**
```bash
# 获取服务器IPv6地址
SERVER_IPV6=$(kubectl get pod ipv6-server -o jsonpath='{.metadata.annotations.bci_internal_PodIPv6}')

# 测试IPv6连接
kubectl exec ipv6-client -- ping6 -c 3 $SERVER_IPV6
kubectl exec ipv6-client -- wget -6 -O- http://[$SERVER_IPV6]:80
```

### 3.4 错误处理和恢复测试

#### 3.4.1 节点不支持IPv6测试
```yaml
# 测试场景：Pod调度到不支持IPv6的节点
apiVersion: v1
kind: Pod
metadata:
  name: test-ipv6-on-ipv4-node
  annotations:
    bci.baidu.com/bci-enable-ipv6: "true"
spec:
  nodeSelector:
    bci-ipv6-enabled: "false"
  containers:
  - name: test-container
    image: nginx:alpine
```

**测试步骤：**
1. 创建需要IPv6但调度到不支持IPv6节点的Pod
2. 验证Pod创建失败或仅获得IPv4地址
3. 检查相关错误日志
4. 验证错误处理机制

#### 3.4.2 IPv6地址分配失败测试
```bash
# 测试场景：模拟IPv6地址分配失败
# 通过修改BciNode状态模拟IPv6地址池为空
kubectl patch bcinode node-name --type='merge' -p='{"spec":{"eniMultiIP":{"pool":{"user":{"eni-id":{"privateIPv6Addresses":{}}}}}}}' 
```

**测试步骤：**
1. 模拟IPv6地址池为空
2. 创建需要IPv6地址的Pod
3. 验证Pod annotation不包含IPv6地址
4. 检查node agent错误日志
5. 验证IPv4地址分配不受影响

### 3.5 性能和并发测试

#### 3.5.1 大量IPv6 Pod创建测试
```yaml
# 测试场景：大量Pod同时请求IPv6地址
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ipv6-stress-test
spec:
  replicas: 100
  selector:
    matchLabels:
      app: ipv6-stress
  template:
    metadata:
      labels:
        app: ipv6-stress
      annotations:
        bci.baidu.com/bci-enable-ipv6: "true"
    spec:
      containers:
      - name: stress
        image: busybox
        command: ['sleep', '300']
```

**测试步骤：**
1. 创建大量需要IPv6地址的Pod
2. 观察IPv6地址分配性能
3. 检查node agent CPU和内存使用
4. 验证所有Pod都获得IPv6地址
5. 测试网络连接性能

#### 3.5.2 IPv6地址分配并发测试
```bash
# 测试场景：并发创建和删除IPv6 Pod
for i in {1..50}; do
  kubectl create -f ipv6-pod-$i.yaml &
done

# 同时删除部分Pod
for i in {1..25}; do
  kubectl delete pod ipv6-pod-$i &
done
```

**测试步骤：**
1. 并发创建和删除大量IPv6 Pod
2. 观察地址分配和回收情况
3. 检查地址池一致性
4. 验证无地址泄漏
5. 检查日志中是否有错误

### 3.6 升级和兼容性测试

#### 3.6.1 版本升级测试
```bash
# 测试场景：node agent升级过程中的IPv6功能
kubectl set image daemonset/node-agent -n kube-system node-agent=new-version
```

**测试步骤：**
1. 升级前创建IPv6 Pod
2. 执行node agent升级
3. 验证现有IPv6 Pod不受影响
4. 创建新的IPv6 Pod
5. 验证IPv6功能正常

#### 3.6.2 向后兼容性测试
```yaml
# 测试场景：旧版本Pod与新版本IPv6功能兼容性
apiVersion: v1
kind: Pod
metadata:
  name: legacy-pod
spec:
  containers:
  - name: legacy-container
    image: nginx:alpine
```

**测试步骤：**
1. 创建不带IPv6 annotation的旧版本Pod
2. 验证Pod正常运行
3. 检查IPv4地址分配正常
4. 验证不会意外分配IPv6地址
5. 测试网络连接正常

## 4. 监控和日志验证

### 4.1 关键日志检查
```bash
# node agent日志
kubectl logs -n kube-system node-agent-xxx | grep -E "(IPv6|alloc|patch)"

# 关键日志模式
# - "allocated IPv6 for pod"
# - "patch pod with IPv6 address"
# - "released IPv6 success"
# - "IPv6 queue available length"
```

### 4.2 指标监控
```bash
# 检查IPv6地址分配指标
kubectl exec -n kube-system node-agent-xxx -- curl localhost:8080/metrics | grep ipv6

# 关键指标
# - ipv6_addresses_allocated_total
# - ipv6_addresses_released_total
# - ipv6_pool_available_count
# - ipv6_allocation_errors_total
```

### 4.3 资源状态验证
```bash
# 检查BciNode IPv6状态
kubectl get bcinode -o yaml | grep -A 50 "privateIPv6Addresses"

# 检查Pod IPv6 annotation
kubectl get pods -o jsonpath='{range .items[*]}{.metadata.name}: {.metadata.annotations.bci_internal_PodIPv6}{"\n"}{end}'
```

## 5. 测试环境清理

### 5.1 资源清理脚本
```bash
#!/bin/bash
# 清理测试资源
kubectl delete deployment ipv6-consumer ipv6-stress-test
kubectl delete pod test-ipv6-pod test-ipv4-only-pod ipv6-client ipv6-server
kubectl delete pod test-ipv6-on-ipv4-node

# 验证IPv6地址释放
kubectl get bcinode -o yaml | grep -A 20 "privateIPv6Addresses"
```

### 5.2 验证清理完成
```bash
# 检查无遗留IPv6地址
kubectl get bcinode -o yaml | grep -E "(privateIPv6Addresses.*\{|\})" | wc -l

# 检查Pod annotation清理
kubectl get pods -o jsonpath='{range .items[*]}{.metadata.annotations.bci_internal_PodIPv6}{"\n"}{end}' | grep -v "^$" | wc -l
```

## 6. 测试执行计划

### 6.1 测试阶段
1. **基本功能验证**: 验证IPv6地址分配基本功能
2. **地址池管理测试**: 验证IPv6地址池管理
3. **网络连接测试**: 验证IPv6网络通信
4. **错误处理测试**: 验证异常情况处理
5. **性能测试**: 验证大规模场景性能
6. **兼容性测试**: 验证向后兼容性

### 6.2 测试环境
- **开发环境**: 基本功能验证
- **测试环境**: 完整功能测试
- **预生产环境**: 性能和稳定性测试
- **生产环境**: 小规模验证测试

## 7. 成功标准

### 7.1 功能验证标准
- [x] IPv6地址成功分配到Pod
- [x] Pod annotation正确包含IPv6地址
- [x] IPv6网络连接正常
- [x] IPv6地址正确释放和回收
- [x] 现有IPv4功能不受影响

### 7.2 性能标准
- [x] IPv6地址分配延迟 < 100ms
- [x] 支持100+ Pod并发IPv6分配
- [x] 内存使用增长 < 20%
- [x] CPU使用增长 < 10%

### 7.3 稳定性标准
- [x] 24小时持续运行无错误
- [x] 无IPv6地址泄漏
- [x] 无内存泄漏
- [x] 日志无异常错误

## 8. 问题处理指南

### 8.1 常见问题
1. **IPv6地址分配失败**
   - 检查节点IPv6支持
   - 验证IPv6地址池状态
   - 检查ENI IPv6配置

2. **Pod annotation缺失**
   - 检查node agent日志
   - 验证Kubernetes API权限
   - 确认Pod annotation格式

3. **IPv6网络连接失败**
   - 检查Pod IPv6地址配置
   - 验证路由表配置
   - 检查安全组规则

### 8.2 调试工具
```bash
# 调试IPv6地址分配
kubectl describe bcinode node-name
kubectl logs -n kube-system node-agent-xxx

# 调试Pod网络
kubectl exec pod-name -- ip -6 addr show
kubectl exec pod-name -- ip -6 route show
kubectl exec pod-name -- ping6 -c 3 2001:db8::1
```

## 9. 测试报告模板

### 9.1 测试结果记录
- **测试时间**: 
- **测试环境**: 
- **测试版本**: 
- **测试结果**: 通过/失败
- **发现问题**: 
- **修复建议**: 

### 9.2 性能数据记录
- **IPv6地址分配性能**: 
- **内存使用情况**: 
- **CPU使用情况**: 
- **网络连接性能**: 

### 9.3 质量评估
- **功能完整性**: 
- **性能表现**: 
- **稳定性**: 
- **兼容性**: 
- **整体评价**: 