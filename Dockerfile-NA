# Build the manager binary
FROM golang:1.19 as builder

WORKDIR /workspace
# Copy the Go Modules manifests and download
COPY go.mod go.mod
COPY go.sum go.sum
RUN go env -w GO111MODULE=on
RUN go env -w GOPROXY="https://goproxy.cn"
RUN go mod download

# Build
COPY . .
WORKDIR /workspace/cmd/node-agent
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -a -o /bci-node-agent main.go
WORKDIR /workspace/cmd/plugins/bci-cni
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -a -o /bci-cni main.go
WORKDIR /workspace/cmd/prob
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -a -o /prob main.go
WORKDIR /workspace/cmd/exec
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -a -o /exec main.go

FROM alpine
WORKDIR /
COPY --from=builder /bci-node-agent .
COPY --from=builder /bci-cni .
COPY --from=builder /prob .
COPY --from=builder /exec .
COPY --from=builder /workspace/cmd/plugins/bci-cni/10-bci-cni.conf .
COPY --from=builder /workspace/config/nadeploy/control.sh .
COPY --from=builder /workspace/config/nadeploy/loopback .
# USER 65532:65532

ENTRYPOINT ["/control.sh", "start"]
