---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  creationTimestamp: null
  name: manager-role
rules:
- apiGroups:
  - networking.bci.cloud.baidu.com
  resources:
  - bcinodes
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - networking.bci.cloud.baidu.com
  resources:
  - bcinodes/finalizers
  verbs:
  - update
- apiGroups:
  - networking.bci.cloud.baidu.com
  resources:
  - bcinodes/status
  verbs:
  - get
  - patch
  - update
