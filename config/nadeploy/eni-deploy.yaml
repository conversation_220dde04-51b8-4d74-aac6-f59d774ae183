apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    deployment.kubernetes.io/revision: "2"
  name: test-eni
  namespace: 2e1be1eb99e946c3a543ec5a4eaa7d39
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: test-eni
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      annotations:
        bci_internal_eniPrivateIp: "1"
        cross-vpc-eni.cce.io/securityGroupIDs: g-59gf44p4jmwe
        cross-vpc-eni.cce.io/subnetID: sbn-r107hfd7a45n
        cross-vpc-eni.cce.io/userID: 2e1be1eb99e946c3a543ec5a4eaa7d39
        cross-vpc-eni.cce.io/vpcCidr: ************/22
        bci_internal_AccountID: 2e1be1eb99e946c3a543ec5a4eaa7d39
        bci_internal_LogicZone: zoneC
        bci_internal_PhysicalZone: AZONE-gzbh
        bci_internal_PodCpu: '0.25'
        bci_internal_PodMem: 0.5Gi
        bci_internal_ZoneID: z-GeehYcB1
        bci.cloud.baidu.com/ephemeral-quota: disabled
        bci.cloud.baidu.com/lxcfs: disabled
      labels:
        app: test-eni
    spec:
      containers:
      - name: pod1
        image: registry.baidubce.com/wenzt-test/alpine
        imagePullPolicy: IfNotPresent
        resources:
          requests:
            cpu: 0.5
            memory: 0.5Gi
          limits:
            cpu: 0.5
            memory: 0.5Gi
        command:
          - sh
        args:
          - -c
          - echo hello && echo gdc && sleep ********