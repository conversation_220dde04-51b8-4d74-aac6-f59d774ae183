apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: bci-cni-node-agent
  namespace: kube-system
  labels:
    app: bci-cni-node-agent
spec:
  selector:
    matchLabels:
      app: bci-cni-node-agent
  template:
    metadata:
      labels:
        app: bci-cni-node-agent
    spec:
      hostNetwork: true
      tolerations:
      - effect: NoSchedule
        key: node-role.kubernetes.io/master
        operator: Exists
      - effect: NoSchedule
        key: node.kubernetes.io/not-ready
        operator: Exists
      imagePullSecrets:
      - name: bce-image-secret
      containers:
      - name: bci-cni-node-agent
        image: registry.baidubce.com/wenzt-test/cni-node-agent:enis
        imagePullPolicy: Always
        securityContext:
          privileged: true
        command:
          - "/control.sh"
          - start
        args:
          - "/config/config-bd-online"
          - ":8994"
          - ":8995"
        env:
        - name: NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        - name: MY_POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: MY_POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        volumeMounts:
          - name: kubeconfig
            mountPath: /config
            readOnly: true
          - name: cni-socket
            mountPath: /var/run/bci-cni/
          - name: netns
            mountPath: /run/netns
            mountPropagation: Bidirectional
          - name: cni-config
            mountPath: /etc/cni/net.d
          - name: cni-bin
            mountPath: /opt/cni/bin
          - name: timezone
            mountPath: /etc/localtime
            readOnly: true
          - name: bci-tools-dir
            mountPath: /etc/bci
      volumes:
        - name: kubeconfig
          configMap:
            name: kubeconfig
        - name: cni-socket
          hostPath:
            path: /run/bci-cni/
            type: DirectoryOrCreate
        - name: netns
          hostPath:
            path: /run/netns
            type: DirectoryOrCreate
        - name: cni-config
          hostPath:
            path: /etc/cni/net.d
            type: DirectoryOrCreate
        - name: cni-bin
          hostPath:
            path: /opt/cni/bin
            type: DirectoryOrCreate
        - name: timezone
          hostPath:
            path: /usr/share/zoneinfo/Asia/Shanghai
        - name: bci-tools-dir
          hostPath:
            path: /etc/bci
            type: DirectoryOrCreate