apiVersion: v1
kind: Pod
metadata:
  name: bci-cni-node-agent
  namespace: kube-system
spec:
  restartPolicy: OnFailure
  hostNetwork: true
  nodeName: ************
  containers:
  - name: bci-cni-node-agent
    resources:
      requests:
        cpu: "1"
        memory: "2G"
    image: registry.baidubce.com/wenzt-test/cni-node-agent:enis
    imagePullPolicy: Always
    # command:
    #   - /node-agent
    # args:
    #   - -kubeconfig
    #   - /config/gz-config-vpc
    #   - -health-probe-bind-address
    #   - :8990
    #   - -metrics-bind-address
    #   - :8991
    securityContext:
      privileged: true
    env:
    - name: NODE_NAME
      valueFrom:
        fieldRef:
          fieldPath: spec.nodeName
    - name: MY_POD_NAME
      valueFrom:
        fieldRef:
          fieldPath: metadata.name
    - name: MY_POD_NAMESPACE
      valueFrom:
        fieldRef:
          fieldPath: metadata.namespace
    volumeMounts:
      - name: kubeconfig
        mountPath: /config
        readOnly: true
      - name: cni-socket
        mountPath: /var/run/bci-cni/
      - name: netns
        mountPath: /run/netns
        mountPropagation: Bidirectional
      - name: cni-config
        mountPath: /etc/cni/net.d
      - name: cni-bin
        mountPath: /opt/cni/bin
      - name: timezone
        mountPath: /etc/localtime
        readOnly: true
  volumes:
    - name: kubeconfig
      configMap:
        name: kubeconfig-vpc
    - name: cni-socket
      hostPath:
        path: /run/bci-cni/
        type: DirectoryOrCreate
    - name: netns
      hostPath:
        path: /run/netns
        type: DirectoryOrCreate
    - name: cni-config
      hostPath:
        path: /etc/cni/net.d
    - name: cni-bin
      hostPath:
        path: /opt/cni/bin
    - name: timezone
      hostPath:
        path: /usr/share/zoneinfo/Asia/Shanghai