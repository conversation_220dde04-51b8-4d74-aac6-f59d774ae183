#!/bin/sh
#kubectl create configmap kubeconfig --from-file=../../cmd/node-agent/config -n kube-system

KUBECONFIG=$2
HEALTH_PORT=$3
METRICS_PORT=$4
set -e

CNI_BIN_DIR=/opt/cni/bin
CNI_CONF_DIR=/etc/cni/net.d

function atomic_cp {
  local src="$1"
  local dst="$2"

  echo "Installing ${src} to ${dst} ..."

  cp "${src}" "${dst}.tmp"
  mv "${dst}.tmp" "${dst}"
}

do_start() {
  # install cni conf
  atomic_cp /10-bci-cni.conf ${CNI_CONF_DIR}/00-bci-cni.conf

  # install cni binary
  atomic_cp /bci-cni ${CNI_BIN_DIR}/bci-cni
  atomic_cp /loopback ${CNI_BIN_DIR}/loopback
  mkdir -p /etc/bci/
  atomic_cp /prob /etc/bci/prob
  atomic_cp /exec /etc/bci/exec

  # start node agent
  /bci-node-agent -kubeconfig $KUBECONFIG -health-probe-bind-address $HEALTH_PORT -metrics-bind-address $METRICS_PORT
}

do_status() {
  pid=$(ps aux | grep node-agent | grep -v grep | awk -F" " '{print $1}')
  ret=$?
  if [ ${ret} -eq 0 -n "${pid}" ]; then
    return $?
  elif [ ${ret} -eq 1 ]; then
    return 1
  fi
}

case "${1}" in
start)
  do_start
  ;;
stop)
  do_stop
  ;;
status)
  do_status
  ;;
*)
  exit 1
  ;;
esac
