---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.18.0
  name: bcinodes.networking.bci.cloud.baidu.com
spec:
  group: networking.bci.cloud.baidu.com
  names:
    kind: BciNode
    listKind: BciNodeList
    plural: bcinodes
    singular: bcinode
  scope: Cluster
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: BciNode is the Schema for the bcinodes API
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: BciNodeSpec defines the desired state of BciNode
            properties:
              eniMultiIP:
                description: EniMultiIP is eni with multi ip mode
                properties:
                  maxAboveWatermark:
                    description: |-
                      MaxAboveWatermark is the maximum number of addresses to allocate
                      beyond the addresses needed to reach the PreAllocate watermark.
                      Going above the watermark can help reduce the number of API calls to
                      allocate IPs, e.g. when a new ENI is allocated, as many secondary
                      IPs as possible are allocated. Limiting the amount can help reduce
                      waste of IPs.
                    minimum: 0
                    type: integer
                  maxAllocate:
                    description: |-
                      MaxAllocate is the maximum number of IPs that can be allocated to the
                      node. When the current amount of allocated IPs will approach this value,
                      the considered value for PreAllocate will decrease down to 0 in order to
                      not attempt to allocate more addresses than defined.
                    minimum: 0
                    type: integer
                  minAllocate:
                    description: |-
                      MinAllocate is the minimum number of IPs that must be allocated when
                      the node is first bootstrapped. It defines the minimum base socket
                      of addresses that must be available. After reaching this watermark,
                      the PreAllocate and MaxAboveWatermark logic takes over to continue
                      allocating IPs.
                    minimum: 0
                    type: integer
                  pool:
                    additionalProperties:
                      additionalProperties:
                        description: |-
                          AllocationIP is an eni which is available for allocation, or already
                          has been allocated
                        properties:
                          eniID:
                            description: EniID is the id of eni
                            type: string
                          macAddress:
                            description: MacAddress is the hw address of eni
                            type: string
                          primaryIPAddress:
                            description: PrimaryIPAddress the primary IPv4 address
                            type: string
                          privateIPAddresses:
                            additionalProperties:
                              description: |-
                                AllocationIP is a IP which is available for allocation, or already
                                has been allocated
                              properties:
                                containerID:
                                  description: ContainerID is the infra container
                                    id of this IP
                                  type: string
                                eip:
                                  description: EIP is the eip of this private IP,
                                    left blank if no eip
                                  type: string
                                eniID:
                                  description: EniID is the id of eni which this IP
                                    belongs
                                  type: string
                                owner:
                                  description: |-
                                    Owner is the owner of the IP. This field is set if the IP has been
                                    allocated. It will be set to format of pod namespace/pod name
                                  type: string
                                userID:
                                  description: UserID is the id of tenant user which
                                    this IP belongs
                                  type: string
                              type: object
                            description: PrivateIPAddresses is a map of AllocationIP
                              indexed by IP
                            type: object
                          privateIPv6Addresses:
                            additionalProperties:
                              description: |-
                                AllocationIP is a IP which is available for allocation, or already
                                has been allocated
                              properties:
                                containerID:
                                  description: ContainerID is the infra container
                                    id of this IP
                                  type: string
                                eip:
                                  description: EIP is the eip of this private IP,
                                    left blank if no eip
                                  type: string
                                eniID:
                                  description: EniID is the id of eni which this IP
                                    belongs
                                  type: string
                                owner:
                                  description: |-
                                    Owner is the owner of the IP. This field is set if the IP has been
                                    allocated. It will be set to format of pod namespace/pod name
                                  type: string
                                userID:
                                  description: UserID is the id of tenant user which
                                    this IP belongs
                                  type: string
                              type: object
                            description: PrivateIPv6Addresses is a map of AllocationIP
                              indexed by IPv6 address
                            type: object
                          securityGroupIDs:
                            description: SecurityGroupIDs is the security groups bound
                              to eni
                            items:
                              type: string
                            type: array
                          subnetID:
                            description: SubnetID is the subnet id where eni creates
                            type: string
                          userID:
                            description: UserID is the id of tenant user
                            type: string
                          vpcCidr:
                            description: VpcCIDR is the cidr of VpcID
                            type: string
                          vpcID:
                            description: VpcID is the vpc id where eni creates
                            type: string
                        required:
                        - eniID
                        - macAddress
                        - primaryIPAddress
                        - userID
                        type: object
                      description: UserAllocationEnis is a map of allocated enis indexed
                        by eniID
                      type: object
                    description: |-
                      Pool is the list of IPs available to the node for allocation. When
                      an IP is used, the IP will remain on this list but will be added to
                      Status.EniMultiIP.Used
                    type: object
                  preAllocate:
                    description: |-
                      PreAllocate defines the number of IP addresses that must be
                      available for allocation in the IPAMspec. It defines the buffer of
                      addresses available immediately without requiring cilium-operator to
                      get involved.
                    minimum: 0
                    type: integer
                required:
                - maxAllocate
                type: object
              instanceId:
                description: InstanceID is the unique instance id of this node
                type: string
              instanceType:
                description: InstanceType is the type of node, bcc/bbc etc.
                type: string
            type: object
          status:
            description: BciNodeStatus defines the observed state of BciNode
            properties:
              eniMultiIP:
                description: EniMultiIP is eni multi ip mode specific status of the
                  node.
                properties:
                  notReady:
                    description: NotReady lists all eniID which init failed
                    items:
                      type: string
                    type: array
                  used:
                    additionalProperties:
                      additionalProperties:
                        description: |-
                          AllocationIP is an eni which is available for allocation, or already
                          has been allocated
                        properties:
                          eniID:
                            description: EniID is the id of eni
                            type: string
                          macAddress:
                            description: MacAddress is the hw address of eni
                            type: string
                          primaryIPAddress:
                            description: PrimaryIPAddress the primary IPv4 address
                            type: string
                          privateIPAddresses:
                            additionalProperties:
                              description: |-
                                AllocationIP is a IP which is available for allocation, or already
                                has been allocated
                              properties:
                                containerID:
                                  description: ContainerID is the infra container
                                    id of this IP
                                  type: string
                                eip:
                                  description: EIP is the eip of this private IP,
                                    left blank if no eip
                                  type: string
                                eniID:
                                  description: EniID is the id of eni which this IP
                                    belongs
                                  type: string
                                owner:
                                  description: |-
                                    Owner is the owner of the IP. This field is set if the IP has been
                                    allocated. It will be set to format of pod namespace/pod name
                                  type: string
                                userID:
                                  description: UserID is the id of tenant user which
                                    this IP belongs
                                  type: string
                              type: object
                            description: PrivateIPAddresses is a map of AllocationIP
                              indexed by IP
                            type: object
                          privateIPv6Addresses:
                            additionalProperties:
                              description: |-
                                AllocationIP is a IP which is available for allocation, or already
                                has been allocated
                              properties:
                                containerID:
                                  description: ContainerID is the infra container
                                    id of this IP
                                  type: string
                                eip:
                                  description: EIP is the eip of this private IP,
                                    left blank if no eip
                                  type: string
                                eniID:
                                  description: EniID is the id of eni which this IP
                                    belongs
                                  type: string
                                owner:
                                  description: |-
                                    Owner is the owner of the IP. This field is set if the IP has been
                                    allocated. It will be set to format of pod namespace/pod name
                                  type: string
                                userID:
                                  description: UserID is the id of tenant user which
                                    this IP belongs
                                  type: string
                              type: object
                            description: PrivateIPv6Addresses is a map of AllocationIP
                              indexed by IPv6 address
                            type: object
                          securityGroupIDs:
                            description: SecurityGroupIDs is the security groups bound
                              to eni
                            items:
                              type: string
                            type: array
                          subnetID:
                            description: SubnetID is the subnet id where eni creates
                            type: string
                          userID:
                            description: UserID is the id of tenant user
                            type: string
                          vpcCidr:
                            description: VpcCIDR is the cidr of VpcID
                            type: string
                          vpcID:
                            description: VpcID is the vpc id where eni creates
                            type: string
                        required:
                        - eniID
                        - macAddress
                        - primaryIPAddress
                        - userID
                        type: object
                      description: UserAllocationEnis is a map of allocated enis indexed
                        by eniID
                      type: object
                    description: |-
                      Used lists all IPs out of Spec.EniMultiIP.Pool which have been allocated
                      and are in use.
                    type: object
                type: object
              waitReleaseIP:
                additionalProperties:
                  additionalProperties:
                    type: string
                  type: object
                description: |-
                  WaitReleaseIP tracks the state for every IP considered for release.
                  This map in map structure has two level of indices. The first index is eni id while the
                  second index is IP address.
                type: object
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
