# init project path
HOMEDIR := $(shell pwd)
OUTDIR  := $(HOMEDIR)/output

GOPKGS  := $$(go list ./...| grep -vE "vendor")
#GOTEST  := $(GO) test -race -timeout 30s -gcflags="-N -l"
#GOBUILD := $(GO) build

# init command params
#GO      := $(GO_1_19_BIN)/go
#GOROOT  := $(GO_1_19_HOME)
#GOPATH  := $(shell $(GO) env GOPATH)
#GOMOD   := $(GO) mod
#GOBUILD := $(GO) build
#GOTEST  := $(GO) test -race -timeout 30s -gcflags="-N -l"
#GOPKGS  := $$($(GO) list ./...| grep -vE "vendor")

# test cover files
#COVPROF := $(HOMEDIR)/covprof.out  # coverage profile
#COVFUNC := $(HOMEDIR)/covfunc.txt  # coverage profile information for each function
#COVHTML := $(HOMEDIR)/covhtml.html # HTML representation of coverage profile

# 设置编译时所需的GO环境
export GOENV = $(HOMEDIR)/go.env

# 执行编译，可使用命令 make 或 make all 执行， 顺序执行 prepare -> compile -> package 几个阶段
all: prepare compile package

# set proxy env
#set-env:
#	$(GO) env -w GO111MODULE=on
#	$(GO) env -w GONOPROXY=\*.baidu.com\*
#	$(GO) env -w GOPROXY=https://goproxy.baidu-int.com
#	$(GO) env -w GONOSUMDB=\*
#	#$(GO) env -w CC=/opt/compiler/gcc-8.2/bin/gcc
#	#$(GO) env -w CXX=/opt/compiler/gcc-8.2/bin/g++

#make prepare, download dependencies
#prepare: gomod

#gomod: set-env
#	$(GOMOD) download -x || $(GOMOD) download -x

# prepare阶段
prepare:
#    bcloud local -U # 下载非 Go 依赖，依赖之前的 BCLOUD 文件
	git version     # 低于 2.17.1 可能不能正常工作
	go env          # 打印出 go 环境信息，可用于排查问题
	go mod download || go mod download -x # 下载 Go 依赖


# ompile 阶段，执行编译命令，可单独执行命令: make compile
compile: build

build: prepare
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -a -o $(HOMEDIR)/bci-node-agent cmd/node-agent/main.go
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -a -o $(HOMEDIR)/bci-cni cmd/plugins/bci-cni/main.go
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -a -o $(HOMEDIR)/prob cmd/prob/main.go
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -a -o $(HOMEDIR)/exec cmd/exec/main.go

# make test, test your code
test: prepare test-case
test-case:
	go test -race -timeout 30s -gcflags="-N -l" -v -cover $(GOPKGS)

# package阶段，阶段，对编译产出进行打包，输出到 output 目录， 可单独执行命令: make package
package: package-bin
package-bin:
	mkdir -p $(OUTDIR)
	mv bci-node-agent $(OUTDIR)/
	mv bci-cni $(OUTDIR)/
	mv prob $(OUTDIR)/
	mv exec $(OUTDIR)/
	cp cmd/plugins/bci-cni/10-bci-cni.conf $(OUTDIR)/
	cp config/nadeploy/control.sh $(OUTDIR)/
	cp config/nadeploy/loopback $(OUTDIR)/
	cp Dockerfile2 $(OUTDIR)/
	cp Dockerfile-NA $(OUTDIR)/

# clean 阶段，清除过程中的输出， 可单独执行命令: make clean
clean:
	#$(GO) clean
	rm -rf $(OUTDIR)
	rm -rf $(HOMEDIR)/bci-node-agent
	rm -rf $(HOMEDIR)/bci-cni
	rm -rf $(GOPATH)/pkg/darwin_amd64

# avoid filename conflict and speed up build 
.PHONY: all prepare compile test package clean build