# 功能6.2-CNI网络接口配置-单元测试清单

## 功能概述
CNI插件网络接口配置功能，支持双栈网络接口的创建、配置和路由设置，确保IPv4和IPv6地址都能正确配置到容器网络接口中。

## 测试范围
本测试清单覆盖CNI插件网络接口配置功能的所有核心组件和关键路径，包括容器veth配置和ENI网络命名空间配置。

## 单元测试清单

### 1. setupContainerVeth函数测试 (cmd/plugins/bci-cni/main_test.go)

#### 1.1 IPv4单个地址接口配置测试
**测试文件**: `TestSetupContainerVeth_IPv4SingleAddress`
- **测试目标**: 验证IPv4单个地址的容器网络接口配置
- **测试场景**:
  - 创建IPv4单地址的veth pair
  - 配置IPv4地址到容器接口
  - 设置IPv4默认路由
- **断言要点**:
  - veth pair正确创建
  - IPv4地址正确配置
  - 使用IPv4zero作为默认路由目标
  - 路由表配置正确

#### 1.2 IPv4多个地址接口配置测试
**测试文件**: `TestSetupContainerVeth_IPv4MultipleAddresses`
- **测试目标**: 验证IPv4多个地址的容器网络接口配置
- **测试场景**:
  - 创建多个IPv4地址的veth pair
  - 配置多个IPv4地址到不同接口
  - 设置多个路由表和规则
- **断言要点**:
  - 多个veth pair正确创建
  - 每个IPv4地址配置到正确接口
  - 路由表规则正确设置
  - 主路由表和自定义路由表都正确配置

#### 1.3 IPv6单个地址接口配置测试
**测试文件**: `TestSetupContainerVeth_IPv6SingleAddress`
- **测试目标**: 验证IPv6单个地址的容器网络接口配置
- **测试场景**:
  - 创建IPv6单地址的veth pair
  - 配置IPv6地址到容器接口
  - 设置IPv6默认路由
- **断言要点**:
  - veth pair正确创建
  - IPv6地址正确配置（/128掩码）
  - 使用IPv6zero作为默认路由目标
  - IPv6路由表配置正确

#### 1.4 双栈地址接口配置测试
**测试文件**: `TestSetupContainerVeth_DualStackAddresses`
- **测试目标**: 验证双栈地址的容器网络接口配置
- **测试场景**:
  - 创建包含IPv4和IPv6地址的veth pair
  - 配置双栈地址到容器接口
  - 设置IPv4和IPv6路由
- **断言要点**:
  - 双栈veth pair正确创建
  - IPv4和IPv6地址都正确配置
  - IPv4使用IPv4zero，IPv6使用IPv6zero
  - 双栈路由表配置正确

#### 1.5 路由规则配置测试
**测试文件**: `TestSetupContainerVeth_RouteRuleConfiguration`
- **测试目标**: 验证路由规则的正确配置
- **测试场景**:
  - IPv4路由规则配置
  - IPv6路由规则配置
  - 多IP路由表创建
- **断言要点**:
  - 网关路由正确设置
  - 默认路由正确配置
  - 路由表规则正确创建
  - 路由优先级正确设置

### 2. setupEniNsVeth函数测试

#### 2.1 IPv4 ENI网络命名空间配置测试
**测试文件**: `TestSetupEniNsVeth_IPv4Configuration`
- **测试目标**: 验证IPv4地址在ENI网络命名空间中的配置
- **测试场景**:
  - 将IPv4 veth移动到ENI命名空间
  - 配置IPv4网关地址
  - 设置IPv4主机路由
- **断言要点**:
  - veth正确移动到ENI命名空间
  - IPv4网关地址正确配置（/32掩码）
  - 主机路由正确设置
  - 网络接口正确启用

#### 2.2 IPv6 ENI网络命名空间配置测试
**测试文件**: `TestSetupEniNsVeth_IPv6Configuration`
- **测试目标**: 验证IPv6地址在ENI网络命名空间中的配置
- **测试场景**:
  - 将IPv6 veth移动到ENI命名空间
  - 配置IPv6网关地址
  - 设置IPv6主机路由
- **断言要点**:
  - veth正确移动到ENI命名空间
  - IPv6网关地址正确配置（/128掩码）
  - 主机路由正确设置
  - 网络接口正确启用

#### 2.3 双栈ENI网络命名空间配置测试
**测试文件**: `TestSetupEniNsVeth_DualStackConfiguration`
- **测试目标**: 验证双栈地址在ENI网络命名空间中的配置
- **测试场景**:
  - 将双栈veth移动到ENI命名空间
  - 配置IPv4和IPv6网关地址
  - 设置双栈主机路由
- **断言要点**:
  - 双栈veth正确移动到ENI命名空间
  - IPv4和IPv6网关地址都正确配置
  - 双栈主机路由都正确设置
  - IP转发功能正确启用

#### 2.4 IP转发配置测试
**测试文件**: `TestSetupEniNsVeth_IPForwardConfiguration`
- **测试目标**: 验证IP转发功能的配置
- **测试场景**:
  - IPv4转发配置
  - IPv6转发配置
  - 双栈转发配置
- **断言要点**:
  - IPv4转发正确启用
  - IPv6转发正确启用
  - 转发配置在ENI命名空间中生效

### 3. 网络接口属性测试

#### 3.1 MTU配置测试
**测试文件**: `TestNetworkInterface_MTUConfiguration`
- **测试目标**: 验证MTU配置的正确性
- **测试场景**:
  - 指定MTU配置
  - 自动检测MTU
  - IPv4和IPv6接口MTU一致性
- **断言要点**:
  - MTU正确应用到接口
  - IPv4和IPv6接口MTU一致
  - 自动检测MTU功能正常

#### 3.2 MAC地址配置测试
**测试文件**: `TestNetworkInterface_MACAddressConfiguration`
- **测试目标**: 验证MAC地址配置的正确性
- **测试场景**:
  - veth pair MAC地址设置
  - 双栈接口MAC地址一致性
  - MAC地址记录准确性
- **断言要点**:
  - MAC地址正确设置
  - 双栈接口使用相同MAC地址
  - Interface对象MAC地址正确记录

#### 3.3 接口命名测试
**测试文件**: `TestNetworkInterface_InterfaceNaming`
- **测试目标**: 验证网络接口命名的正确性
- **测试场景**:
  - 单IP接口命名
  - 多IP接口命名
  - 双栈接口命名
- **断言要点**:
  - 主接口使用指定名称
  - 多IP接口使用eth0, eth1等命名
  - 双栈接口命名规则正确

### 4. 错误处理测试

#### 4.1 veth pair创建失败测试
**测试文件**: `TestSetupContainerVeth_VethCreationFailure`
- **测试目标**: 验证veth pair创建失败时的处理
- **测试场景**:
  - 网络命名空间异常
  - 接口名称冲突
  - 系统资源不足
- **断言要点**:
  - 错误正确传播
  - 资源清理完整
  - 错误信息准确

#### 4.2 IP地址配置失败测试
**测试文件**: `TestSetupContainerVeth_IPConfigurationFailure`
- **测试目标**: 验证IP地址配置失败时的处理
- **测试场景**:
  - IPv4地址配置失败
  - IPv6地址配置失败
  - 网关地址无效
- **断言要点**:
  - 错误正确处理
  - 部分配置回滚
  - 错误区分IPv4/IPv6

#### 4.3 路由配置失败测试
**测试文件**: `TestSetupContainerVeth_RouteConfigurationFailure`
- **测试目标**: 验证路由配置失败时的处理
- **测试场景**:
  - 默认路由配置失败
  - 网关路由配置失败
  - 路由表创建失败
- **断言要点**:
  - 路由错误正确处理
  - 接口配置状态一致
  - 错误信息详细

#### 4.4 ENI命名空间配置失败测试
**测试文件**: `TestSetupEniNsVeth_ENINamespaceFailure`
- **测试目标**: 验证ENI命名空间配置失败时的处理
- **测试场景**:
  - veth移动失败
  - 网关地址配置失败
  - 主机路由配置失败
- **断言要点**:
  - 命名空间错误正确处理
  - 接口状态一致
  - 清理机制生效

### 5. 网络命名空间测试

#### 5.1 容器命名空间操作测试
**测试文件**: `TestNetworkNamespace_ContainerOperations`
- **测试目标**: 验证容器网络命名空间的操作
- **测试场景**:
  - 进入容器命名空间
  - 在容器命名空间中配置网络
  - 命名空间间网络隔离
- **断言要点**:
  - 命名空间切换正确
  - 网络配置在正确命名空间
  - 网络隔离有效

#### 5.2 ENI命名空间操作测试
**测试文件**: `TestNetworkNamespace_ENIOperations`
- **测试目标**: 验证ENI网络命名空间的操作
- **测试场景**:
  - 进入ENI命名空间
  - 在ENI命名空间中配置网络
  - 命名空间间通信
- **断言要点**:
  - ENI命名空间操作正确
  - 网络配置在正确命名空间
  - 跨命名空间通信正常

### 6. 性能测试

#### 6.1 网络接口创建性能测试
**测试文件**: `BenchmarkNetworkInterface_Creation`
- **测试目标**: 验证网络接口创建性能
- **测试场景**:
  - 单IP接口创建性能
  - 多IP接口创建性能
  - 双栈接口创建性能
- **断言要点**:
  - 接口创建时间合理
  - 内存使用优化
  - 并发创建性能

#### 6.2 路由配置性能测试
**测试文件**: `BenchmarkNetworkInterface_RouteConfiguration`
- **测试目标**: 验证路由配置性能
- **测试场景**:
  - 大量路由规则配置
  - 复杂路由表配置
  - 双栈路由配置
- **断言要点**:
  - 路由配置时间合理
  - 系统资源使用优化
  - 大规模配置性能

### 7. 集成测试

#### 7.1 完整网络配置流程测试
**测试文件**: `TestNetworkConfiguration_CompleteFlow`
- **测试目标**: 验证完整的网络配置流程
- **测试场景**:
  - 从IP分配到接口配置的完整流程
  - 双栈网络配置端到端测试
  - 多IP双栈配置测试
- **断言要点**:
  - 完整流程无错误
  - 网络配置正确完成
  - 网络连通性正常

#### 7.2 不同场景兼容性测试
**测试文件**: `TestNetworkConfiguration_ScenarioCompatibility`
- **测试目标**: 验证不同场景的兼容性
- **测试场景**:
  - 单栈转双栈兼容性
  - 多IP双栈兼容性
  - 不同MTU配置兼容性
- **断言要点**:
  - 所有场景都能正确处理
  - 兼容性问题及时发现
  - 降级机制有效

## 覆盖率目标
- **目标覆盖率**: 85%+
- **关键路径覆盖率**: 95%+
- **错误处理覆盖率**: 90%+

## 测试数据准备
- Mock netlink操作
- Mock 网络命名空间
- Mock IP地址配置
- 测试用的网络配置数据

## 测试环境要求
- Go 1.19+
- Mock框架支持
- 网络命名空间权限
- netlink操作权限
- 网络接口操作权限 